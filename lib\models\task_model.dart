// import 'dart:developer' as developer;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
// tools
import '../tools/config.dart';
import '../tools/extensions.dart';
import '../tools/explicit_value.dart';

/// ⚠️ 这段注释不要删除：
/// ⚠️ 如果 Models 字段有改动
/// ⚠️ 请提醒我同步更新 Cloud Functions 和 Firebase Rules

/// 时间命名约定：
/// 1. 日期：Date (yyyy-MM-dd)
/// 2. 时刻：Clock (HH:mm:ss)
/// 3. 时间：Time (yyyy-MM-dd HH:mm:ss)

/// 三种截止状态：
/// 1. 无截止信息任务 (无截止日期和截止时刻)(dueDate == null && dueTime == null)
/// 2. 全天任务 (只有截止日期)(dueDate != null && dueTime == null)
/// 3. 精确时刻任务 (有截止日期和截止时刻)(dueTime != null && dueDate == null)
/// ================================
/// "无截止/有截止"为截止的根状态
/// "有截止"：有"全天任务"和"精确时刻任务"两种子状态
/// dueDate 和 dueTime 互斥，不能同时有值

/// 四种任务状态：
/// 1. 待办 (未结束未逾期)(isTodo)
/// 2. 逾期 (未结束已逾期)(isOverdue)
/// 3. 已完成 (已结束已完成)(isCompleted)
/// 4. 已丢弃 (已结束已丢弃)(isDropped) 
/// ================================
/// "未结束/已结束"为任务的根状态
/// "未结束"：有"未逾期"和"已逾期"两种子状态
/// "已结束"：有"已完成"和"已丢弃"两种子状态
/// "待办"和"逾期"状态互斥：待办任务不能逾期，逾期任务不能待办
/// "已完成"和"已丢弃"状态互斥：已完成任务不能丢弃，已丢弃任务不能完成

class TaskModel extends Equatable {
  // 系统设置
  final String id;
  final String userId;               // 关联用户 id
  final DateTime createTime;         // 创建时间
  final DateTime updateTime;         // 更新时间 (指 task 本身数据的更新)
  // 用户设置
  final String name;
  final String timezone;             // 时区 (只跟随 dueDate 或 dueTime 更新)
  final int? dueDate;                // 全天任务的日期: 20250715
  final DateTime? dueTime;           // 精确时刻任务的时间
  final String? projectId;
  final String? recurringId;         // 关联周期任务
  final bool isImportant;            // 是否重要
  final bool isUrgent;               // 是否紧急
  // 用户行为
  final DateTime? completeTime;      // 完成时间
  final DateTime? dropTime;          // 丢弃时间

  const TaskModel({
    required this.id,
    required this.userId,
    required this.createTime,
    required this.updateTime,

    required this.name,
    required this.timezone,
    this.dueDate,
    this.dueTime,
    this.projectId,
    this.recurringId,
    this.isImportant = false,
    this.isUrgent = false,

    this.completeTime,
    this.dropTime,
  });

  // ====== 公共状态 ======

  // --- 截止信息 ---

  // 判断是否无截止信息 (无截止日期和截止时刻)
  bool get hasNoDue => dueDate == null && dueTime == null;
  // 判断是否仅设置了截止日期 (只有截止日期)
  bool get hasDueDateOnly => dueDate != null;
  // 判断是否设置了截止日期和时间 (有截止日期和截止时刻)
  bool get hasDueDateClock => dueTime != null;

  // --- 任务状态 ---
  
  // 判断任务是否待办 (未结束未逾期)
  bool get isTodo {
    // 没有截止时间的任务不会逾期
    if (deadlineTime == null) return false;
    
    // 如果任务未完成，判断当前时间是否已经过了截止时间
    return !isCompleted && !isDropped && DateTime.now().isBefore(deadlineTime!);
  }
  // 判断任务是否逾期 (未结束已逾期)
  bool get isOverdue {
    // 没有截止时间的任务不会逾期
    if (deadlineTime == null) return false;
    
    // 如果任务未完成，判断当前时间是否已经过了截止时间
    return !isCompleted && !isDropped && !DateTime.now().isBefore(deadlineTime!);
  }
  // 判断任务是否已完成 (已结束已完成)
  bool get isCompleted => completeTime != null;
  // 判断任务是否已丢弃 (已结束已丢弃)
  bool get isDropped => dropTime != null;

  // --- 截止信息的单一权威来源 ---

  // 排序索引的单一权威来源 (综合了 dueDate 和 dueTime)
  // - 对于有 dueTime 的任务，直接返回 dueTime 的时间戳
  // - 对于只有 dueDate 的任务，返回 dueDate 当天 23:59:59 的时间戳
  // - 都没有则返回 null
  Timestamp? get deadlineIndex {
    if (dueTime != null) {
      return Timestamp.fromDate(dueTime!);
    }
    if (dueDate != null) {
      final date = DateTime.tryParse(dueDate.toString());
      if (date != null) {
        return Timestamp.fromDate(date.endOfDay);
      }
    }
    return null;
  }
  
  // 截止时间的单一权威来源 (综合了 dueDate 和 dueTime)
  // - 对于有 dueTime 的任务，直接返回 dueTime
  // - 对于只有 dueDate 的任务，返回 dueDate 当天 23:59:59
  // - 都没有则返回 null
  DateTime? get deadlineTime {
    if (dueTime != null) {
      return dueTime;
    }
    if (dueDate != null) {
      final date = DateTime.tryParse(dueDate.toString());
      return date?.endOfDay;
    }
    return null;
  }

  // ====== 工具方法 ======

  TaskModel copyWith({
    ExplicitValue<String>? id,
    ExplicitValue<String>? userId,
    ExplicitValue<DateTime>? createTime,
    ExplicitValue<DateTime>? updateTime,

    ExplicitValue<String>? name,
    ExplicitValue<String>? timezone,
    ExplicitValue<int?>? dueDate,
    ExplicitValue<DateTime?>? dueTime,
    ExplicitValue<String?>? projectId,
    ExplicitValue<String?>? recurringId,
    ExplicitValue<bool>? isImportant,
    ExplicitValue<bool>? isUrgent,

    ExplicitValue<DateTime?>? completeTime,
    ExplicitValue<DateTime?>? dropTime,
  }) {
    return TaskModel(
      id: id == null ? this.id : id.value,
      userId: userId == null ? this.userId : userId.value,
      createTime: createTime == null ? this.createTime : createTime.value,
      updateTime: updateTime == null ? this.updateTime : updateTime.value,

      name: name == null ? this.name : name.value,
      timezone: timezone == null ? this.timezone : timezone.value,
      dueDate: dueDate == null ? this.dueDate : dueDate.value,
      dueTime: dueTime == null ? this.dueTime : dueTime.value,
      projectId: projectId == null ? this.projectId : projectId.value,
      recurringId: recurringId == null ? this.recurringId : recurringId.value,
      isImportant: isImportant == null ? this.isImportant : isImportant.value,
      isUrgent: isUrgent == null ? this.isUrgent : isUrgent.value,

      completeTime: completeTime == null ? this.completeTime : completeTime.value,
      dropTime: dropTime == null ? this.dropTime : dropTime.value,
    );
  }

  factory TaskModel.fromDatabase(Map<String, dynamic> json) {
    int? parsedDueDate;
    
    // 兼容性转换：
    // 1. 优先使用 int 型的 `dueDate`
    if (json['dueDate'] is int) {
      parsedDueDate = json['dueDate'];
    } 
    // 2. 其次兼容 String 型的 `dueDate`
    else if (json['dueDate'] is String) {
      parsedDueDate = int.tryParse((json['dueDate'] as String).replaceAll('-', ''));
    } 
    // 3. 最后兼容旧的 `dueDateIndex`
    else if (json.containsKey('dueDateIndex')) {
      parsedDueDate = json['dueDateIndex'];
    }

    return TaskModel(
      id: json['id'] as String? ?? '',
      userId: json['userId'] as String? ?? '',
      createTime: (json['createTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updateTime: (json['updateTime'] as Timestamp?)?.toDate() ?? DateTime.now(),

      name: json['name'] as String? ?? 'Unnamed Task',
      timezone: json['timezone'] as String? ?? Config.app.defaultTimezone,
      dueDate: parsedDueDate,
      dueTime: (json['dueTime'] as Timestamp?)?.toDate(),
      projectId: json['projectId'] as String?,
      recurringId: json['recurringId'] as String?,
      isImportant: json['isImportant'] as bool? ?? false,
      isUrgent: json['isUrgent'] as bool? ?? false,

      completeTime: (json['completeTime'] as Timestamp?)?.toDate(),
      dropTime: (json['dropTime'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toDatabase() {
    return {
      'name': name,
      'timezone': timezone,
      'dueDate': dueDate,
      'dueTime': dueTime != null ? Timestamp.fromDate(dueTime!) : null,
      'projectId': projectId,
      'recurringId': recurringId,
      'isImportant': isImportant,
      'isUrgent': isUrgent,
      'completeTime': completeTime != null ? Timestamp.fromDate(completeTime!) : null,
      'dropTime': dropTime != null ? Timestamp.fromDate(dropTime!) : null,
    };
  }

  // Equatable 需要比较的属性
  @override
  List<Object?> get props => [
    id,
    userId,
    createTime,
    updateTime,
    name,
    timezone,
    dueDate,
    dueTime,
    projectId,
    recurringId,
    isImportant,
    isUrgent,
    completeTime,
    dropTime,
  ];
}
