// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TaskPriority extends StatelessWidget {
  final bool isUrgent;
  final bool isImportant;
  final Function(bool, bool) onSelected;

  const TaskPriority({
    super.key,
    required this.isUrgent,
    required this.isImportant,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final ColorScheme colorScheme = Theme.of(context).colorScheme;

    return Row(
      children: [
        Expanded(
          child: _buildPriorityToggle(
            context,
            label: l10n.taskPriorityWidget_important,
            value: isImportant,
            onChanged: (value) {
              // 增加震动反馈
              HapticFeedback.lightImpact();
              onSelected(value, isUrgent);
            },
            color: colorScheme.primary,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: _buildPriorityToggle(
            context,
            label: l10n.taskPriorityWidget_urgent,
            value: isUrgent,
            onChanged: (value) {
              // 增加震动反馈
              HapticFeedback.lightImpact();
              onSelected(isImportant, value);
            },
            color: colorScheme.error,
          ),
        ),
      ],
    );
  }

  Widget _buildPriorityToggle(
    BuildContext context, {
    required String label,
    required bool value,
    required Function(bool) onChanged,
    required Color color,
  }) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    // 调整内边距使开关看起来更紧凑
    return Container(
      padding: const EdgeInsets.only(left: 12.0),
      constraints: const BoxConstraints(maxHeight: 45.0),
      decoration: BoxDecoration(
        border: Border.all(
          color: value ? color : colorScheme.outline,
          width: 1.0,
        ),
        borderRadius: BorderRadius.circular(8.0),
        color: value ? color.withValues(alpha: 0.1) : null,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14.0,
              color: value ? color : colorScheme.onSurface,
            ),
          ),
          Transform.scale(
            scale: 0.75,
            child: Switch(
              value: value,
              onChanged: onChanged,
              activeColor: color,
            ),
          ),
        ],
      ),
    );
  }
}
