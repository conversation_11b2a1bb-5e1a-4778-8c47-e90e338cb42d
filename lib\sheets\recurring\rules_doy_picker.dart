// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/recurring_model.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

typedef DoyCallback = void Function(List<MonthDay>? days);

// 年份日期选择器 (Days of Year) - 点击占位符弹出选择
class RulesDoyPicker extends StatelessWidget {
  final List<MonthDay>? currentDaysOfYear; // 当前选中的日期
  final DoyCallback onDaysSelected;
  final List<int> _daysInMonth = const [0, 31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

  const RulesDoyPicker({
    super.key,
    this.currentDaysOfYear,
    required this.onDaysSelected,
  });

  // 将 MonthDay 格式化为 "M月d日" 字符串
  String _formatMonthDay(MonthDay md, String locale) {
    // 使用 DateFormat 来格式化，更健壮
    try {
      // 创建一个日期对象（年份不重要，这里用 2024）
      final date = DateTime(2024, md.month, md.day);
      // 使用本地化的格式
      return DateFormat.MMMd(locale).format(date);
    } catch (e) {
      return '${md.month}/${md.day}'; // 备用格式
    }
  }

  // 格式化显示的选中日期
  String _formatSelectedDays(AppLocalizations l10n) {
    if (currentDaysOfYear == null || currentDaysOfYear!.isEmpty) {
      return l10n.rulesDoyPicker_placeholder; // 占位符
    }
    // 先复制再排序
    final sortedDays = List<MonthDay>.from(currentDaysOfYear!)..sort((a, b) => a.compareTo(b));
    return sortedDays.map((md) => _formatMonthDay(md, l10n.localeName)).join(', ');
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: EdgeInsets.only(bottom: 12.0),
          child: Text(
            l10n.common_specificDate,
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: colorScheme.onSurfaceVariant),
          ),
        ),
        // 可点击的选择区域
        InkWell(
          onTap: () => _showRulesDoySheet(context),
          child: Container(
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    _formatSelectedDays(l10n),
                    style: TextStyle(
                      fontSize: 16,
                      color: (currentDaysOfYear == null || currentDaysOfYear!.isEmpty)
                          ? colorScheme.onSurfaceVariant
                          : colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center, // 文本居中
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Icon(LucideIcons.chevronsUpDown, size: 18, color: colorScheme.onSurfaceVariant),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 显示年份日期选择底部弹窗
  void _showRulesDoySheet(BuildContext context) {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;
    // 使用 MonthDay 列表
    List<MonthDay> tempSelectedDays = List<MonthDay>.from(currentDaysOfYear ?? []);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // 允许弹窗占据更多高度
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            void toggleDayInModal(int month, int day) {
              final monthDay = MonthDay(month: month, day: day);
              setModalState(() {
                // 使用 MonthDay 的比较
                if (tempSelectedDays.contains(monthDay)) {
                  tempSelectedDays.remove(monthDay);
                } else {
                  if (tempSelectedDays.length < 5) {
                    tempSelectedDays.add(monthDay);
                    // 使用 MonthDay 的排序
                    tempSelectedDays.sort((a, b) => a.compareTo(b));
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(l10n.common_maxSelection(5)), duration: Duration(seconds: 1)),
                    );
                  }
                }
              });
            }

            return BaseBottomSheet(
              title: l10n.rulesDoyPicker_title,
              onConfirm: () {
                // 确认选择，调用回调并关闭弹窗
                onDaysSelected(tempSelectedDays.isEmpty ? null : tempSelectedDays);
                Navigator.pop(context);
              },
              child: SizedBox(
                height: MediaQuery.of(context).size.height * 0.7,
                child: ListView.builder(
                  itemCount: 12,
                  itemBuilder: (context, monthIndex) {
                    final month = monthIndex + 1;
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            DateFormat.MMMM(l10n.localeName).format(DateTime(2024, month)),
                            style: TextStyle(fontWeight: FontWeight.w500, color: colorScheme.onSurface),
                          ),
                          const SizedBox(height: 8),
                          GridView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 7,
                              childAspectRatio: 1.0,
                              crossAxisSpacing: 8,
                              mainAxisSpacing: 8,
                            ),
                            itemCount: _daysInMonth[month],
                            itemBuilder: (context, dayIndex) {
                              final day = dayIndex + 1;
                              final monthDay = MonthDay(month: month, day: day);
                              final isSelected = tempSelectedDays.contains(monthDay);
                              return ChoiceChip(
                                label: Center(child: Text(day.toString())),
                                selected: isSelected,
                                onSelected: (selected) {
                                  // 增加震动反馈
                                  HapticFeedback.lightImpact();
                                  toggleDayInModal(month, day);
                                },
                                selectedColor: colorScheme.primary.withValues(alpha: 0.2),
                                showCheckmark: false,
                                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                labelPadding: EdgeInsets.zero,
                                padding: EdgeInsets.zero,
                                side: BorderSide(
                                  color: isSelected ? Colors.transparent : colorScheme.outline,
                                  width: 1.0,
                                ),
                                labelStyle: TextStyle(
                                  color: isSelected ? colorScheme.primary : colorScheme.onSurface,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            );
          },
        );
      },
    );
  }
}
