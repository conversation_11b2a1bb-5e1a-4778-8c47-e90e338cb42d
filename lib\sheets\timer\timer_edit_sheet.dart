// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
// models
import '../../models/timer_model.dart';
// states
import '../../states/timer_state.dart';
// tools
import '../../tools/config.dart';
import '../../tools/extensions.dart';
import '../../tools/explicit_value.dart';
import '../../tools/app_exception.dart';
import '../../tools/app_exception_mapper.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
import '../../widgets/common/base_alert_dialog.dart';
import '../../widgets/common/app_exception_snackbar.dart';
// sheets
import './timer_date_sheet.dart';
import './timer_clock_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TimerEditSheet extends StatefulWidget {
  final TimerModel timer;

  const TimerEditSheet({
    super.key,
    required this.timer,
  });

  static Future<void> show(BuildContext context, {required TimerModel timer}) {
    return showModalBottomSheet<void>(
      context: context,
      // 使弹窗能响应键盘，自动调整大小
      isScrollControlled: true,
      builder: (context) {
        return TimerEditSheet(timer: timer);
      },
    );
  }

  @override
  State<TimerEditSheet> createState() => _TimerEditSheetState();
}

class _TimerEditSheetState extends State<TimerEditSheet> {
  late DateTime _startTime;
  late DateTime _endTime;
  late int _durationSeconds;
  
  bool _isDeleting = false;

  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd');
  final DateFormat _timeFormat = DateFormat('HH:mm');

  @override
  void initState() {
    super.initState();
    _startTime = widget.timer.startTime;
    // 根据用户说明，编辑页面不会处理正在运行的计时器，所以直接使用 endTime
    _endTime = widget.timer.endTime;
    _updateDuration();
  }

  void _updateDuration() {
    // 确保结束时间不早于开始时间
    if (_endTime.isBefore(_startTime)) {
      _endTime = _startTime; // 或者显示错误提示
    }
    setState(() {
      _durationSeconds = _endTime.difference(_startTime).inSeconds;
      // 如果 duration 为负（理论上不应该发生，但以防万一），则设为0
      if (_durationSeconds < 0) _durationSeconds = 0;
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return BaseBottomSheet(
      title: l10n.timerEditSheet_title,
      onConfirm: _isDeleting ? () {} : _saveTimer,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 持续时间显示
            Center(
              child: Text(
                _durationSeconds.format(),
                style: TextStyle(
                  fontSize: 36,
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: 24),

            // 使用 Table 实现标签对齐
            Table(
              columnWidths: const <int, TableColumnWidth>{
                0: IntrinsicColumnWidth(), // 标签列，宽度自适应内容
                1: FlexColumnWidth(),      // 按钮列，占据剩余空间
              },
              defaultVerticalAlignment: TableCellVerticalAlignment.middle,
              children: [
                // 开始时间行
                TableRow(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: Text(
                        l10n.timerEditSheet_startLabel,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                    _buildPickerControls(
                      date: _startTime,
                      onDateTap: _isDeleting ? null : _selectStartDate,
                      onTimeTap: _isDeleting ? null : _selectStartTime,
                    ),
                  ],
                ),
                // 用于垂直间距的空行
                const TableRow(
                  children: [SizedBox(height: 16), SizedBox(height: 16)],
                ),
                // 结束时间行
                TableRow(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: Text(
                        l10n.timerEditSheet_endLabel,
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                    _buildPickerControls(
                      date: _endTime,
                      onDateTap: _isDeleting ? null : _selectEndDate,
                      onTimeTap: _isDeleting ? null : _selectEndTime,
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 60), // 增加与删除按钮的间距

            // 删除按钮
            TextButton(
              onPressed: _isDeleting ? null : _confirmDeleteTimer,
              style: TextButton.styleFrom(
                foregroundColor: colorScheme.error,
                disabledForegroundColor: colorScheme.error,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center, // 居中对齐
                children: [
                  _isDeleting
                    ? SizedBox(
                        width: 14,
                        height: 14,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          color: colorScheme.error,
                        ),
                      )
                    : const Icon(LucideIcons.trash2, size: 16), // 添加图标
                  const SizedBox(width: 8), // 图标和文本间距
                  Text(l10n.timerEditSheet_deleteTimer),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建日期和时间选择器按钮组
  Widget _buildPickerControls({
    required DateTime date,
    required VoidCallback? onDateTap,
    required VoidCallback? onTimeTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        // 时间按钮
        Expanded(
          flex: 2,
          child: InkWell(
            onTap: onTimeTap == null
                ? null
                : () {
                    // 增加震动反馈
                    HapticFeedback.lightImpact();
                    // 调用时间选择回调
                    onTimeTap();
                  },
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: colorScheme.outline),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _timeFormat.format(date), // 显示时间
                    style: const TextStyle(fontSize: 16),
                  ),
                  Icon(LucideIcons.clock, size: 18, color: colorScheme.onSurfaceVariant),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        // 日期按钮
        Expanded(
          flex: 3, // 日期占更多空间
          child: InkWell(
            onTap: onDateTap == null
                ? null
                : () {
                    // 增加震动反馈
                    HapticFeedback.lightImpact();
                    // 调用日期选择回调
                    onDateTap();
                  },
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                border: Border.all(color: colorScheme.outline),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _dateFormat.format(date), // 显示日期
                    style: const TextStyle(fontSize: 16),
                  ),
                  Icon(LucideIcons.calendarDays, size: 18, color: colorScheme.onSurfaceVariant),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // --- 选择器逻辑 (调用导入的函数) ---
  void _selectStartDate() async {
    final DateTime? pickedDate = await showTimerDateSheet(
      context: context,
      initialDate: _startTime,
      firstDate: Config.app.calendarFirstDay,
      lastDate: Config.app.calendarLastDay,
    );
    if (pickedDate != null) {
      setState(() {
        _startTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          _startTime.hour,
          _startTime.minute,
          _startTime.second, // 保留秒
        );
        _updateDuration();
      });
    }
  }

  void _selectStartTime() async {
    final TimeOfDay? pickedTime = await showTimerClockSheet(
      context: context,
      initialTime: TimeOfDay.fromDateTime(_startTime),
    );
    if (pickedTime != null) {
      setState(() {
        _startTime = DateTime(
          _startTime.year,
          _startTime.month,
          _startTime.day,
          pickedTime.hour,
          pickedTime.minute,
          _startTime.second, // 保留原始秒数
        );
        _updateDuration();
      });
    }
  }

  void _selectEndDate() async {
    final DateTime? pickedDate = await showTimerDateSheet(
      context: context,
      initialDate: _endTime,
      firstDate: _startTime, // 结束日期不能早于开始日期
      lastDate: Config.app.calendarLastDay,
    );
    if (pickedDate != null) {
      setState(() {
        _endTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          _endTime.hour,
          _endTime.minute,
          _endTime.second, // 保留秒
        );
        _updateDuration();
      });
    }
  }

  void _selectEndTime() async {
    final l10n = AppLocalizations.of(context)!;

    final TimeOfDay? pickedTime = await showTimerClockSheet(
      context: context,
      initialTime: TimeOfDay.fromDateTime(_endTime),
    );

    if (pickedTime != null) {
      DateTime potentialEndTime = DateTime(
        _endTime.year,
        _endTime.month,
        _endTime.day,
        pickedTime.hour,
        pickedTime.minute,
        _endTime.second, // 保留原始秒数
      );

      if (potentialEndTime.isBefore(_startTime) && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(l10n.timerEditSheet_errorEndClockBeforeStartClock)),
        );
        return;
      }

      setState(() {
        _endTime = potentialEndTime;
        _updateDuration();
      });
    }
  }

  // --- 保存逻辑 ---
  void _saveTimer() async {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final timerNotifier = Provider.of<TimerNotifier>(context, listen: false);
    final navigator = Navigator.of(context);

    try {
      final String timezone = await FlutterTimezone.getLocalTimezone();

      // 1. 创建更新后的 TimerModel
      final updatedTimer = widget.timer.copyWith(
        timezone: ExplicitValue(timezone),
        startTime: ExplicitValue(_startTime),
        endTime: ExplicitValue(_endTime),
        isRunning: ExplicitValue(false), // 确保 isRunning 为 false
      );

      // 2. 调用 timerNotifier 的 updateTimer 方法
      if (updatedTimer != widget.timer) {
        await timerNotifier.updateTimer(updatedTimer);
      }

      // 3. 返回上一页 (现在是关闭 BottomSheet)
      if (mounted) navigator.pop();

    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.updateTimer,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    }
  }

  // --- 删除逻辑 ---
  void _confirmDeleteTimer() {
    // 增加震动反馈
    HapticFeedback.lightImpact();
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) { // 使用独立的 dialogContext
        return BaseAlertDialog(
          title: l10n.timerEditSheet_confirmDeleteTitle,
          content: l10n.timerEditSheet_confirmDeleteContent,
          actions: <Widget>[
            TextButton(
              child: Text(l10n.common_cancel),
              onPressed: () => Navigator.of(dialogContext).pop(false),
            ),
            TextButton(
              style: TextButton.styleFrom(foregroundColor: colorScheme.error),
              child: Text(l10n.common_confirmDelete),
              onPressed: () => Navigator.of(dialogContext).pop(true),
            ),
          ],
        );
      },
    ).then((confirmed) {
      if (confirmed == true) {
        _handleDeleteTimer();
      }
    });
  }

  Future<void> _handleDeleteTimer() async {
    if (!mounted) return;
    setState(() => _isDeleting = true);

    final timerState = Provider.of<TimerNotifier>(context, listen: false);
    final navigator = Navigator.of(context);

    try {
      await timerState.deleteTimer(widget.timer.id);
      if (mounted) navigator.pop();
    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.deleteTimer,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    } finally {
      if (mounted) {
        setState(() => _isDeleting = false);
      }
    }
  }
}
