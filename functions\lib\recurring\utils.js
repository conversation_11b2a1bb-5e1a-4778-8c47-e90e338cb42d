import { Timestamp } from 'firebase-admin/firestore';
import * as logger from 'firebase-functions/logger';
import rrule from 'rrule';
import { db, DATABASE } from '../tools/config.js';
import TimeUtils from '../tools/time.js';
// RRule 库本身不支持 ES Module 的默认导出，因此需要先整体导入，再解构出 RRule
const { RRule } = rrule;
/**
 * 根据规则在给定日期范围内计算发生日期 (时区感知)
 * 使用 'rrule' 库实现。
 *
 * @param {RecurringRule} rule - Firestore 中的周期规则对象 (RecurringRule)
 * @param {Date} rangeStartDate - 范围的开始日期 (包含)
 * @param {Date} rangeEndDate - 范围的结束日期 (包含)
 * @param {string} timezone - IANA 时区 ID
 * @returns {Date[]} 包含发生日期的 Date 对象数组
 */
function calculateOccurrencesInRange(rule, rangeStartDate, rangeEndDate, timezone) {
    logger.debug('正在使用 rrule 计算发生日期 (时区感知)', { rule, rangeStartDate, rangeEndDate, timezone });
    if (!rule || !rule.startTime || typeof rule.startTime.toDate !== 'function') {
        logger.error("无效的规则对象或缺失/无效的 startTime 属性", { rule });
        return [];
    }
    const options = {
        // 确保每周的起始日为星期一 (RRule.MO)
        wkst: RRule.MO,
    };
    const freqMap = TimeUtils.getFrequencyMap();
    // 如果 recurrence 是 'yearly' (每年) 并且指定了 daysOfYear (具体日期)
    // 我们将其视为每日规则，然后在计算后手动过滤。这是因为 rrule 不直接支持"每年在这些特定日期发生"的模式
    if (rule.frequency === 'yearly' && rule.daysOfYear && rule.daysOfYear.length > 0) {
        options.freq = RRule.DAILY;
    }
    else {
        options.freq = freqMap[rule.frequency];
    }
    if (options.freq === undefined) {
        logger.error('规则中的频率 (frequency) 无效', { frequency: rule.frequency });
        return [];
    }
    options.interval = rule.interval || 1;
    try {
        options.dtstart = TimeUtils.createRRuleDtstart(timezone, rule.startTime.toDate());
    }
    catch (e) {
        logger.error("解析 rule.startTime 时出错", { startTime: rule.startTime, error: e.message });
        return [];
    }
    if (rule.endTime && typeof rule.endTime.toDate === 'function') {
        try {
            options.until = TimeUtils.createRRuleUntil(timezone, rule.endTime.toDate());
        }
        catch (e) {
            logger.error('解析 rule.endTime 时出错', { endTime: rule.endTime, error: e.message });
        }
    }
    else if (rule.repeatCount && typeof rule.repeatCount === 'number' && rule.repeatCount > 0) {
        // `rule.repeatCount` 是重复次数
        // `rrule.count` 是总共发生次数
        // 当一个周期内有多个发生日时（例如，每周的周一和周三），我们需要进行换算
        // 这是每年特定日期的特殊情况，我们通过"生成每日再过滤"的方式来模拟
        // 在这种模式下，不能使用 `count`，因为它会错误地限制生成的天数
        // 正确的做法是根据重复的年数计算出一个明确的 `until` 结束日期
        if (rule.frequency === 'yearly' && rule.daysOfYear && rule.daysOfYear.length > 0) {
            options.until = TimeUtils.calculateYearlyRuleUntil(timezone, rule.startTime.toDate(), rule.repeatCount);
            logger.debug(`已将 "每年特定日期重复 ${rule.repeatCount} 年" 的规则转换为截止日期: ${options.until.toISOString()}`);
        }
        else {
            const occurrencesPerCycle = TimeUtils.calculateOccurrencesPerCycle(rule.frequency, rule.daysOfWeek, rule.daysOfMonth);
            options.count = rule.repeatCount * occurrencesPerCycle;
        }
    }
    if (rule.frequency === 'weekly') {
        const rruleDaysOfWeek = TimeUtils.mapDaysOfWeekToRRule(rule.daysOfWeek);
        if (rruleDaysOfWeek && rruleDaysOfWeek.length > 0) {
            options.byweekday = rruleDaysOfWeek;
        }
    }
    if (rule.frequency === 'monthly' && rule.daysOfMonth && rule.daysOfMonth.length > 0) {
        options.bymonthday = rule.daysOfMonth;
    }
    try {
        const rruleInstance = new RRule(options);
        logger.debug('RRule 选项:', { ...options, dtstart: options.dtstart?.toISOString(), until: options.until?.toISOString() });
        const { start: normalizedRangeStart, end: normalizedRangeEnd } = TimeUtils.createRRuleRange(timezone, rangeStartDate, rangeEndDate);
        let occurrences;
        // `rrule` 的 `between()` 方法被设计用来从一个可能无限的规则中获取一个时间段内的实例
        // 正因如此，它会忽略规则本身定义的 `count` (重复次数) 和 `until` (结束日期) 属性
        // 这导致了我们定义的有限规则（例如"重复5次"）被错误地当作无限规则处理，其产生的实例仅仅受 `between()` 的时间窗口限制
        //
        // 正确的做法是：
        // 1. 对于有限规则 (即设置了 `count` 或 `until` 的规则)，我们应该使用 `.all()` 方法获取其所有将发生的实例
        //    然后，我们再手动地从这个完整的列表中筛选出位于我们任务生成窗口内的实例
        // 2. 对于无限规则, 继续使用 `.between()` 方法来安全地获取一个时间切片
        if (options.count || options.until) {
            const allOccurrences = rruleInstance.all();
            // 手动筛选出在生成窗口内的日期
            occurrences = allOccurrences.filter(d => d >= normalizedRangeStart && d <= normalizedRangeEnd);
        }
        else {
            // 对于无限规则，原始逻辑是正确的
            occurrences = rruleInstance.between(normalizedRangeStart, normalizedRangeEnd, true);
        }
        if (rule.frequency === 'yearly' && rule.daysOfYear && rule.daysOfYear.length > 0) {
            const validDaysOfYear = rule.daysOfYear;
            occurrences = occurrences.filter(date => {
                const { month, day } = TimeUtils.toMonthDayInTimezone(timezone, date);
                return validDaysOfYear.some(md => md.month === month && md.day === day);
            });
        }
        logger.debug(`RRule 计算得到 ${occurrences.length} 个发生日期`, { rule: rule.frequency, rangeStartDate, rangeEndDate, timezone });
        return occurrences;
    }
    catch (e) {
        logger.error('创建 RRule 或获取发生日期时出错', { options, error: e.message });
        return [];
    }
}
/**
 * 根据周期任务为特定日期生成任务数据 (TaskModel) (时区感知)
 * @param {string} userId - 用户 ID
 * @param {string} recurringId - 周期任务 ID
 * @param {RecurringModel} recurring - Firestore 中的 RecurringModel 数据
 * @param {Date} occurrenceDate - 任务实例的具体发生日期 (通常是用户时区下的零点)
 * @returns {TaskModel | null} 准备写入 Firestore 的任务数据，如果出错则返回 null
 */
function createTaskDataFromTemplate(userId, recurringId, recurring, occurrenceDate) {
    const template = recurring.template;
    if (!template) {
        logger.warn('模板数据 (template) 缺失', { userId, recurringId });
        return null;
    }
    const timezone = recurring.timezone ?? DATABASE.DEFAULT_TIMEZONE;
    const isAllDayTask = TimeUtils.isAllDayTask(template);
    // 初始化新的任务时间字段
    let dueTime = null;
    let dueDate = null;
    if (!isAllDayTask) {
        // === 精确时刻任务 ===
        dueTime = TimeUtils.combineDateClock(timezone, occurrenceDate, template.dueClock.toDate());
        dueDate = null;
    }
    else {
        // === 全天任务 ===
        dueDate = TimeUtils.toNumberedDueDate(timezone, occurrenceDate);
        dueTime = null;
    }
    const taskData = {
        userId: userId,
        createTime: Timestamp.now(),
        updateTime: Timestamp.now(),
        name: template.name || 'Unnamed Task',
        timezone: timezone,
        dueDate: dueDate,
        dueTime: dueTime,
        projectId: template.projectId ? template.projectId : null,
        recurringId: recurringId,
        isImportant: template.isImportant || false,
        isUrgent: template.isUrgent || false,
        completeTime: null,
        dropTime: null,
    };
    return taskData;
}
/**
 * 使用批量写入为给定周期任务在指定日期范围内生成任务实例 (时区感知)
 * @param {string} userId - 用户 ID
 * @param {string} recurringId - 周期任务 ID
 * @param {RecurringModel} recurring - Firestore 中的 RecurringModel 数据 (包含 timezone)
 * @param {Date} startDate - 开始日期 (包含)
 * @param {Date} endDate - 结束日期 (包含)
 * @returns {Promise<void>}
 */
async function generateTasksForPeriod(userId, recurringId, recurring, startDate, endDate) {
    if (!recurring || !recurring.rule || !recurring.template) {
        logger.error('无法生成任务：没有周期任务数据或数据缺失', { userId, recurringId });
        return;
    }
    const timezone = recurring.timezone ?? DATABASE.DEFAULT_TIMEZONE;
    const tasksColRef = db.collection(DATABASE.USERS_COLLECTION).doc(userId).collection(DATABASE.TASKS_COLLECTION);
    // 查询已完成或已丢弃的任务
    const [completedTasksSnapshot, droppedTasksSnapshot] = await Promise.all([
        tasksColRef.where('recurringId', '==', recurringId).where('completeTime', '!=', null).get(),
        tasksColRef.where('recurringId', '==', recurringId).where('dropTime', '!=', null).get()
    ]);
    const preservedTaskDates = new Set();
    const processSnapshot = (snapshot) => {
        snapshot.forEach(doc => {
            const task = doc.data();
            let taskDateStr = null;
            if (task.dueDate) {
                taskDateStr = TimeUtils.formatDueDateToString(task.dueDate);
            }
            else if (task.dueTime) {
                taskDateStr = TimeUtils.toDateStringInTimezone(timezone, task.dueTime.toDate());
            }
            if (taskDateStr) {
                preservedTaskDates.add(taskDateStr);
            }
        });
    };
    processSnapshot(completedTasksSnapshot);
    processSnapshot(droppedTasksSnapshot);
    const occurrences = calculateOccurrencesInRange(recurring.rule, startDate, endDate, timezone);
    if (occurrences.length === 0) {
        logger.info('在指定期间内未找到需要生成的任务日期', { userId, recurringId, startDate, endDate, timezone });
        return;
    }
    let batch = db.batch();
    let batchCount = 0;
    let totalGenerated = 0;
    for (const occurrenceDate of occurrences) {
        const occurrenceDateStr = TimeUtils.toDateStringInTimezone(timezone, occurrenceDate);
        if (preservedTaskDates.has(occurrenceDateStr)) {
            logger.debug(`跳过为日期 ${occurrenceDateStr} 创建任务，因为该日期已存在一个已完成/已丢弃的任务`, { userId, recurringId });
            continue;
        }
        const taskData = createTaskDataFromTemplate(userId, recurringId, recurring, occurrenceDate);
        if (taskData) {
            const newTaskRef = tasksColRef.doc();
            batch.set(newTaskRef, taskData);
            batchCount++;
            totalGenerated++;
            if (batchCount >= DATABASE.BATCH_LIMIT) {
                logger.warn(`任务生成接近批次限制 (${batchCount})，正在提交部分批次`, { userId, recurringId });
                await batch.commit();
                batch = db.batch();
                batchCount = 0;
            }
        }
    }
    if (batchCount > 0) {
        try {
            await batch.commit();
            logger.info(`成功为周期 ${startDate.toISOString().split("T")[0]} 到 ${endDate.toISOString().split("T")[0]} 生成了 ${totalGenerated} 个任务`, { userId, recurringId });
        }
        catch (error) {
            logger.error("提交最终批次任务生成时出错", { userId, recurringId, error: error.message });
        }
    }
    else if (totalGenerated > 0) {
        logger.info(`成功为周期 ${startDate.toISOString().split("T")[0]} 到 ${endDate.toISOString().split("T")[0]} 生成了 ${totalGenerated} 个任务 (最后一次提交是满批次或无剩余)`, { userId, recurringId });
    }
    else {
        logger.info("处理完所有发生日期后，批次为空或未生成有效任务数据，未提交任何新任务", { userId, recurringId });
    }
}
/**
 * 使用批量写入删除与周期任务关联的未来未完成任务 (时区感知)
 * 未完成状态定义为任务没有完成时间 (completeTime) 和丢弃时间 (dropTime)
 * 未来定义为任务的截止时间 (dueTime) 是 "今天" 或 "今天" 之后 (根据用户时区)
 * @param {string} userId - 用户 ID
 * @param {string} recurringId - 周期任务 ID
 * @param {string} timezone - IANA 时区标识符
 * @returns {Promise<void>}
 */
async function deleteFutureIncompleteTasks(userId, recurringId, timezone) {
    const tasksColRef = db.collection(DATABASE.USERS_COLLECTION).doc(userId).collection(DATABASE.TASKS_COLLECTION);
    const startOfToday = TimeUtils.toStartOfDay(timezone, new Date());
    const startOfTodayDateIndex = TimeUtils.toNumberedDueDate(timezone, new Date());
    // 分别查询两种可能的未来任务
    const [timeTasksSnapshot, dateTasksSnapshot] = await Promise.all([
        tasksColRef.where('recurringId', '==', recurringId).where('dueTime', '>=', Timestamp.fromDate(startOfToday)).get(),
        tasksColRef.where('recurringId', '==', recurringId).where('dueDate', '>=', startOfTodayDateIndex).get(),
    ]);
    if (timeTasksSnapshot.empty && dateTasksSnapshot.empty) {
        logger.info('未找到需要删除的未来任务', { userId, recurringId, timezone });
        return;
    }
    let batch = db.batch();
    let deleteCount = 0;
    let totalMatchingDocs = timeTasksSnapshot.size + dateTasksSnapshot.size;
    let actualDeletedCount = 0;
    const allDocs = [...timeTasksSnapshot.docs, ...dateTasksSnapshot.docs];
    for (const doc of allDocs) {
        const taskData = doc.data();
        if (taskData.completeTime === null && taskData.dropTime === null) {
            batch.delete(doc.ref);
            deleteCount++;
            actualDeletedCount++;
            if (deleteCount >= DATABASE.BATCH_LIMIT) {
                logger.warn(`批量删除任务接近限制 (${deleteCount})，正在提交部分批次`, { userId, recurringId });
                await batch.commit();
                batch = db.batch();
                deleteCount = 0;
            }
        }
    }
    if (deleteCount > 0) {
        try {
            await batch.commit();
            logger.info(`成功删除了 ${actualDeletedCount} 个未来未完成任务 (共找到 ${totalMatchingDocs} 个匹配任务)`, { userId, recurringId });
        }
        catch (error) {
            logger.error("提交最终批次任务删除时出错", { userId, recurringId, error: error.message });
        }
    }
    else if (actualDeletedCount > 0) {
        logger.info(`成功删除了 ${actualDeletedCount} 个未来未完成任务 (共找到 ${totalMatchingDocs} 个匹配任务，最后一次提交是满批次)`, { userId, recurringId });
    }
    else {
        logger.info(`根据完成状态过滤后，未找到需要删除的未来 *未完成* 任务 (共找到 ${totalMatchingDocs} 个匹配任务)`, { userId, recurringId });
    }
}
export { calculateOccurrencesInRange, createTaskDataFromTemplate, generateTasksForPeriod, deleteFutureIncompleteTasks, };
//# sourceMappingURL=utils.js.map