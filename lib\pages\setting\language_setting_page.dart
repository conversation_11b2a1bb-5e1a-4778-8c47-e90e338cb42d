// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
// states
import '../../states/setting_state.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class LanguageSettingPage extends StatelessWidget {
  const LanguageSettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final settingState = context.watch<SettingState>();
    final currentLocale = settingState.locale;
    final String currentLanguageCode = currentLocale?.languageCode ?? 'system';

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.languageSettingPage_title),
      ),
      body: ListView(
        children: [
          _buildLanguageOption(
            context: context,
            title: l10n.languageSettingPage_system,
            languageCode: 'system',
            currentLanguageCode: currentLanguageCode,
          ),
          _buildLanguageOption(
            context: context,
            title: 'English',
            languageCode: 'en',
            currentLanguageCode: currentLanguageCode,
          ),
          _buildLanguageOption(
            context: context,
            title: 'Español (Spanish)',
            languageCode: 'es',
            currentLanguageCode: currentLanguageCode,
          ),
          _buildLanguageOption(
            context: context,
            title: 'Deutsch (German)',
            languageCode: 'de',
            currentLanguageCode: currentLanguageCode,
          ),
          _buildLanguageOption(
            context: context,
            title: 'Français (French)',
            languageCode: 'fr',
            currentLanguageCode: currentLanguageCode,
          ),
          _buildLanguageOption(
            context: context,
            title: '日本語 (Japanese)',
            languageCode: 'ja',
            currentLanguageCode: currentLanguageCode,
          ),
          _buildLanguageOption(
            context: context,
            title: '中文 (Chinese)',
            languageCode: 'zh',
            currentLanguageCode: currentLanguageCode,
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageOption({
    required BuildContext context,
    required String title,
    required String languageCode,
    required String currentLanguageCode,
  }) {
    return RadioListTile<String>(
      title: Text(title),
      value: languageCode,
      groupValue: currentLanguageCode,
      dense: true,
      onChanged: (String? value) {
        if (value != null) {
          HapticFeedback.lightImpact();
          context.read<SettingState>().setLocale(value);
        }
      },
    );
  }
}
