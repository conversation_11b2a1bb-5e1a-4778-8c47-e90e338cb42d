// import 'dart:developer' as developer;
import 'package:flutter/material.dart';

/// 自定义底部滑动路由
class BottomSlideRoute<T> extends MaterialPageRoute<T> {
  BottomSlideRoute({
    required Widget page,
    super.settings,
  }) : super(
    builder: (context) => page,
  );

  @override
  Widget buildTransitions(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(0, 1),
        end: Offset.zero,
      ).animate(animation),
      child: child,
    );
  }
}
