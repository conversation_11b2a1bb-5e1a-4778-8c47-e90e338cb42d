// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// states
import '../states/project_state.dart';
import '../states/user_state.dart';
// services
import '../services/permission_service.dart';
// tools
import '../tools/config.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/project/project_card.dart';
// sheets
import '../sheets/project/project_edit_sheet.dart';
import '../sheets/common/subscribe_guide_sheet.dart';
// pages
import '../pages/project/archived_projects_page.dart';
// others
import '../generated/l10n/app_localizations.dart';

class ProjectsView extends StatelessWidget {
  const ProjectsView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    // 同时监听 ProjectState 和 UserState 的变化
    final projectState = context.watch<ProjectState>();
    context.watch<UserState>(); // 确保 UserState 变化时此视图会重建

    // 过滤掉已归档的项目
    final projects = projectState.projects;
    final activeProjects = projects.where((p) => !p.isArchived).toList();
    // 是否有归档项目
    final hasArchivedProjects = projects.any((p) => p.isArchived);

    return Scaffold(
      appBar: AppHeaderBar(
        title: Text(l10n.projectsView_title),
        actions: [
          IconButton(
            icon: const Icon(LucideIcons.circlePlus),
            onPressed: () {
              // 增加震动反馈
              HapticFeedback.lightImpact();
              if (projectState.canCreateProject()) {
                // 如果可以创建，则显示编辑页
                ProjectEditSheet.show(context);
              } else {
                // 如果不可以创建，则显示订阅引导页
                SubscribeGuideSheet.show(
                  context,
                  feature: Feature.project,
                );
              }
            },
          ),
        ],
      ),
      body: _buildPlaceholder(context, projects.isEmpty) ?? ListView.builder(
        // itemCount 增加 1 (按钮) + 1 (底部空白)
        itemCount: activeProjects.length + (hasArchivedProjects ? 1 : 0) + 1,
        itemBuilder: (context, index) {
          // 最后一项是底部空白
          if (index == activeProjects.length + (hasArchivedProjects ? 1 : 0)) {
            return SizedBox(height: Config.app.bottomSpace);
          }
          // 倒数第二项（如果存在归档项目）是按钮
          if (hasArchivedProjects && index == activeProjects.length) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
              child: Center(
                child: TextButton.icon(
                  icon: const Icon(LucideIcons.archive, size: 16),
                  label: Text(l10n.projectsView_archived),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const ArchivedProjectsPage()),
                    );
                  },
                ),
              ),
            );
          }
          // 其他项是项目卡片
          final project = activeProjects[index];
          return ProjectCard(project: project);
        },
      ),
    );
  }

  Widget? _buildPlaceholder(BuildContext context, bool isEmpty) {
    final l10n = AppLocalizations.of(context)!;
    // 既没有活跃项目，也没有归档项目
    return isEmpty ? Center(child: Text(l10n.projectsView_emptyMessage)) : null;
  }
}
