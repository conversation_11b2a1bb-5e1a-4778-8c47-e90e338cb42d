// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// states
import '../../states/user_state.dart';
// tools
import '../../tools/app_exception.dart';
import '../../tools/app_exception_mapper.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
import '../../widgets/common/app_exception_snackbar.dart';
import '../../widgets/common/base_action_dialog.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class DeleteAccountSheet extends StatefulWidget {
  const DeleteAccountSheet({super.key});

  @override
  State<DeleteAccountSheet> createState() => _DeleteAccountSheetState();
}

class _DeleteAccountSheetState extends State<DeleteAccountSheet> {
  bool _isConfirmed = false;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return BaseBottomSheet(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            l10n.deleteAccountSheet_title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            l10n.deleteAccountSheet_warning,
          ),
          const SizedBox(height: 16),
          CheckboxListTile(
            title: Text(
              l10n.common_confirmDelete,
              style: TextStyle(color: colorScheme.error),
            ),
            value: _isConfirmed,
            activeColor: colorScheme.error,
            controlAffinity: ListTileControlAffinity.leading,
            onChanged: (bool? value) {
              HapticFeedback.lightImpact();
              setState(() => _isConfirmed = value ?? false);
            },
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _isConfirmed ? _handleDeleteAccount : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _isConfirmed ? colorScheme.error : colorScheme.onSurface.withValues(alpha: 0.12),
              foregroundColor: _isConfirmed ? colorScheme.onError : colorScheme.onSurface.withValues(alpha: 0.38),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(LucideIcons.trash2),
                const SizedBox(width: 8),
                Text(l10n.common_delete),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleDeleteAccount() async {
    HapticFeedback.lightImpact();

    final l10n = AppLocalizations.of(context)!;

    // 先关闭 BottomSheet
    Navigator.of(context).pop();

    // 然后显示确认对话框
    final result = await BaseActionDialog.show(
      context,
      title: l10n.deleteAccountSheet_confirmTitle,
      content: l10n.deleteAccountSheet_confirmContent,
      cancelText: l10n.common_cancel,
      confirmText: l10n.common_delete,
      onConfirm: () => context.read<UserState>().deleteUser(),
    );

    if (result == true) {
      // 删除成功
      if (mounted) {
        // 关闭 AccountPage
        Navigator.of(context).pop();
        // 关闭 SettingsPage
        Navigator.of(context).pop();
        // 显示成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(l10n.deleteAccountSheet_successed),
            backgroundColor: Colors.green,
          ),
        );
      }
    } else if (result is Exception) {
      // 删除失败
      if (mounted) {
        final appException = appExceptionMapper(
          error: result,
          how: AppExceptionHow.deleteAccount,
        );
        showAppExceptionSnackBar(context, appException);
      }
    }
  }
}
