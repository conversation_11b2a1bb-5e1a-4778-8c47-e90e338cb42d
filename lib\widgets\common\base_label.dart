// import 'dart:developer' as developer;
import 'package:flutter/material.dart';

class BaseLabel extends StatelessWidget {
  final String text;
  final Color? color;
  final double? fontSize;

  const BaseLabel({
    super.key,
    required this.text,
    this.color,
    this.fontSize,
  });

  @override
  Widget build(BuildContext context) {
    final finalColor = color ?? Theme.of(context).colorScheme.primary;
    final finalFontSize = fontSize ?? 12.0;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 1.0),
      decoration: BoxDecoration(
        color: finalColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20.0),
        border: Border.all(
          width: 1.0,
          color: finalColor.withValues(alpha: 0.2),
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: finalColor,
          fontSize: finalFontSize,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
