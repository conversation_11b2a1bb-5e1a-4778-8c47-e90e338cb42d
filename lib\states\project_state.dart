// import 'dart:developer' as developer;
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:collection/collection.dart';
// models
import '../models/project_model.dart';
import '../models/user_model.dart';
// services
import '../services/permission_service.dart';
import '../services/project_service.dart';
// tools
import '../tools/app_exception.dart';
import '../tools/app_exception_mapper.dart';

class ProjectState extends ChangeNotifier {
  final ProjectService _projectService;
  final PermissionService _permissionService;

  /// ====== 私有状态 ======

  StreamSubscription<List<ProjectModel>>? _projectsSubscription;
  List<ProjectModel> _projects = [];
  AppException? _streamError;

  /// 当前用户对象 (缓存)
  UserModel? _currentUser;

  /// ====== 构造函数 ======

  ProjectState(this._projectService, this._permissionService);

  /// ====== 公共状态 ======

  // 获取所有项目的只读列表
  List<ProjectModel> get projects => List.unmodifiable(_projects);
  // 获取错误信息
  AppException? get streamError => _streamError;

  /// ====== 流监听 ======

  /// 当 UserState 发生变化时，由 ProxyProvider 调用
  /// 这是管理项目数据订阅的唯一入口
  void listenToDependencyChanges(UserModel? user) {
    final oldUserId = _currentUser?.id;
    final newUserId = user?.id;

    if (oldUserId == newUserId) return;

    _projectsSubscription?.cancel();
    _projectsSubscription = null;
    _currentUser = user;
    _projects = [];
    _streamError = null;
    notifyListeners();

    if (newUserId != null) {
      _listenToProjectChanges(newUserId);
    }
  }

  // 监听项目流 (需要 userId)
  void _listenToProjectChanges(String userId) {
    if (userId.isEmpty) return;

    _projectsSubscription = _projectService.getProjectStream(userId).listen(
      (projectsFromStream) {
        _projects = projectsFromStream;
        _streamError = null;
        notifyListeners();
      },
      onError: (error, stack) {
        _streamError = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.loadProject,
          stack: stack,
        );
        notifyListeners();
      },
    );
  }

  /// ====== 网络请求 ======

  // 创建项目 (使用缓存的 userId)
  Future<ProjectModel?> createProject(ProjectModel project) async {
    final user = _currentUser;

    if (user == null || user.id.isEmpty) {
      throw AppException(
        how: AppExceptionHow.createProject,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    // 使用权限服务执行操作
    return await _permissionService.executeWithPermission(
      feature: Feature.project,
      currentUsage: _projects.length,
      currentUser: user,
      action: () => _projectService.createProject(user.id, project),
    );
  }

  // 更新现有项目 (使用缓存的 userId)
  Future<void> updateProject(ProjectModel project) async {
    final userId = _currentUser?.id;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.updateProject,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (project.userId != userId) {
      throw AppException(
        how: AppExceptionHow.updateProject,
        why: AppExceptionWhy.permissionDenied,
      );
    }

    if (project.id.isEmpty) return;

    await _projectService.updateProject(userId, project);
  }

  // 仅删除项目 (使用缓存的 userId)
  Future<void> deleteProjectOnly(String projectId) async {
    final userId = _currentUser?.id;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.deleteProject,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (projectId.isEmpty) return;

    await _projectService.deleteProjectOnly(projectId);
  }

  // 删除项目及任务 (使用缓存的 userId)
  Future<void> deleteProjectWithTasks(String projectId) async {
    final userId = _currentUser?.id;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.deleteProject,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (projectId.isEmpty) return;

    await _projectService.deleteProjectWithTasks(projectId);
  }

  /// ====== 工具方法 ======
  
  /// 检查当前用户是否可以创建新项目 (用于UI层)
  bool canCreateProject() {
    final user = _currentUser;

    if (user == null) return false;

    return _permissionService.canPerformAction(
      Feature.project,
      _projects.length, // 计算所有项目 (包括活跃项目与归档项目)
      user,
    );
  }

  // 根据ID获取项目
  ProjectModel? getProjectById(String id) {
    return _projects.firstWhereOrNull((project) => project.id == id);
  }

  @override
  void dispose() {
    _projectsSubscription?.cancel();
    super.dispose();
  }
}
