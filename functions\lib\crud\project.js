import { onCall, HttpsError } from 'firebase-functions/v2/https';
import * as logger from 'firebase-functions/logger';
import { db, FIREBASE, DATABASE } from '../tools/config.js';
/**
 * 仅删除项目文档，并将其所有关联任务的 projectId 字段重置为 null。
 */
export const deleteProjectOnly = onCall({ region: FIREBASE.REGION }, async (request) => {
    if (!request.auth) {
        throw new HttpsError('unauthenticated', 'Authentication is required for this operation');
    }
    const uid = request.auth.uid;
    const { projectId } = request.data;
    if (!projectId || typeof projectId !== 'string') {
        throw new HttpsError('invalid-argument', 'A valid projectId must be provided');
    }
    const projectRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid).collection(DATABASE.PROJECTS_COLLECTION).doc(projectId);
    const tasksRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid).collection(DATABASE.TASKS_COLLECTION);
    try {
        const batch = db.batch();
        // 1. 查找所有关联的任务并更新其 projectId
        const tasksQuery = await tasksRef.where('projectId', '==', projectId).get();
        tasksQuery.forEach(doc => {
            batch.update(doc.ref, { projectId: null });
        });
        // 2. 删除项目文档本身
        batch.delete(projectRef);
        await batch.commit();
        return { success: true };
    }
    catch (error) {
        logger.error(`为用户 ${uid} 仅删除项目 ${projectId} 失败:`, error);
        throw new HttpsError('internal', 'An unknown error occurred while deleting the project');
    }
});
/**
 * 删除项目及其所有关联的任务。
 */
export const deleteProjectWithTasks = onCall({ region: FIREBASE.REGION }, async (request) => {
    if (!request.auth) {
        throw new HttpsError('unauthenticated', 'Authentication is required for this operation');
    }
    const uid = request.auth.uid;
    const { projectId } = request.data;
    if (!projectId || typeof projectId !== 'string') {
        throw new HttpsError('invalid-argument', 'A valid projectId must be provided');
    }
    const projectRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid).collection(DATABASE.PROJECTS_COLLECTION).doc(projectId);
    const tasksRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid).collection(DATABASE.TASKS_COLLECTION);
    try {
        const batch = db.batch();
        // 1. 查找所有关联的任务并删除
        const tasksQuery = await tasksRef.where('projectId', '==', projectId).get();
        tasksQuery.forEach(doc => {
            batch.delete(doc.ref);
        });
        // 2. 删除项目文档本身
        batch.delete(projectRef);
        await batch.commit();
        return { success: true };
    }
    catch (error) {
        logger.error(`为用户 ${uid} 删除项目及其关联任务 ${projectId} 失败:`, error);
        throw new HttpsError('internal', 'An unknown error occurred while deleting the project and its tasks');
    }
});
//# sourceMappingURL=project.js.map