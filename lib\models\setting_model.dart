// import 'dart:developer' as developer;
import 'package:equatable/equatable.dart';
// models
import './nav_model.dart';
// tools
import '../tools/config.dart';

/// ⚠️ 这段注释不要删除：
/// ⚠️ 如果 Models 字段有改动
/// ⚠️ 请提醒我同步更新 Cloud Functions 和 Firebase Rules

class SettingModel extends Equatable {
  // 竖屏设备
  final List<String>? navVer;         // 导航自定义配置
  final List<String>? statVer;        // 统计图自定义配置
  // 横屏设备，本项目可以忽略
  final List<String>? navHor;         // 导航自定义配置
  final List<String>? statHor;        // 统计图自定义配置
  final int weekStartDay;             // 一周的开始日 (1=周一, 7=周日)
  final bool showCompletedTasks;      // 是否显示已完成的任务
  final bool showOverdueTasks;        // 是否显示已逾期的任务
  final bool showDroppedTasks;        // 是否显示已丢弃的任务

  // 构造函数保持不变，但 fromDatabase 会处理默认值
  const SettingModel({
    required this.navVer,
    required this.statVer,
    required this.navHor,
    required this.statHor,
    required this.weekStartDay,
    required this.showCompletedTasks,
    required this.showOverdueTasks,
    required this.showDroppedTasks,
  });

  /// 默认设置
  static final SettingModel defaults = SettingModel(
    navVer: NavModel.defaultNavIds,
    statVer: null,
    navHor: null,
    statHor: null,
    weekStartDay: 1,
    showCompletedTasks: true,
    showOverdueTasks: true,
    showDroppedTasks: true,
  );

  /// 创建一个新的SettingModel，保留未指定的值
  SettingModel copyWith({
    List<String>? navVer,
    List<String>? statVer,
    List<String>? navHor,
    List<String>? statHor,
    int? weekStartDay,
    bool? showCompletedTasks,
    bool? showOverdueTasks,
    bool? showDroppedTasks,
  }) {
    return SettingModel(
      navVer: navVer ?? this.navVer,
      statVer: statVer ?? this.statVer,
      navHor: navHor ?? this.navHor,
      statHor: statHor ?? this.statHor,
      weekStartDay: weekStartDay ?? this.weekStartDay,
      showCompletedTasks: showCompletedTasks ?? this.showCompletedTasks,
      showOverdueTasks: showOverdueTasks ?? this.showOverdueTasks,
      showDroppedTasks: showDroppedTasks ?? this.showDroppedTasks,
    );
  }

  /// 从 Firestore 文档数据创建 SettingModel
  factory SettingModel.fromDatabase(Map<String, dynamic>? data) {
    if (data == null) return SettingModel.defaults;

    final navVerData = data[Config.service.navVerKey];
    final statVerData = data[Config.service.statVerKey];
    final navHorData = data[Config.service.navHorKey];
    final statHorData = data[Config.service.statHorKey];

    return SettingModel(
      navVer: navVerData is List ? List<String>.from(navVerData) : null,
      statVer: statVerData is List ? List<String>.from(statVerData) : null,
      navHor: navHorData is List ? List<String>.from(navHorData) : null,
      statHor: statHorData is List ? List<String>.from(statHorData) : null,
      weekStartDay: data[Config.service.weekStartKey] as int? ?? defaults.weekStartDay,
      showCompletedTasks: data[Config.service.showCompletedTasksKey] as bool? ?? defaults.showCompletedTasks,
      showOverdueTasks: data[Config.service.showOverdueTasksKey] as bool? ?? defaults.showOverdueTasks,
      showDroppedTasks: data[Config.service.showDroppedTasksKey] as bool? ?? defaults.showDroppedTasks,
    );
  }

  // Equatable 需要比较的属性
  @override
  List<Object?> get props => [
    navVer,
    statVer,
    navHor,
    statHor,
    weekStartDay,
    showCompletedTasks,
    showOverdueTasks,
    showDroppedTasks,
  ];
}
