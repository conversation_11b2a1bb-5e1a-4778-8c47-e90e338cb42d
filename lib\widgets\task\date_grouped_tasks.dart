// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// models
import '../../models/task_model.dart';
// tools
import '../../tools/config.dart';
import '../../tools/extensions.dart';
// widgets
import './task_card.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class DateGroupedTasks extends StatelessWidget {
  final List<TaskModel> tasks;
  final bool descending;

  const DateGroupedTasks({
    super.key,
    required this.tasks,
    this.descending = false,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    if (tasks.isEmpty) {
      return SliverFillRemaining(
        child: Center(child: Text(l10n.dateGroupedTasksWidget_empty)),
      );
    }

    // 按日期分组任务
    final Map<String, List<TaskModel>> tasksByDate = _groupTasksByDate(context);
    
    // 排序日期键
    final dateKeys = tasksByDate.keys.toList()
      ..sort((a, b) {
        if (a == l10n.common_noDueDate) return 1;
        if (b == l10n.common_noDueDate) return -1;
        // 根据 descending 参数决定排序方向
        return descending ? b.compareTo(a) : a.compareTo(b);
      });

    // 展平所有任务，添加日期分隔器
    final List<Widget> allItems = [];
    
    for (int i = 0; i < dateKeys.length; i++) {
      final dateKey = dateKeys[i];
      final dateTasks = tasksByDate[dateKey]!;
      
      // 添加日期分隔标题
      allItems.add(
        _buildDateHeader(context, dateKey),
      );
      
      // 为该日期的每个任务创建卡片
      for (int j = 0; j < dateTasks.length; j++) {
        final task = dateTasks[j];
        allItems.add(
          TaskCard(task: task),
        );
      }
    }

    // 添加底部空白，防止FAB遮挡内容
    allItems.add(SizedBox(height: Config.app.bottomSpace));

    // 使用SliverList将所有项包装成Sliver
    return SliverList(
      delegate: SliverChildListDelegate(allItems),
    );
  }

  // 构建日期分隔标题
  Widget _buildDateHeader(BuildContext context, String dateKey) {
    final l10n = AppLocalizations.of(context)!;
    // 将日期key转换为显示的相对日期文本
    String headerText;
    
    if (dateKey == l10n.common_noDueDate) {
      headerText = l10n.common_noDueDate;
    } else {
      // 解析日期
      final parts = dateKey.split('-');
      final date = DateTime(
        int.parse(parts[0]), 
        int.parse(parts[1]), 
        int.parse(parts[2])
      );
      
      // 计算相对日期
      headerText = date.getRelativeDateText(l10n);
    }
    
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 8, 20, 4),
      alignment: Alignment.bottomLeft,
      child: Text(
        headerText,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Map<String, List<TaskModel>> _groupTasksByDate(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final Map<String, List<TaskModel>> groupedTasks = {};
    
    for (var task in tasks) {
      // 使用日期作为键，格式为：yyyy-MM-dd
      String dateKey;
      if (task.deadlineTime != null) {
        final year = task.deadlineTime!.year;
        final month = task.deadlineTime!.month.toString().padLeft(2, '0');
        final day = task.deadlineTime!.day.toString().padLeft(2, '0');
        dateKey = '$year-$month-$day';
      } else {
        dateKey = l10n.common_noDueDate;
      }
      
      // 将任务添加到对应日期分组
      if (!groupedTasks.containsKey(dateKey)) {
        groupedTasks[dateKey] = [];
      }
      groupedTasks[dateKey]!.add(task);
    }
    
    return groupedTasks;
  }
}
