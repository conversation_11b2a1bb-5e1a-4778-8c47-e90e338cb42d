// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
// states
import '../../states/setting_state.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class DateTimeSettingPage extends StatelessWidget {
  const DateTimeSettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.dateTimeSettingPage_title),
      ),
      body: Consumer<SettingState>(
        builder: (context, settingState, _) {
          return ListView(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  l10n.dateTimeSettingPage_weekStart,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
              RadioListTile<int>(
                title: Text(l10n.common_monday),
                value: 1,
                groupValue: settingState.weekStartDay,
                dense: true,
                onChanged: (value) {
                  if (value != null) {
                    HapticFeedback.lightImpact();
                    settingState.setWeekStartDay(value);
                  }
                },
              ),
              RadioListTile<int>(
                title: Text(l10n.common_sunday),
                value: 7,
                groupValue: settingState.weekStartDay,
                dense: true,
                onChanged: (value) {
                  if (value != null) {
                    HapticFeedback.lightImpact();
                    settingState.setWeekStartDay(value);
                  }
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
