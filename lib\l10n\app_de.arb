{"@@locale": "de", "common_today": "<PERSON><PERSON>", "common_tomorrow": "<PERSON><PERSON>", "common_yesterday": "Gestern", "common_daysAgo": "Vor {count} <PERSON>en", "common_daysLater": "In {count} Tagen", "common_noDueDate": "<PERSON><PERSON>", "common_day": "Tag", "common_week": "<PERSON><PERSON><PERSON>", "common_month": "<PERSON><PERSON>", "common_year": "<PERSON><PERSON><PERSON>", "common_monday": "Montag", "common_tuesday": "Dienstag", "common_wednesday": "Mittwoch", "common_thursday": "Don<PERSON><PERSON>", "common_friday": "Freitag", "common_saturday": "Samstag", "common_sunday": "Sonntag", "common_weekdays": "<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,Sa,So", "common_save": "Speichern", "common_cancel": "Abbrechen", "common_confirm": "Bestätigen", "common_delete": "Löschen", "common_confirmDelete": "Löschen bestätigen", "common_retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "common_default": "Standard", "common_unknown": "Unbekannt", "common_categoryTask": "Aufgabenansichten", "common_categoryTimer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "common_categoryProject": "Projektansichten", "common_categoryFunction": "Funktionsansichten", "common_initializing": "Initialisiere...", "common_processing": "In Bearbeitung...", "common_loading": "Lädt...", "common_unknownError": "Ein unbekannter Fehler ist aufgetreten", "common_specificDate": "Bestimmtes Datum", "common_maxSelection": "<PERSON>is zu {count} <PERSON><PERSON> auswählen", "common_unknownTask": "Unbekannte Aufgabe", "initScreen_authenticatingTitle": "Motor wird gestartet...", "initScreen_authenticatingDesc": "Be<PERSON><PERSON>, Ihre Welt der Produktivität zu betreten", "initScreen_refreshingSessionTitle": "<PERSON><PERSON> wird synchronisiert...", "initScreen_refreshingSessionDesc": "Damit Ihre Daten auf allen Geräten konsistent bleiben", "next1DayView_title": "<PERSON><PERSON>", "next1DayView_emptyMessage": "<PERSON><PERSON> Aufgaben für heute", "next3DaysView_title": "3 Tage", "next3DaysView_emptyMessage": "<PERSON>ine Aufgaben in den nächsten 3 Tagen", "next7DaysView_title": "7 Tage", "next7DaysView_emptyMessage": "<PERSON><PERSON> Aufgaben in den nächsten 7 Tagen", "inboxView_title": "<PERSON><PERSON><PERSON><PERSON>", "inboxView_emptyMessage": "<PERSON><PERSON> Aufgaben im Posteingang", "calendarListView_titleCurrentYear": "{date}", "calendarListView_titleOtherYear": "{date}", "calendarListView_emptyMessage": "<PERSON><PERSON> an diesem Tag", "calendarTimelineView_title": "Zeitleiste", "monthCalendarView_title": "<PERSON><PERSON><PERSON>", "eisenhowerMatrixView_title": "<PERSON>-<PERSON>", "eisenhowerMatrixView_quadrant1": "Dringend & Wichtig 🚩🔥", "eisenhowerMatrixView_quadrant2": "Nicht Dringend & Wichtig 🚩", "eisenhowerMatrixView_quadrant3": "Dringend & Nicht Wichtig 🔥", "eisenhowerMatrixView_quadrant4": "Nicht Dringend & Nicht Wichtig", "eisenhowerMatrixView_emptyMessage": "<PERSON><PERSON> in diesem Quadranten", "todoView_title": "<PERSON><PERSON><PERSON><PERSON>", "todoView_emptyMessage": "<PERSON><PERSON> ausstehenden Aufgaben", "overdueView_title": "Überfällig", "overdueView_emptyMessage": "<PERSON><PERSON> überfälligen Aufgaben", "completedView_title": "Abgeschlossen", "completedView_emptyMessage": "<PERSON><PERSON> abgeschlossenen Aufgaben", "droppedView_title": "Verworfen", "droppedView_emptyMessage": "<PERSON><PERSON> verworfenen Aufgaben", "timersView_title": "Timer", "timersView_emptyMessage": "<PERSON><PERSON>", "tasksTimersView_title": "Aufgaben-Timer", "tasksTimersView_emptyMessage": "<PERSON><PERSON>", "projectsView_title": "Projekte", "projectsView_archived": "Archivierte Projekte", "projectsView_emptyMessage": "<PERSON><PERSON>", "projectView_projectNotFound": "Projekt nicht gefunden", "projectView_taskCompletedRate": "{completedCount}/{totalCount} erledigt", "projectView_emptyMessage": "<PERSON><PERSON> Aufgaben in diesem Projekt", "recurringsView_title": "Wiederkehrende Aufgaben", "recurringsView_emptyMessage": "<PERSON><PERSON> wiederkehrenden Aufgaben", "statisticsView_title": "Statistiken", "statisticsView_emptyMessage": "<PERSON><PERSON> Statistik-Diagramme, bitte in den Einstellungen aktivieren", "entryView_title": "Eingang", "searchPage_title": "<PERSON><PERSON>", "searchPage_hintText": "Aufgabennamen suchen...", "searchPage_noKeywordMessage": "<PERSON>te gib Suchbegriffe ein, um Aufgaben zu suchen", "searchPage_noResultFound": "<PERSON><PERSON> Aufgaben mit \"{searchTerm}\" gefunden", "settingsPage_title": "Einstellungen", "settingsPage_notSignedIn": "Nicht angemeldet", "settingsPage_notSignedInDesc": "Zum Anmelden oder Registrieren tippen", "settingsPage_accountInfo": "Kontoinformationen", "settingsPage_accountInfoDesc": "Zum Anzeigen oder Bearbeiten tippen", "settingsPage_upgradeToPremium": "Auf Premium upgraden", "settingsPage_upgradeToPremiumDesc": "Weitere erweiterte Funktionen freischalten", "settingsPage_nav": "Navigationsleiste", "settingsPage_navDesc": "Passe deine untere Navigationsleiste an", "settingsPage_stat": "Statistik-Diagramm", "settingsPage_statDesc": "Passe deine Statistik-Diagramme an", "settingsPage_theme": "<PERSON>a", "settingsPage_themeDesc": "Wähle dein bevorzugtes App-Thema", "settingsPage_language": "<PERSON><PERSON><PERSON>", "settingsPage_languageDesc": "Anzeigesprache der Anwendung ändern", "settingsPage_dateTime": "Datum und Uhrzeit", "settingsPage_dateTimeDesc": "Datums- und Uhrzeitanzeigeformate einstellen", "settingsPage_taskDisplay": "Aufgabenanzeige", "settingsPage_taskDisplayDesc": "Einstellen, wie Aufgaben angezeigt werden", "accountPage_title": "Kontoinformationen", "accountPage_currentPlan": "Aktueller Plan", "accountPage_planMonthly": "<PERSON><PERSON><PERSON>", "accountPage_planYearly": "<PERSON><PERSON><PERSON><PERSON>", "accountPage_planLifetime": "Lebenslang", "accountPage_renewsOn": "Verlängert am", "accountPage_expiresOn": "Läuft ab am", "accountPage_joined": "Beigetreten", "accountPage_days": "{days} <PERSON><PERSON>", "accountPage_signUpMethod": "Anmeldeart", "accountPage_changeUsername": "Benutzernamen ändern", "accountPage_signOut": "Abmelden", "accountPage_confirmSignOutTitle": "Abmeldung bestätigen", "accountPage_confirmSignOutContent": "Bist du sicher, dass du dich vom aktuellen Konto abmelden möchtest?", "accountPage_deleteAccount": "Konto löschen (Gefährlich)", "navSettingPage_title": "Navigationseinstellungen", "navSettingPage_enabledNavs": "Aktivierte Navigationselemente", "navSettingPage_emptyEnabledNavs": "Keine aktivierten Navigationselemente. Nur die Eingangsseite wird angezeigt.", "navSettingPage_disabledNavs": "Deaktivierte Navigationselemente", "statSettingPage_title": "Statistik-Diagramm-Einstellungen", "statSettingPage_enabledStats": "Aktivierte Diagramme", "statSettingPage_emptyEnabledStats": "<PERSON>ine aktivierten Diagramme", "statSettingPage_disabledStats": "Deaktivierte Diagramme", "statSettingPage_categoryTask": "Aufgabenstatistiken", "statSettingPage_categoryTimer": "Timer-<PERSON><PERSON><PERSON><PERSON><PERSON>", "statSettingPage_categoryHabit": "Gewohnheitsstatistiken", "statSettingPage_categoryOther": "Andere Statistiken", "themeSettingPage_title": "Thema-Einstellungen", "languageSettingPage_title": "Spracheinstellungen", "languageSettingPage_system": "System", "dateTimeSettingPage_title": "Datums- und Zeiteinstellungen", "dateTimeSettingPage_weekStart": "Woche beginnt am", "taskDisplaySettingPage_title": "Aufgabenanzeigeeinstellungen", "taskDisplaySettingPage_showCompleted": "Erledigte Aufgaben anzeigen", "taskDisplaySettingPage_showCompletedDesc": "Erledigte Aufgaben in der Aufgabenliste anzeigen", "taskDisplaySettingPage_showOverdue": "Überfällige Aufgaben anzeigen", "taskDisplaySettingPage_showOverdueDesc": "Überfällige, aber unerledigte Aufgaben in der Aufgabenliste anzeigen", "taskDisplaySettingPage_showDropped": "Verworfene Aufgaben anzeigen", "taskDisplaySettingPage_showDroppedDesc": "Verworfene Aufgaben in der Aufgabenliste anzeigen", "signInPage_title": "Anmelden oder Registrieren", "signInPage_linkSent": "Anmeldelink gesendet, bitte überprüfe deine E-Mails", "signInPage_linkFailed": "<PERSON><PERSON> beim Senden des Links: {message}", "signInPage_errorAccountExists": "Diese E-Mail ist bereits mit einer anderen Methode registriert. Bitte melde dich zuerst mit dieser Methode an und verknüpfe dann dein Google-Konto in den Einstellungen.", "signInPage_errorUserDisabled": "Dieses Google-Konto wurde deaktiviert. Bitte kontaktiere den Support.", "signInPage_errorOperationNotAllowed": "Die Google-Anmeldung ist nicht aktiviert. Bitte kontaktiere den Administrator.", "signInPage_errorNetworkRequestFailed": "Netzwerkanfrage fehlgeschlagen. Bitte überprüfe deine Netzwerkverbindung.", "signInPage_errorDefault": "Google-Anmeldung fehlgeschlagen. Bitte versuche es später erneut.", "signInPage_invalidEmail": "<PERSON>te gib eine gültige E-Mail-Adresse ein", "signInPage_sendLinkButton": "Anmeldelink senden", "signInPage_orDivider": "ODER", "signInPage_googleButton": "Mit Google anmelden", "taskTimersPage_title": "Aufgaben-Timer", "taskTimersPage_taskNotFound": "Aufgabeninformationen nicht gefunden", "taskTimersPage_noTimers": "<PERSON><PERSON> hat keine Timer", "archivedProjectsPage_title": "Archivierte Projekte", "archivedProjectsPage_emptyMessage": "<PERSON>ine archivierten Projekte", "subscribeGuideSheet_projectTitle": "Projektlimit erreicht", "subscribeGuideSheet_projectLimit": "Die kostenlose Version erlaubt das Erstellen von bis zu {count} Projekten.", "subscribeGuideSheet_projectBenefit": "Upgrade auf Premium, um bis zu {count} Projekte zu erstellen.", "subscribeGuideSheet_recurringTitle": "Limit für wiederkehrende Aufgaben erreicht", "subscribeGuideSheet_recurringLimit": "Die kostenlose Version erlaubt das Erstellen von bis zu {count} wiederkehrenden Aufgaben.", "subscribeGuideSheet_recurringBenefit": "Upgrade auf Premium, um bis zu {count} wiederkehrende Aufgaben zu erstellen.", "subscribeGuideSheet_timerTitle": "Timer-<PERSON><PERSON>", "subscribeGuideSheet_timerLimit": "Die kostenlose Version erlaubt das Erstellen von bis zu {count} Timern.", "subscribeGuideSheet_timerBenefit": "Upgrade auf Premium für unbegrenzte Timer.", "subscribeGuideSheet_upgradeButton": "Auf Premium upgraden", "subscribeGuideSheet_upgradeError": "Abonnementseite konnte nicht geladen werden: {error}", "subscribeGuideSheet_laterButton": "Vielleicht später", "taskEditPage_titleEdit": "Aufgabe bearbeiten", "taskEditPage_titleCreate": "Aufgabe erstellen", "taskEditPage_droppedWarning": "Diese Aufgabe wurde verworfen und kann nicht bearbeitet werden. Um sie zu bearbeiten, stellen Sie sie bitte zuerst wieder her.", "taskEditPage_recurringWarning": "Diese Aufgabe ist eine Instanz einer wiederkehrenden Aufgabe. Aktuelle Änderungen gelten nur für diese Instanz.", "taskEditPage_nameHint": "Was hast du vor?", "taskEditPage_validatorEmpty": "Aufgabenname darf nicht leer sein", "taskEditPage_validatorLength": "Aufgabenname darf {max} Zeichen nicht überschreiten", "taskEditPage_timerActionStatusRunning": "Ein Timer läuft gerade, daher kann nicht geklickt werden", "taskEditPage_timerActionStatusCompleted": "Erledigte Aufgaben können nicht zeitlich erfasst werden", "taskEditPage_timerActionStatusDropped": "Zeitmessung für verworfene Aufgaben nicht möglich", "taskEditPage_timerActionStatusLimitReached": "Du hast das Timer-<PERSON><PERSON> erre<PERSON>t, bitte abonniere", "taskEditPage_timerActionStatusReady": "<PERSON><PERSON><PERSON>, um die Zeit für die Aufgabe zu starten", "taskDatePicker_noDate": "<PERSON><PERSON>", "taskDatePicker_nextMonday": "Nächsten Montag", "taskProjectPicker_noProject": "<PERSON><PERSON> zugeordnetes Projekt", "taskProjectPicker_emptyMessage": "<PERSON>ch keine Projekte, bitte erstelle zu<PERSON>t eines", "taskProjectPicker_createProject": "Neues Projekt erstellen", "taskClockPicker_noDueClock": "<PERSON>ine Fälligkeitszeit", "taskClockPicker_title": "Fälligkeitszeit auswählen", "taskEventPicker_noEventClock": "<PERSON><PERSON>", "taskEventPicker_title": "Ereigniszeit auswählen", "taskEventPicker_errorNoStartClock": "Bitte wähle eine Startzeit", "taskEventPicker_errorNoEndClock": "Bitte wähle eine Endzeit", "taskEventPicker_errorEndClockBeforeStartClock": "Endzeit kann nicht vor der Startzeit liegen", "taskEventPicker_startClockLabel": "Startzeit", "taskEventPicker_endClockLabel": "Endzeit", "taskEventPicker_selectClock": "Zeit auswählen", "taskLongPressSheet_drop": "Aufgabe verwerfen", "taskLongPressSheet_dropDesc": "Die Aufgabe wird nicht mehr bedienbar sein", "taskLongPressSheet_restore": "Aufgabe wiederherstellen", "taskLongPressSheet_restoreDesc": "Die Aufgabe wird in einen bedienbaren Zustand zurückversetzt", "taskLongPressSheet_delete": "Aufgabe löschen", "taskLongPressSheet_deleteDesc": "Zugehörige Timer werden ebenfalls gelöscht", "taskLongPressSheet_confirmDeleteTitle": "Löschen bestätigen", "taskLongPressSheet_confirmDeleteContent": "B<PERSON> du sicher, dass du die Aufgabe \"{name}\" dauerhaft löschen möchtest? Diese Aktion kann nicht rückgängig gemacht werden.", "timerEditSheet_title": "Timer bearbeiten", "timerEditSheet_startLabel": "Start:", "timerEditSheet_endLabel": "Ende:", "timerEditSheet_deleteTimer": "<PERSON>sen Timer löschen", "timerEditSheet_errorEndClockBeforeStartClock": "Endzeit kann nicht vor der Startzeit liegen.", "timerEditSheet_confirmDeleteTitle": "Löschen bestätigen", "timerEditSheet_confirmDeleteContent": "B<PERSON> du sicher, dass du diesen Timer-Eintrag löschen möchtest? Diese Aktion kann nicht rückgängig gemacht werden.", "timerClockSheet_title": "Zeit auswählen", "projectEditSheet_titleEdit": "Projekt bearbeiten", "projectEditSheet_titleCreate": "Projekt erstellen", "projectEditSheet_nameHint": "Projektnamen eingeben...", "projectEditSheet_validatorEmpty": "Projektname darf nicht leer sein", "projectEditSheet_validatorLength": "Projektname darf {max} Zeichen nicht überschreiten", "projectLongPressSheet_unarchive": "Dearchivieren", "projectLongPressSheet_unarchiveDesc": "Das Projekt wird zurück in die Listenansicht verschoben", "projectLongPressSheet_archive": "Projekt archivieren", "projectLongPressSheet_archiveDesc": "Das Projekt wird in die Archivseite verschoben", "projectLongPressSheet_deleteProject": "Projekt löschen", "projectLongPressSheet_deleteProjectDesc": "Aufgaben bleiben erhalten, sind aber nicht mehr mit diesem Projekt verknüpft", "projectLongPressSheet_deleteProjectAndTasks": "Projekt und Aufgaben löschen", "projectLongPressSheet_deleteProjectAndTasksDesc": "Alle Aufgaben unter dem Projekt werden dauerhaft gelöscht", "projectLongPressSheet_confirmDeleteTitle": "Projekt löschen bestätigen?", "projectLongPressSheet_confirmDeleteContent": "Das Projekt \"{name}\" wird <PERSON>, aber seine Aufgaben bleiben erhalten (nicht mehr mit diesem Projekt verknüpft). Diese Aktion kann nicht rückgängig gemacht werden.", "projectLongPressSheet_deleteFailed": "Fehler beim Löschen des Projekts: {error}", "projectLongPressSheet_confirmDeleteWithTasksTitle": "Projekt und alle Aufgaben löschen bestätigen?", "projectLongPressSheet_confirmDeleteWithTasksContent": "Das Projekt \"{name}\" und alle seine Aufgaben werden dauerhaft gelöscht. Diese Aktion kann nicht rückgängig gemacht werden.", "projectLongPressSheet_deleteWithTasksFailed": "Fehler beim Löschen des Projekts und der Aufgaben: {error}", "recurringEditPage_titleEdit": "Wiederkehrende Aufgabe bearbeiten", "recurringEditPage_titleCreate": "Neue wiederkehrende Aufgabe", "recurringEditPage_creationHint": "Zunächst werden Aufgabeninstanzen für die nächsten 30 Tage erstellt, danach täglich fortlaufend.", "recurringEditPage_nameHint": "Was planst du zu wiederholen?", "recurringEditPage_errorRuleEmpty": "Wiederholungsregel darf nicht leer sein", "recurringEditPage_errorSaveFailed": "Fehler beim Erstellen/Aktualisieren der wiederkehrenden Aufgabe: {error}", "recurringRulesPicker_noRule": "<PERSON><PERSON>erholungsregel", "recurringRulesPicker_title": "Wiederholung einstellen", "rulesModePicker_title": "Wiederholungsmodus", "rulesDowPicker_title": "Wochentage auswählen", "rulesDowPicker_placeholder": "Wochentage auswählen", "rulesDowPicker_weekdays": "<PERSON>,<PERSON>,<PERSON>,<PERSON>,<PERSON>,Sa,So", "rulesDomPicker_title": "Daten auswählen", "rulesDomPicker_placeholder": "Daten auswählen", "rulesDoyPicker_title": "Daten auswählen", "rulesDoyPicker_placeholder": "Daten auswählen", "rulesStartPicker_title": "Beginnt am", "rulesStartPicker_placeholder": "Startdatum auswählen", "rulesEndPicker_title": "Endet am", "rulesEndPicker_neverEnds": "<PERSON>et nie", "rulesCountPicker_title": "Wiederholungen", "rulesCountPicker_timesSuffix": "Mal", "rulesCountPicker_unlimited": "Unbegrenzt", "recurringLongPressSheet_delete": "Wiederkehrende Aufgabe löschen", "recurringLongPressSheet_deleteDesc": "Löscht die wiederkehrende Aufgabe und ihre Instanzen dauerhaft", "recurringLongPressSheet_confirmDeleteTitle": "Löschen bestätigen", "recurringLongPressSheet_confirmDeleteContent": "B<PERSON> du sicher, dass du die wiederkehrende Aufgabe \"{name}\" dauerhaft löschen möchtest? Diese Aktion kann nicht rückgängig gemacht werden.", "changeUsernameSheet_title": "Benutzernamen ändern", "changeUsernameSheet_label": "<PERSON><PERSON><PERSON><PERSON>", "changeUsernameSheet_validatorEmpty": "<PERSON><PERSON><PERSON><PERSON> darf nicht leer sein", "changeUsernameSheet_validatorLength": "<PERSON><PERSON><PERSON><PERSON> darf {max} Zeichen nicht überschreiten", "deleteAccountSheet_title": "Konto löschen", "deleteAccountSheet_warning": "Diese Aktion kann nicht rückgängig gemacht werden. Alle deine Daten, eins<PERSON><PERSON><PERSON><PERSON> Aufgaben, <PERSON>jekt<PERSON> usw., werden dauerhaft gelöscht.", "deleteAccountSheet_confirmTitle": "Bist du sicher?", "deleteAccountSheet_confirmContent": "Einmal gelöschte Konten können nicht wiederhergestellt werden.", "deleteAccountSheet_successed": "Dein <PERSON> und alle zugehörigen Daten wurden dauerhaft gelöscht. <PERSON><PERSON><PERSON> Dank, dass du bei uns warst. Du kannst jederzeit eine neue Reise beginnen!", "todayGlanceWidget_todo": "<PERSON><PERSON>", "todayGlanceWidget_timer": "Timer", "todayGlanceWidget_overdue": "Überfällig", "taskPriorityWidget_important": "<PERSON><PERSON><PERSON><PERSON>", "taskPriorityWidget_urgent": "Dringend", "dateGroupedTasksWidget_empty": "<PERSON><PERSON>", "dayTasksColumnWidget_untimed": "Ungeplante Aufgaben", "weeklyTaskChart_title": "Aufgabenerledigung dieser Woche", "weeklyTaskChart_legendDue": "Nach Fälligkeitsdatum", "weeklyTaskChart_legendCompleted": "Nach Erledigungsdatum", "weeklyTimerChart_title": "Timer<PERSON><PERSON><PERSON> dies<PERSON>e", "habitTaskChart_error": "Fehler beim Laden der Gewohnheitsdaten", "habitTimerChart_note": "* Statistiken basieren auf dem Fälligkeitsdatum der Aufgabe, nicht auf dem Datum des Timers.", "navModel_next1DayLabel": "<PERSON><PERSON>", "navModel_next1DayName": "<PERSON><PERSON><PERSON> Aufgaben", "navModel_next1DayDesc": "Zeigt alle Aufgaben für heute", "navModel_next3DaysLabel": "3 Tage", "navModel_next3DaysName": "Aufgaben für 3 Tage", "navModel_next3DaysDesc": "Zeigt alle Aufgaben für die nächsten 3 Tage", "navModel_next7DaysLabel": "7 Tage", "navModel_next7DaysName": "Aufgaben für 7 Tage", "navModel_next7DaysDesc": "<PERSON><PERSON>gt alle Aufgaben für die nächsten 7 Tage", "navModel_inboxLabel": "Eingang", "navModel_inboxName": "Posteingangsaufgaben", "navModel_inboxDesc": "Zeigt alle Aufgaben ohne Fälligkeitsdatum oder Projekt", "navModel_calendarListLabel": "<PERSON><PERSON><PERSON>", "navModel_calendarListName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "navModel_calendarListDesc": "<PERSON><PERSON><PERSON> und Aufgabenliste", "navModel_calendarTimelineLabel": "Zeitleiste", "navModel_calendarTimelineName": "Kalender-Zeitleiste", "navModel_calendarTimelineDesc": "<PERSON><PERSON><PERSON> und Aufgaben-Zeitleiste", "navModel_monthCalendarLabel": "<PERSON><PERSON><PERSON>", "navModel_monthCalendarName": "Monatskalender", "navModel_monthCalendarDesc": "Zeigt monatliche Kalenderaufgaben", "navModel_eisenhowerMatrixLabel": "Matrix", "navModel_eisenhowerMatrixName": "<PERSON>-<PERSON>", "navModel_eisenhowerMatrixDesc": "<PERSON><PERSON><PERSON>-<PERSON>", "navModel_todoLabel": "<PERSON><PERSON><PERSON><PERSON>", "navModel_todoName": "Ausstehende Aufgaben", "navModel_todoDesc": "Alle nicht abgeschlossenen Aufgaben vor ihrer Frist anzeigen", "navModel_overdueLabel": "Überfällig", "navModel_overdueName": "Überfällige Aufgaben", "navModel_overdueDesc": "Alle nicht abgeschlossenen Aufgaben nach ihrer Frist anzeigen", "navModel_completedLabel": "Abgeschlossen", "navModel_completedName": "Abgeschlossene Aufgaben", "navModel_completedDesc": "Alle abgeschlossenen Aufgaben anzeigen", "navModel_droppedLabel": "Verworfen", "navModel_droppedName": "Verworfene Aufgaben", "navModel_droppedDesc": "Alle verworfenen Aufgaben anzeigen", "navModel_timersLabel": "Timer", "navModel_timersName": "Timer-<PERSON><PERSON>", "navModel_timersDesc": "<PERSON><PERSON><PERSON> alle Timer", "navModel_tasksTimersLabel": "Timer", "navModel_tasksTimersName": "Aufgaben-Timer-Liste", "navModel_tasksTimersDesc": "<PERSON><PERSON><PERSON> alle Aufgaben-Timer", "navModel_projectsLabel": "Projekte", "navModel_projectsName": "Projektliste", "navModel_projectsDesc": "<PERSON>eigt alle Projekte", "navModel_projectLabel": "Projekt", "navModel_projectName": "{projectName} (Projekt)", "navModel_projectDesc": "Zeigt alle Aufgaben in diesem Projekt", "navModel_recurringsLabel": "<PERSON><PERSON>erk.", "navModel_recurringsName": "Wiederkehrende Aufgaben", "navModel_recurringsDesc": "Zeigt alle wiederkehrenden Aufgaben", "navModel_statisticsLabel": "Statistik", "navModel_statisticsName": "Statistiken", "navModel_statisticsDesc": "<PERSON><PERSON><PERSON> alle Statistiken", "navModel_entryLabel": "Eingang", "navModel_entryName": "Eingang", "navModel_entryDesc": "Zeigt alle Ansichts-Verknüpfungen", "statModel_weeklyTaskName": "Wöchentliche Aufgaben", "statModel_weeklyTaskDescription": "Erledigungsstatistiken für tägliche Aufgaben dieser Woche", "statModel_weeklyTimerName": "Wöchentliche Timer", "statModel_weeklyTimerDescription": "Dauerstatistiken für tägliche Timer dieser Woche", "statModel_habitTaskName": "{habitName} (Aufgabe)", "statModel_habitTaskDescription": "Verfolge die Erledigung der Gewohnheit \"{habitName}\"", "statModel_habitTimerName": "{habitName} (Timer)", "statModel_habitTimerDescription": "Verfolge die Timer-Dauer für die Gewohnheit \"{habitName}\"", "statModel_timeProgressName": "Zeitfortschritt", "statModel_timeProgressDescription": "Ringfortschrittsstatistiken für Tag, Woche, Monat und Jahr", "themeModel_forestGreen": "Waldgrün", "themeModel_morningMist": "<PERSON><PERSON><PERSON><PERSON>", "recurringModel_every": "Alle", "recurringModel_doyFormat": "<PERSON><PERSON>", "recurringModel_description": "{every} {interval} {frequency} {dates}", "recurringModel_toDate": " bis {date}", "recurringModel_toInfinite": " unbegrenzt", "recurringModel_repeat": " {count} Mal", "appException_template": "{how} fehlgeschlagen: {why}", "appException_how_initializeApp": "App initialisieren", "appException_how_signIn": "Anmelden", "appException_how_signOut": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "appException_how_updateUsername": "Benutzernamen aktualisieren", "appException_how_deleteAccount": "Konto löschen", "appException_how_createTask": "Erstellen der Aufgabe", "appException_how_updateTask": "Aktualisieren der Aufgabe", "appException_how_deleteTask": "Löschen der Aufgabe", "appException_how_createTimer": "<PERSON><PERSON><PERSON><PERSON> des Timers", "appException_how_updateTimer": "Aktualisieren des Timers", "appException_how_deleteTimer": "Löschen des Timers", "appException_how_createProject": "Erstellen des Projekts", "appException_how_updateProject": "Aktualisieren des Projekts", "appException_how_deleteProject": "Löschen des Projekts", "appException_how_createRecurring": "Erstellen der wiederkehrenden Aufgabe", "appException_how_updateRecurring": "Aktualisieren der wiederkehrenden Aufgabe", "appException_how_deleteRecurring": "Löschen der wiederkehrenden Aufgabe", "appException_why_unauthenticated": "Benutzer nicht angemeldet", "appException_why_permissionDenied": "Berechtigung verweigert", "appException_why_serverError": "Ein Serverfehler ist aufgetreten", "appException_why_notFound": "Ressource nicht gefunden", "appException_why_alreadyExists": "Ressource existiert bereits", "appException_why_invalidArgument": "Ungültiges Argument", "appException_why_credentialAlreadyInUse": "Anmeldeinformationen bereits in Verwendung", "appException_why_emailAlreadyInUse": "E-Mail bereits in Verwendung", "appException_why_accountExistsWithDifferentCredential": "Konto existiert mit anderen Anmeldeinformationen", "appException_why_userDisabled": "Benutzerkonto deaktiviert", "appException_why_operationNotAllowed": "Vorgang nicht erlaubt", "appException_why_networkRequestFailed": "Netzwerkanfrage fehlgeschlagen", "appException_why_operationFailed": "Vorgang fehlgeschlagen", "appException_why_featureLimited": "Funktionslimit für kostenlosen Plan erreicht", "appException_why_unknown": "Unbekannter Fehler aufgetreten"}