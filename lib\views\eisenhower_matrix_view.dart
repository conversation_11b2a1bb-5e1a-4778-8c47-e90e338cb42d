// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// models
import '../models/task_model.dart';
// states
import '../states/task_state.dart';
import '../states/setting_state.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/simple_task_card.dart';
// pages
import '../pages/task/matrix_tasks_page.dart';
// others
import '../generated/l10n/app_localizations.dart';

class EisenhowerMatrixView extends StatelessWidget {
  const EisenhowerMatrixView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return Consumer2<TaskState, SettingState>(
      builder: (context, taskState, settingState, _) {
        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(l10n.eisenhowerMatrixView_title),
          ),
          body: LayoutBuilder(
            builder: (context, constraints) {
              // 计算尺寸，添加边距
              final spacing = 8.0; // 视图边缘和象限的间距以及象限之间的间距
              
              final availableWidth = constraints.maxWidth - (spacing * 2) - spacing;
              // 减去2个像素解决溢出问题
              final availableHeight = constraints.maxHeight - (spacing * 2) - spacing - 2.0;
              
              final quadrantWidth = availableWidth / 2;
              final quadrantHeight = availableHeight / 2;
              
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: spacing),
                child: Stack(
                  children: [
                    Positioned(
                      left: 0,
                      top: 0,
                      width: quadrantWidth,
                      height: quadrantHeight,
                      child: _buildQuadrant(
                        context,
                        title: l10n.eisenhowerMatrixView_quadrant1,
                        tasks: taskState.getImportantUrgentTasks(),
                        textColor: colorScheme.error,
                      ),
                    ),
                    
                    Positioned(
                      left: quadrantWidth + spacing,
                      top: 0,
                      width: quadrantWidth,
                      height: quadrantHeight,
                      child: _buildQuadrant(
                        context,
                        title: l10n.eisenhowerMatrixView_quadrant2,
                        tasks: taskState.getImportantNotUrgentTasks(),
                        textColor: colorScheme.primary,
                      ),
                    ),
                    
                    Positioned(
                      left: 0,
                      top: quadrantHeight + spacing,
                      width: quadrantWidth,
                      height: quadrantHeight,
                      child: _buildQuadrant(
                        context,
                        title: l10n.eisenhowerMatrixView_quadrant3,
                        tasks: taskState.getNotImportantUrgentTasks(),
                        textColor: colorScheme.secondary,
                      ),
                    ),
                    
                    Positioned(
                      left: quadrantWidth + spacing,
                      top: quadrantHeight + spacing,
                      width: quadrantWidth,
                      height: quadrantHeight,
                      child: _buildQuadrant(
                        context,
                        title: l10n.eisenhowerMatrixView_quadrant4,
                        tasks: taskState.getNotImportantNotUrgentTasks(),
                        textColor: colorScheme.tertiary,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
  
  Widget _buildQuadrant(
    BuildContext context, {
    required String title,
    required List<TaskModel> tasks,
    required Color textColor,
  }) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return GestureDetector(
      // 添加点击事件，进入矩阵任务页面
      onTap: () {
        Navigator.push(
          context, 
          MaterialPageRoute(
            builder: (context) => MatrixTasksPage(
              quadrantTitle: title,
              tasks: tasks,
            ),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainer,
          borderRadius: BorderRadius.circular(8.0),
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withAlpha(36),
              spreadRadius: 0.5,
              blurRadius: 1,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: textColor,
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: tasks.isEmpty
                  ? Center(
                      child: Text(
                        l10n.eisenhowerMatrixView_emptyMessage,
                        style: TextStyle(color: textColor.withAlpha(153)),
                      ),
                    )
                  : ListView.builder(
                      padding: EdgeInsets.zero,
                      itemCount: tasks.length,
                      itemBuilder: (context, index) {
                        return SimpleTaskCard(task: tasks[index]);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
