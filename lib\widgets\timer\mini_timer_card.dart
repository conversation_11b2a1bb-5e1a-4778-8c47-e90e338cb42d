// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/timer_model.dart';
// tools
import '../../tools/extensions.dart';
// sheets
import '../../sheets/timer/timer_edit_sheet.dart';

class MiniTimerCard extends StatelessWidget {
  final TimerModel timer;

  const MiniTimerCard({
    super.key,
    required this.timer,
  });

  // 构建单个计时记录的小卡片
  @override
  Widget build(BuildContext context) {
    final String dateText = timer.startTime.formatDate();
    final String timeSpanText = timer.startTime.formatSpan(timer.endTime);
    final String durationText = timer.durationSeconds.format();

    // 使用 InkWell 包裹 Card 使其可点击
    return InkWell(
      onTap: () {
        // 增加震动反馈
        HapticFeedback.lightImpact();
        // 点击时显示底部弹窗
        TimerEditSheet.show(context, timer: timer);
      },
      borderRadius: BorderRadius.circular(6.0), // 匹配 Card 的圆角
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(6.0),
        ),
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Row(
            children: [
              Text(
                dateText,
                style: TextStyle(fontSize: 16.0, color: Theme.of(context).colorScheme.onSurfaceVariant),
              ),

              const SizedBox(width: 4.0),
              Icon(LucideIcons.clock, size: 12.0, color: Theme.of(context).colorScheme.onSurfaceVariant),
              const SizedBox(width: 4.0),

              Text(
                timeSpanText,
                style: TextStyle(fontSize: 16.0, color: Theme.of(context).colorScheme.onSurfaceVariant),
              ),

              const Spacer(),

              Text(
                durationText,
                style: TextStyle(fontSize: 18.0, color: Theme.of(context).colorScheme.onSurface, fontWeight: FontWeight.w500),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
