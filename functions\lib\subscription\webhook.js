import { onRequest } from 'firebase-functions/v2/https';
import * as logger from 'firebase-functions/logger';
import { SecretManagerServiceClient } from '@google-cloud/secret-manager';
import { FIREBASE, KEY } from '../tools/config.js';
import { processRevenueCatEvent } from './utils.js';
// --- Secret Manager 客户端初始化 ---
const secretManager = new SecretManagerServiceClient();
let revenueCatWebhookKey = null; // 用于缓存从 Secret Manager 获取的密钥
/**
 * 从 Google Secret Manager 获取 RevenueCat 的 Webhook 密钥
 * 为了提高性能并减少 API 调用，密钥在获取后会被缓存
 * @returns {Promise<string>} Webhook 密钥
 */
async function getRevenueCatWebhookKey() {
    // 如果密钥已缓存，直接返回
    if (revenueCatWebhookKey)
        return revenueCatWebhookKey;
    // 构建密钥版本的完整资源名称
    const name = `projects/${FIREBASE.PROJECT_NUMBER}/secrets/${KEY.REVENUECAT_WEBHOOK}/versions/latest`;
    try {
        const [version] = await secretManager.accessSecretVersion({ name });
        const payload = version.payload?.data?.toString();
        if (!payload) {
            throw new Error('从 Secret Manager 获取的密钥内容为空');
        }
        // 缓存密钥并返回
        revenueCatWebhookKey = payload;
        return payload;
    }
    catch (error) {
        logger.error('从 Secret Manager 访问密钥时出错:', error);
        throw new Error('无法检索 Webhook 密钥');
    }
}
/**
 * HTTP 云函数，用于接收和处理来自 RevenueCat 的 Webhook 事件
 */
export const onSubscriptionWebhook = onRequest({
    region: FIREBASE.REGION,
    // 允许未经身份验证的公共访问，这对于接收来自 RevenueCat 的 Webhook 是必需的
    invoker: 'public',
}, async (request, response) => {
    // 仅允许 POST 请求
    if (request.method !== 'POST') {
        logger.warn('收到非 POST 类型的 RevenueCat Webhook 请求', { method: request.method });
        response.status(405).send('Method Not Allowed');
        return;
    }
    const token = request.headers.authorization;
    // 安全地获取用于验证的密钥
    let webhookSecret;
    try {
        webhookSecret = await getRevenueCatWebhookKey();
    }
    catch (error) {
        response.status(500).send(error.message);
        return;
    }
    // 验证请求头中是否包含 token
    if (!token) {
        logger.warn('RevenueCat Webhook 请求中缺少 Authorization 头');
        response.status(400).send('Missing Authorization header');
        return;
    }
    // 验证 token 是否匹配
    if (token !== webhookSecret) {
        logger.error('收到了无效的 RevenueCat Webhook 令牌');
        response.status(403).send('Invalid token');
        return;
    }
    try {
        logger.info('RevenueCat Webhook 令牌验证成功');
        const payload = request.body;
        // 调用核心处理逻辑
        await processRevenueCatEvent(payload);
        // 成功处理后返回 200
        response.status(200).send('Webhook received and processed');
    }
    catch (error) {
        const e = error;
        logger.error('处理 RevenueCat Webhook 时出错:', { error: e.message, stack: e.stack });
        response.status(500).send('Internal server error while processing webhook');
    }
});
//# sourceMappingURL=webhook.js.map