import { onDocumentCreated, onDocumentUpdated, FirestoreEvent, DocumentSnapshot, Change } from 'firebase-functions/v2/firestore';
import * as logger from 'firebase-functions/logger';
import { DATABASE } from '../tools/config.js';
import type { RecurringModel } from '../tools/types.js';
import TimeUtils from '../tools/time.js';
import { generateTasksForPeriod, deleteFutureIncompleteTasks } from './utils.js';


/**
 * Firestore 触发器：当创建新的周期任务时触发
 * 功能：为新周期任务生成未来 N 天的初始任务实例
 */
export const onRecurringCreate = onDocumentCreated(
  `${DATABASE.USERS_COLLECTION}/{userId}/${DATABASE.RECURRINGS_COLLECTION}/{recurringId}`,
  async (event: FirestoreEvent<DocumentSnapshot | undefined>) => {
    const snap = event.data;

    if (!snap) {
      logger.error('onRecurringCreate: 文档快照不存在或未定义', { params: event.params });
      return;
    }

    const { userId, recurringId } = event.params as { userId: string, recurringId: string };
    const recurring = snap.data() as RecurringModel | undefined;

    logger.info('onRecurringCreate 已触发', { userId, recurringId });

    if (!recurring || !recurring.rule || !recurring.template) {
      logger.error('创建的周期任务快照中未找到数据或数据缺失', { userId, recurringId, dataExists: !!recurring });
      return;
    }

    const timezone = recurring.timezone ?? DATABASE.DEFAULT_TIMEZONE;
    const { startDate, endDate } = TimeUtils.calculateGenerationRange(timezone);

    try {
      await generateTasksForPeriod(userId, recurringId, recurring, startDate, endDate);
    } catch (error: unknown) {
      const e = error as Error;
      logger.error('周期任务实例生成过程中出错', { userId, recurringId, error: e.message, stack: e.stack });
    }
  }
);


/**
 * Firestore 触发器：当更新周期任务时触发
 * 功能：删除现有的未来未完成任务，并根据更新后的周期任务重新生成未来 N 天的任务实例
 */
export const onRecurringUpdate = onDocumentUpdated(
  `${DATABASE.USERS_COLLECTION}/{userId}/${DATABASE.RECURRINGS_COLLECTION}/{recurringId}`,
  async (event: FirestoreEvent<Change<DocumentSnapshot> | undefined>) => {
    const snap = event.data;

    if (!snap) {
      logger.error('onRecurringUpdate: 文档快照不存在或未定义', { params: event.params });
      return;
    }

    const { userId, recurringId } = event.params as { userId: string, recurringId: string };
    const recurring = snap.after.data() as RecurringModel | undefined;

    logger.info('onRecurringUpdate 已触发', { userId, recurringId });

    if (!recurring || !recurring.rule || !recurring.template) {
      logger.error('更新的周期任务快照中未找到数据或数据缺失', { userId, recurringId, dataExists: !!recurring });
      return;
    }

    const timezone = recurring.timezone ?? DATABASE.DEFAULT_TIMEZONE;
    const { startDate, endDate } = TimeUtils.calculateGenerationRange(timezone);

    try {
      await deleteFutureIncompleteTasks(userId, recurringId, timezone);

      await generateTasksForPeriod(userId, recurringId, recurring, startDate, endDate);
    } catch (error: unknown) {
      const e = error as Error;
      logger.error('周期任务实例重新生成过程中出错', { userId, recurringId, error: e.message, stack: e.stack });
    }
  }
);
