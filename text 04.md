
# 首次进入

[log] ✅ Firebase 模拟器 host: *************
[log] >>> Auth Decision Tree Start: AuthUser=null, AppUser=null
[log]   [Branch 1.2] Controlled sign-out or first launch. Creating new anonymous session...

D/FirebaseAuth(22704): Notifying id token listeners about user ( fJeHfd8JkAxkCGCPuDZ6K6xpGlKH ).
D/FirebaseAuth(22704): Notifying auth state listeners about user ( fJeHfd8JkAxkCGCPuDZ6K6xpGlKH ).
[log] >>> Auth Decision Tree Start: AuthUser=fJeHfd8JkAxkCGCPuDZ6K6xpGlKH, AppUser=null
[log]   [Branch 2.1] New session detected for fJeHfd8JkAxkCGCPuDZ6K6xpGlKH. Initializing...
[log] <<< Auth Decision Tree End

[log] <<< Auth Decision Tree End

# 谷歌登录

[log] >>> Auth Decision Tree Start: AuthUser=fJeHfd8JkAxkCGCPuDZ6K6xpGlKH, AppUser=fJeHfd8JkAxkCGCPuDZ6K6xpGlKH
[log]   [Branch 2.2.1] Same user session update for fJeHfd8JkAxkCGCPuDZ6K6xpGlKH.

[log] <<< Auth Decision Tree End

# 再次进入

D/FirebaseAuth(32622): Notifying id token listeners about user ( fJeHfd8JkAxkCGCPuDZ6K6xpGlKH ).
[log] ✅ Firebase 模拟器 host: *************
[log] >>> Auth Decision Tree Start: AuthUser=fJeHfd8JkAxkCGCPuDZ6K6xpGlKH, AppUser=null
[log]   [Branch 2.1] New session detected for fJeHfd8JkAxkCGCPuDZ6K6xpGlKH. Initializing...

[log] <<< Auth Decision Tree End

# 登出

[log] >>> Auth Decision Tree Start: AuthUser=null, AppUser=null
[log]   [Branch 1.2] Controlled sign-out or first launch. Creating new anonymous session...
[log] >>> Auth Decision Tree Start: AuthUser=8Uc2rcVElGJhaYfsz7Bnat5tRvzb, AppUser=null
[log]   [Branch 2.1] New session detected for 8Uc2rcVElGJhaYfsz7Bnat5tRvzb. Initializing...
[log] <<< Auth Decision Tree End
[log] <<< Auth Decision Tree End

# 再次谷歌登录

[log] >>> Auth Decision Tree Start: AuthUser=fJeHfd8JkAxkCGCPuDZ6K6xpGlKH, AppUser=8Uc2rcVElGJhaYfsz7Bnat5tRvzb
[log]   [Branch 2.2.2] Direct account switch detected: 8Uc2rcVElGJhaYfsz7Bnat5tRvzb -> fJeHfd8JkAxkCGCPuDZ6K6xpGlKH. Forcing cleanup-then-load cycle...
[log] [Branch 2.2.2] Continuing with new session initialization for fJeHfd8JkAxkCGCPuDZ6K6xpGlKH after cleanup.
[log] <<< Auth Decision Tree End

# 删除账户

[log] >>> Auth Decision Tree Start: AuthUser=null, AppUser=null
[log]   [Branch 1.2] Controlled sign-out or first launch. Creating new anonymous session...
[log] >>> Auth Decision Tree Start: AuthUser=mYcsEOCssvL5oaYkprIjWKCwn65c, AppUser=null
[log]   [Branch 2.1] New session detected for mYcsEOCssvL5oaYkprIjWKCwn65c. Initializing...
[log] <<< Auth Decision Tree End
[log] <<< Auth Decision Tree End

# 再次进入

[log] ✅ Firebase 模拟器 host: *************
[log] >>> Auth Decision Tree Start: AuthUser=mYcsEOCssvL5oaYkprIjWKCwn65c, AppUser=null
[log]   [Branch 2.1] New session detected for mYcsEOCssvL5oaYkprIjWKCwn65c. Initializing...

[log] <<< Auth Decision Tree End

# 谷歌登录

D/FirebaseAuth( 2465): Notifying id token listeners about user ( mYcsEOCssvL5oaYkprIjWKCwn65c ).
[log] >>> Auth Decision Tree Start: AuthUser=mYcsEOCssvL5oaYkprIjWKCwn65c, AppUser=mYcsEOCssvL5oaYkprIjWKCwn65c
[log]   [Branch 2.2.1] Same user session update for mYcsEOCssvL5oaYkprIjWKCwn65c.
[log] <<< Auth Decision Tree End

# 登出

[log] >>> Auth Decision Tree Start: AuthUser=null, AppUser=null
[log]   [Branch 1.2] Controlled sign-out or first launch. Creating new anonymous session...

D/FirebaseAuth( 2465): Notifying id token listeners about user ( zhaNg4LVmHYaPYYGS4nlIa4aIDYf ).
D/FirebaseAuth( 2465): Notifying auth state listeners about user ( zhaNg4LVmHYaPYYGS4nlIa4aIDYf ).
[log] >>> Auth Decision Tree Start: AuthUser=zhaNg4LVmHYaPYYGS4nlIa4aIDYf, AppUser=null
[log]   [Branch 2.1] New session detected for zhaNg4LVmHYaPYYGS4nlIa4aIDYf. Initializing...
[log] <<< Auth Decision Tree End
W/Firestore( 2465): (25.1.4) [Firestore]: Listen for Query(target=Query(users/mYcsEOCssvL5oaYkprIjWKCwn65c order by __name__);limitType=LIMIT_TO_FIRST) failed: Status{code=PERMISSION_DENIED, description=
W/Firestore( 2465): false for 'get' @ L15, cause=null}
[log] <<< Auth Decision Tree End

# 再次谷歌登录

[log] >>> Auth Decision Tree Start: AuthUser=mYcsEOCssvL5oaYkprIjWKCwn65c, AppUser=zhaNg4LVmHYaPYYGS4nlIa4aIDYf
[log] null
[log] [Branch 2.2.2] Continuing with new session initialization for mYcsEOCssvL5oaYkprIjWKCwn65c after cleanup.
[log] <<< Auth Decision Tree End