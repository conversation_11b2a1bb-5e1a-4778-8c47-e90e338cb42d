import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")
    // START: FlutterFire Configuration
    id("com.google.gms.google-services")
    // END: FlutterFire Configuration
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.taskivestudio.taskive"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }

    defaultConfig {
        applicationId = "com.taskivestudio.taskive"
        minSdk = 24
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    // 设置风味维度，用于区分不同构建版本
    flavorDimensions += "app"

    productFlavors {
        // 开发版本 "dev"
        create("dev") {
            dimension = "app"
            applicationIdSuffix = ".dev" // 包名后缀
            versionNameSuffix = "-dev"   // 版本名后缀
        }
        // 生产版本 "prod"
        create("prod") {
            dimension = "app"
            // prod 版本不添加任何后缀，使用 defaultConfig 中的默认值
        }
    }

    val keyPropertiesFile = rootProject.file("key.properties")
    val keyProperties = Properties()
    if (keyPropertiesFile.exists()) {
        keyProperties.load(FileInputStream(keyPropertiesFile))
    }

    signingConfigs {
        // debug 版本使用默认的调试版本密钥库
        create("release") {
            if (project.hasProperty("android.injected.signing.store.file")) {
                // 由 Google Play 注入密钥
                keyAlias = project.property("android.injected.signing.key.alias") as String
                keyPassword = project.property("android.injected.signing.key.password") as String
                storePassword = project.property("android.injected.signing.store.password") as String
                storeFile = project.file(project.property("android.injected.signing.store.file") as String)
            } else {
                // 本地注入密钥
                keyAlias = keyProperties.getProperty("keyAlias")
                keyPassword = keyProperties.getProperty("keyPassword")
                storePassword = keyProperties.getProperty("storePassword")
                storeFile = project.file(keyProperties.getProperty("storeFile"))
            }
        }
    }

    buildTypes {
        // debug 版本使用默认的调试版本密钥库
        getByName("release") {
            signingConfig = signingConfigs.getByName("release")

            isMinifyEnabled = true
            isShrinkResources = true

            proguardFiles.add(getDefaultProguardFile("proguard-android-optimize.txt"))
            proguardFiles.add(file("proguard-rules.pro"))
        }
    }
}

dependencies {
    // 邮箱链接登录的依赖
    implementation(platform("com.google.firebase:firebase-bom:33.1.0"))
    implementation("com.google.firebase:firebase-auth")
    implementation("com.google.android.gms:play-services-auth:21.1.0")
}

flutter {
    source = "../.."
}
