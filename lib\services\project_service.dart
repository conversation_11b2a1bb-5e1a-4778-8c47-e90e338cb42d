import 'dart:developer' as developer;
import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
// models
import '../models/project_model.dart';
// tools
import '../tools/config.dart';
import '../tools/explicit_value.dart';

class ProjectService {
  final FirebaseFirestore _firestore;
  final FirebaseFunctions _functions;

  /// ====== 私有状态 ======

  // Firestore 集合引用常量
  final String _usersCollection = Config.service.usersCollection;
  final String _projectsCollection = Config.service.projectsCollection;

  /// ====== 构造函数 ======
  
  ProjectService(this._firestore, this._functions);

  /// ====== 流监听 ======

  /// 获取该用户的项目实时流
  Stream<List<ProjectModel>> getProjectStream(String userId) {
    // 如果 userId 无效
    if (userId.isEmpty) return Stream.value([]);

    final collectionRef = _getProjectCollectionRef(userId);

    // 移除 try-catch 块，让解析错误能够冒泡到 Stream 的 onError 处理器
    // 这使得状态管理器可以更优雅地处理错误，而不是接收一个空的列表
    return collectionRef.orderBy('createTime', descending: true).limit(100).snapshots().map((snapshot) {
      if (snapshot.docs.isEmpty) return []; // 如果没有文档，返回空列表

      // 将 Firestore 文档映射为 ProjectModel 列表
      return snapshot.docs.map((doc) {
        try {
          final data = doc.data();
          data['id'] = doc.id; // 将文档 ID 添加到数据中
          return ProjectModel.fromDatabase(data);
        } catch (e) {
          developer.log('⛔ 解析项目文档 ${doc.id} 失败', error: e);
          return null; // 解析失败则返回 null
        }
      }).whereType<ProjectModel>().toList();
    });
  }

  /// ====== 网络请求 ======

  /// 创建项目（客户端直接写入以提高速度）
  Future<ProjectModel?> createProject(String userId, ProjectModel project) async {
    final collectionRef = _getProjectCollectionRef(userId);
    final data = project.toDatabase();

    // 添加系统字段
    data['userId'] = userId;
    data['createTime'] = FieldValue.serverTimestamp();
    data['updateTime'] = FieldValue.serverTimestamp();

    final docRef = await collectionRef.add(data);

    if (docRef.id.isNotEmpty) {
      return project.copyWith(
        id: ExplicitValue(docRef.id),
        userId: ExplicitValue(userId),
        // 虽然我们发送的是 serverTimestamp，但在UI上立即反映一个近似时间
        // 真实时间会在下次从 Firestore 读取时同步
        createTime: ExplicitValue(DateTime.now()),
        updateTime: ExplicitValue(DateTime.now()),
      );
    }

    return null;
  }

  /// 更新项目（客户端直接写入以提高速度）
  Future<void> updateProject(String userId, ProjectModel project) async {
    final docRef = _getProjectCollectionRef(userId).doc(project.id);
    final data = project.toDatabase();

    // 添加系统字段
    data['updateTime'] = FieldValue.serverTimestamp();
    
    await docRef.update(data);
  }

  /// 仅删除项目（保留在云函数以保证事务性）
  Future<void> deleteProjectOnly(String projectId) async {
    final callable = _functions.httpsCallable(Config.service.deleteProjectOnlyFn);
      
    await callable.call({'projectId': projectId});
  }

  /// 删除项目及其所有关联的任务（保留在云函数以保证事务性）
  Future<void> deleteProjectWithTasks(String projectId) async {
    final callable = _functions.httpsCallable(Config.service.deleteProjectWithTasksFn);
      
    await callable.call({'projectId': projectId});
  }

  /// ====== 工具方法 ======

  // 内部方法：获取该用户的项目子集合引用
  CollectionReference<Map<String, dynamic>> _getProjectCollectionRef(String userId) {
    return _firestore.collection(_usersCollection).doc(userId).collection(_projectsCollection);
  }
}
