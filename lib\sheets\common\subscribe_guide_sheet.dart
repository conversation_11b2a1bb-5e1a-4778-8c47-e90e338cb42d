// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// services
import '../../services/permission_service.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class SubscribeGuideSheet extends StatelessWidget {
  final Feature feature;

  const SubscribeGuideSheet({
    super.key,
    required this.feature,
  });

  static void show(BuildContext context, {required Feature feature}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true, // 允许内容决定高度
      builder: (context) => SubscribeGuideSheet(feature: feature),
    );
  }

  // 根据功能特性获取对应的文案
  ({String title, String currentLimit, String premiumBenefit}) _getFeatureDetails(BuildContext context) {
    final limits = PermissionService.getFeatureLimits(feature);
    final l10n = AppLocalizations.of(context)!;
    switch (feature) {
      case Feature.project:
        return (
          title: l10n.subscribeGuideSheet_projectTitle,
          currentLimit: l10n.subscribeGuideSheet_projectLimit(limits.free),
          premiumBenefit: l10n.subscribeGuideSheet_projectBenefit(limits.premium)
        );
      case Feature.recurring:
        return (
          title: l10n.subscribeGuideSheet_recurringTitle,
          currentLimit: l10n.subscribeGuideSheet_recurringLimit(limits.free),
          premiumBenefit: l10n.subscribeGuideSheet_recurringBenefit(limits.premium)
        );
      case Feature.timer:
        return (
          title: l10n.subscribeGuideSheet_timerTitle,
          currentLimit: l10n.subscribeGuideSheet_timerLimit(limits.free),
          premiumBenefit: l10n.subscribeGuideSheet_timerBenefit
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    final details = _getFeatureDetails(context);
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final l10n = AppLocalizations.of(context)!;

    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 高度由内容决定
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 标题
          Text(
            details.title,
            style: textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          // 描述
          Text(
            '${details.currentLimit} ${details.premiumBenefit}',
            textAlign: TextAlign.center,
            style: textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
          ),
          const SizedBox(height: 24),
          // "升级" 按钮
          ElevatedButton.icon(
            icon: const Icon(LucideIcons.gem),
            label: Text(l10n.subscribeGuideSheet_upgradeButton),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              minimumSize: const Size(double.infinity, 50), // 宽度撑满，高度50
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onPressed: () async {
              try {
                // 关闭当前的 BottomSheet
                if (context.mounted) Navigator.of(context).pop();
                // 显示 RevenueCat 付费墙
                await RevenueCatUI.presentPaywall();
              } catch (e) {
                 if (context.mounted) {
                   ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(l10n.subscribeGuideSheet_upgradeError(e.toString()))),
                  );
                 }
              }
            },
          ),
          const SizedBox(height: 8),
          // "以后再说" 按钮
          TextButton(
            child: Text(l10n.subscribeGuideSheet_laterButton),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }
}
