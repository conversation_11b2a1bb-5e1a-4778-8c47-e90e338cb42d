// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// states
import '../../states/project_state.dart';
// tools
import '../../tools/config.dart';
// widgets
import '../../widgets/project/project_card.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class ArchivedProjectsPage extends StatelessWidget {
  const ArchivedProjectsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.archivedProjectsPage_title),
      ),
      // 使用 Consumer 获取 ProjectState
      body: Consumer<ProjectState>(
        builder: (context, projectState, _) {
          // 过滤出已归档的项目
          final archivedProjects = projectState.projects.where((p) => p.isArchived).toList();

          // 如果没有归档项目，显示提示
          if (archivedProjects.isEmpty) {
            return Center(child: Text(l10n.archivedProjectsPage_emptyMessage));
          }

          // 使用 ListView 显示归档项目
          return ListView.builder(
            itemCount: archivedProjects.length + 1, // 增加一项用于底部空白
            itemBuilder: (context, index) {
              if (index == archivedProjects.length) {
                // 最后一项是底部空白
                return SizedBox(height: Config.app.bottomSpace);
              }
              final project = archivedProjects[index];
              // 使用 ProjectCard 显示项目
              return ProjectCard(project: project);
            },
          );
        },
      ),
    );
  }
}
