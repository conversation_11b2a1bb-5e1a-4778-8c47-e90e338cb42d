// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart' show ScrollDirection;
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';
// models
import '../models/task_model.dart';
// states
import '../states/task_state.dart';
import '../states/setting_state.dart';
// tools
import '../tools/config.dart';
import '../tools/extensions.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/date_grouped_tasks.dart';
// others
import '../generated/l10n/app_localizations.dart';

class CalendarListView extends StatefulWidget {
  const CalendarListView({super.key});

  @override
  State<CalendarListView> createState() => _CalendarListViewState();
}

class _CalendarListViewState extends State<CalendarListView> {
  DateTime _focusedDay = DateTime.now();
  DateTime _selectedDay = DateTime.now();
  CalendarFormat _calendarFormat = CalendarFormat.week; // 默认显示周视图
  bool _isCalendarExpanded = false;
  List<TaskModel> _selectedDayTasks = [];

  // 用于监听滚动
  final ScrollController _scrollController = ScrollController();
  bool _isScrollingUp = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer2<TaskState, SettingState>(
      builder: (context, taskState, settingState, _) {
        // 获取选中日期的任务
        _updateSelectedDayTasks(taskState, _selectedDay);

        // 获取用户设置的一周的开始日
        final weekStartsOnSunday = settingState.weekStartDay == 7;
        final startingDayOfWeek = weekStartsOnSunday ? 
            StartingDayOfWeek.sunday : StartingDayOfWeek.monday;
            
        // 根据当前月份生成标题
        final now = DateTime.now();
        final isCurrentYear = _focusedDay.year == now.year;
        final titleText = isCurrentYear 
            ? l10n.calendarListView_titleCurrentYear(_focusedDay)
            : l10n.calendarListView_titleOtherYear(_focusedDay);
            
        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(titleText),
            actions: [
              // 回到今天按钮
              TextButton(
                onPressed: () {
                  setState(() {
                    _focusedDay = DateTime.now();
                    _selectedDay = DateTime.now();
                    _updateSelectedDayTasks(taskState, _selectedDay);
                  });
                },
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.primary,
                ),
                child: Text(l10n.common_today),
              ),
            ],
          ),
          body: GestureDetector(
            onVerticalDragUpdate: _handleDragUpdate,
            onVerticalDragEnd: _handleDragEnd,
            child: Column(
              children: [
                // 日历部分
                TableCalendar<TaskModel>(
                  // 不显示日历头部（就是 titleText、formatButton、leftChevron、rightChevron 那一行）
                  headerVisible: false,
                  firstDay: Config.app.calendarFirstDay,
                  lastDay: Config.app.calendarLastDay,
                  focusedDay: _focusedDay,
                  selectedDayPredicate: (day) => _selectedDay.isSameDay(day),
                  calendarFormat: _calendarFormat,
                  eventLoader: (day) => taskState.get1DayTasks(day),
                  startingDayOfWeek: startingDayOfWeek,
                  availableCalendarFormats: const {
                    CalendarFormat.week: "Week",
                    CalendarFormat.month: "Month",
                  },
                  onFormatChanged: (format) {
                    setState(() {
                      _calendarFormat = format;
                      _isCalendarExpanded = format == CalendarFormat.month;
                    });
                  },
                  onDaySelected: (selectedDay, focusedDay) {
                    setState(() {
                      _selectedDay = selectedDay;
                      _focusedDay = focusedDay;
                      _updateSelectedDayTasks(taskState, selectedDay);
                    });
                  },
                  calendarStyle: CalendarStyle(
                    tablePadding: const EdgeInsets.symmetric(vertical: 10),
                    // margin 越大，数字圆圈越小
                    cellMargin: const EdgeInsets.all(8),
                    markersMaxCount: 1,
                    markerSize: 4,
                    markerMargin: const EdgeInsets.only(top: 7),
                    markerDecoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                    todayDecoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    selectedDecoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                    todayTextStyle: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.bold,
                      fontSize: 13,
                    ),
                    selectedTextStyle: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                      fontSize: 13,
                    ),
                  ),
                ),
                
                // 任务列表部分
                Expanded(
                  child: _selectedDayTasks.isEmpty
                  ? Center(
                      child: Text(
                        l10n.calendarListView_emptyMessage,
                        style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
                      )
                    )
                  : CustomScrollView(
                      controller: _scrollController,
                      slivers: [
                        DateGroupedTasks(tasks: _selectedDayTasks),
                      ],
                    ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 更新选中日期的任务列表
  void _updateSelectedDayTasks(TaskState taskState, DateTime day) {
    _selectedDayTasks = taskState.get1DayTasks(day);
  }

  void _onScroll() {
    // 监听滚动方向
    if (_scrollController.position.userScrollDirection == ScrollDirection.reverse) {
      // 向下滚动，收起日历
      if (_isCalendarExpanded) {
        setState(() {
          _calendarFormat = CalendarFormat.week;
          _isCalendarExpanded = false;
        });
      }
    } else if (_scrollController.position.userScrollDirection == ScrollDirection.forward) {
      // 向上滚动到顶部时，设置一个标志准备展开日历
      if (_scrollController.position.pixels <= 0 && !_isScrollingUp) {
        setState(() {
          _isScrollingUp = true;
        });
      }
    }
  }

  // 处理上拉手势展开日历的逻辑
  void _handleDragUpdate(DragUpdateDetails details) {
    if (_isScrollingUp && details.delta.dy > 0 && !_isCalendarExpanded) {
      // 只有在向下拖拽且日历未展开时才处理
      setState(() {
        _calendarFormat = CalendarFormat.month;
        _isCalendarExpanded = true;
        _isScrollingUp = false;
      });
    }
  }

  // 手势结束时重置标志
  void _handleDragEnd(DragEndDetails details) {
    setState(() {
      _isScrollingUp = false;
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }
}