// import 'dart:developer' as developer;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
// tools
import '../tools/config.dart';
import '../tools/explicit_value.dart';

/// ⚠️ 这段注释不要删除：
/// ⚠️ 如果 Models 字段有改动
/// ⚠️ 请提醒我同步更新 Cloud Functions 和 Firebase Rules

class TimerModel extends Equatable {
  final String id;
  final String userId;
  final String taskId;
  final DateTime createTime;         // 创建时间
  final DateTime updateTime;         // 更新时间 (指 timer 本身数据的更新)
  final String timezone;             // 时区
  final DateTime startTime;          // 计时开始时间
  final DateTime endTime;            // 计时结束时间
  final bool isRunning;

  const TimerModel({
    required this.id,
    required this.userId,
    required this.taskId,
    required this.createTime,
    required this.updateTime,
    required this.timezone,
    required this.startTime,
    required this.endTime,
    this.isRunning = false,
  });

  // 判断任务是否已完成
  int get durationSeconds => endTime.difference(startTime).inSeconds;

  TimerModel copyWith({
    ExplicitValue<String>? id,
    ExplicitValue<String>? userId,
    ExplicitValue<String>? taskId,
    ExplicitValue<DateTime>? createTime,
    ExplicitValue<DateTime>? updateTime,
    ExplicitValue<String>? timezone,
    ExplicitValue<DateTime>? startTime,
    ExplicitValue<DateTime>? endTime,
    ExplicitValue<bool>? isRunning,
  }) {
    return TimerModel(
      id: id == null ? this.id : id.value,
      userId: userId == null ? this.userId : userId.value,
      taskId: taskId == null ? this.taskId : taskId.value,
      createTime: createTime == null ? this.createTime : createTime.value,
      updateTime: updateTime == null ? this.updateTime : updateTime.value,
      timezone: timezone == null ? this.timezone : timezone.value,
      startTime: startTime == null ? this.startTime : startTime.value,
      endTime: endTime == null ? this.endTime : endTime.value,
      isRunning: isRunning == null ? this.isRunning : isRunning.value,
    );
  }

  // json to model
  factory TimerModel.fromDatabase(Map<String, dynamic> json) {
    return TimerModel(
      id: json['id'] as String? ?? '',
      userId: json['userId'] as String? ?? '',
      taskId: json['taskId'] as String? ?? '',
      createTime: (json['createTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updateTime: (json['updateTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      timezone: json['timezone'] as String? ?? Config.app.defaultTimezone,
      startTime: (json['startTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      endTime: (json['endTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isRunning: json['isRunning'] as bool? ?? false,
    );
  }

  // model to json
  Map<String, dynamic> toDatabase() {
    return {
      'taskId': taskId,
      'timezone': timezone,
      'startTime': Timestamp.fromDate(startTime),
      'endTime': Timestamp.fromDate(endTime),
      'isRunning': isRunning,
    };
  }

  // Equatable 需要比较的属性
  @override
  List<Object?> get props => [
    id,
    userId,
    taskId,
    createTime,
    updateTime,
    timezone,
    startTime,
    endTime,
    isRunning,
  ];
}
