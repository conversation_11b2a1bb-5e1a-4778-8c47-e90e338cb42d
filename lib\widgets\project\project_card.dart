// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/project_model.dart';
// states
import '../../states/task_state.dart';
// sheets
import '../../sheets/project/project_longpress_sheet.dart';
// views
import '../../views/project_view.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class ProjectCard extends StatelessWidget {
  final ProjectModel project;

  const ProjectCard({
    super.key,
    required this.project,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer<TaskState>(
      builder: (context, taskState, _) {
        // 获取该项目的任务数据
        final projectTasks = taskState.getProjectTasks(project.id);
        final completedTasks = projectTasks.where((task) => task.isCompleted).length;
        final totalTasks = projectTasks.length;
        
        // 计算完成率
        final completionRate = totalTasks > 0 ? completedTasks / totalTasks : 0.0;
        
        // 计算百分比文本
        final percentText = '${(completionRate * 100).toStringAsFixed(0)}%';
        
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0),
          child: InkWell(
            onTap: () {
              HapticFeedback.lightImpact();
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ProjectView(projectId: project.id),
                ),
              );
            },
            onLongPress: () {
              HapticFeedback.lightImpact();
              // 调用静态 show 方法
              ProjectLongPressSheet.show(context, project);
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 项目名称和颜色标识
                  Row(
                    children: [
                      Container(
                        width: 14,
                        height: 14,
                        decoration: BoxDecoration(
                          color: project.color,
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          project.name,
                          style: Theme.of(context).textTheme.titleMedium,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (project.isArchived)
                        Icon(LucideIcons.archive, color: Theme.of(context).colorScheme.onSurfaceVariant, size: 16),
                    ],
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // 进度条和百分比文本
                  Row(
                    children: [
                      Expanded(
                        child: LinearProgressIndicator(
                          value: completionRate,
                          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                          color: project.color,
                          minHeight: 6,
                          borderRadius: BorderRadius.circular(2.0),
                        ),
                      ),
                      // 使用Intrinsic宽度确保百分比文本完整显示
                      const SizedBox(width: 8),
                      Text(
                        percentText,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 4),
                  
                  // 完成情况
                  Text(
                    l10n.projectView_taskCompletedRate(completedTasks, totalTasks),
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
