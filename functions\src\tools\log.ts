import * as logger from 'firebase-functions/logger';
import type { RecurringModel } from './types.js';

/**
 * 统一的错误处理和日志记录
 */
export function handleRecurringError(
  operation: string,
  userId: string,
  recurringId: string,
  error: unknown
): void {
  const e = error as Error;
  logger.error(`${operation}时出错`, {
    userId,
    recurringId,
    error: e.message,
    stack: e.stack
  });
}

/**
 * 统一的参数验证
 */
export function validateRecurringData(
  recurring: RecurringModel | undefined,
  userId: string,
  recurringId: string
): boolean {
  if (!recurring || !recurring.rule) {
    logger.error("周期任务数据无效或缺少规则", {
      userId,
      recurringId,
      dataExists: !!recurring
    });
    return false;
  }
  return true;
}

/**
 * 记录性能指标
 */
export function recordMetrics(operation: string, duration: number, count: number): void {
  logger.info(`${operation} 性能指标`, {
    operation,
    duration_ms: duration,
    processed_count: count,
    avg_per_item: duration / count
  });
}
