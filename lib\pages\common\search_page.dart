// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/task_model.dart';
// states
import '../../states/task_state.dart';
// widgets
import '../../widgets/task/task_card.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class SearchPage extends StatefulWidget {
  const SearchPage({super.key});

  @override
  State<SearchPage> createState() => _SearchPageState();
}

class _SearchPageState extends State<SearchPage> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearchDataLoad = false; // 标记是否已加载搜索数据
  String _searchTerm = '';

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_updateSearchTermState);
    _loadSearchDataIfNeeded();
  }

  // 当 controller 文本变化时，触发组件重建
  void _updateSearchTermState() {
    final searchTerm = _searchController.text.trim();
    if (_searchTerm != searchTerm) {
      setState(() {
        _searchTerm = searchTerm;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return Consumer<TaskState>(
      builder: (context, taskState, _) {
        // --- 从 TaskState 获取数据源和加载状态 ---
        final List<TaskModel>? dataSource = taskState.searchDataSource;
        final String? error = taskState.searchDataError;

        // --- 使用本地 _searchTerm 和 dataSource 进行过滤 ---
        final List<TaskModel> filteredResults;
        if (_searchTerm.isEmpty || dataSource == null) {
          filteredResults = [];
        } else {
          final lowerCaseSearchTerm = _searchTerm.toLowerCase();
          filteredResults = dataSource.where((task) {
            final titleMatch = task.name.toLowerCase().contains(lowerCaseSearchTerm);
            return titleMatch;
          }).toList();
        }
        
        return Scaffold(
          appBar: AppBar(
            title: Text(l10n.searchPage_title),
          ),
          body: Column(
            children: [
              // 搜索框
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: l10n.searchPage_hintText,
                    suffixIcon: _searchTerm.isNotEmpty 
                        ? IconButton(
                            icon: const Icon(LucideIcons.x, size: 20),
                            onPressed: () {
                              // 清除输入框
                              _searchController.clear();
                            },
                          )
                        : Padding(
                           padding: const EdgeInsets.all(12.0),
                           child: Icon(LucideIcons.search, size: 20, color: colorScheme.onSurfaceVariant),
                         ),
                     border: const UnderlineInputBorder(),
                     contentPadding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 4.0),
                  ),
                  autofocus: true,
                ),
              ),
              
              // 搜索结果区域
              Expanded(
                child: Builder(
                  builder: (context) {
                    // 1. 加载错误状态
                    if (error != null && dataSource == null) {
                       return Center(
                         child: Padding(
                           padding: const EdgeInsets.all(16.0),
                           child: Text(
                             error,
                             style: TextStyle(color: colorScheme.error),
                             textAlign: TextAlign.center,
                           ),
                         ),
                       );
                    }
                    
                    // 2. 搜索词为空
                    if (_searchTerm.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(LucideIcons.search, size: 80, color: colorScheme.onSurfaceVariant),
                            const SizedBox(height: 16),
                            Text(
                              l10n.searchPage_noKeywordMessage,
                              style: TextStyle(color: colorScheme.onSurfaceVariant),
                            ),
                          ],
                        ),
                      );
                    }

                    // 3. 无搜索结果 (使用本地计算的 filteredResults)
                    if (filteredResults.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(LucideIcons.searchX, size: 80, color: colorScheme.onSurfaceVariant),
                            const SizedBox(height: 16),
                            Text(
                              l10n.searchPage_noResultFound(_searchTerm),
                              style: TextStyle(color: colorScheme.onSurfaceVariant),
                            ),
                          ],
                        ),
                      );
                    }

                    // 4. 显示搜索结果列表 (使用本地计算的 filteredResults)
                    return ListView.builder(
                      itemCount: filteredResults.length,
                      itemBuilder: (context, index) {
                        return TaskCard(task: filteredResults[index]);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      }
    );
  }

  // 尝试获取搜索数据
  void _loadSearchDataIfNeeded() {
     final taskState = Provider.of<TaskState>(context, listen: false);
     if (!_isSearchDataLoad && taskState.searchDataSource == null) {
        taskState.searchTasksFake(); 
        _isSearchDataLoad = true;
     }
  }

  @override
  void dispose() {
    _searchController.removeListener(_updateSearchTermState); 
    _searchController.dispose();
    super.dispose();
  }
}
