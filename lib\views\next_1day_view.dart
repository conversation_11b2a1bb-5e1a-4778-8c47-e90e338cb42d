// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// states
import '../states/task_state.dart';
// tools
import '../tools/config.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/today_glance.dart';
import '../widgets/task/task_card.dart';
// others
import '../generated/l10n/app_localizations.dart';

class Next1DayView extends StatelessWidget {
  const Next1DayView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Consumer<TaskState>(
      builder: (context, taskState, _) {
        // 获取1天任务
        final next1dayTasks = taskState.getNextDaysTasks(1);
        
        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(l10n.next1DayView_title),
          ),
          body: CustomScrollView(
            slivers: [
              // 今日一览
              SliverToBoxAdapter(child: TodayGlance()),
              
              // 添加间距
              const SliverToBoxAdapter(child: Si<PERSON><PERSON><PERSON>(height: 8)),
              
              // 任务列表
              if (next1dayTasks.isEmpty)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(child: Text(l10n.next1DayView_emptyMessage)),
                )
              else
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      return TaskCard(task: next1dayTasks[index]);
                    },
                    childCount: next1dayTasks.length,
                  ),
                ),
              
              // 在最后添加一个底部空间
              // 列表为空时不添加底部空间，否则可以滚动，不符合预期
              if (next1dayTasks.isNotEmpty)
                SliverToBoxAdapter(
                  child: SizedBox(height: Config.app.bottomSpace),
                ),
            ],
          ),
        );
      },
    );
  }
}
