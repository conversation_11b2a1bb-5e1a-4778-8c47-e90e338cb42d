// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/task_model.dart';
// states
import '../../states/task_state.dart';
// tools
import '../../tools/explicit_value.dart';
import '../../tools/app_exception.dart';
import '../../tools/app_exception_mapper.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
import '../../widgets/common/base_alert_dialog.dart';
import '../../widgets/common/app_exception_snackbar.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TaskLongPressSheet extends StatefulWidget {
  final TaskModel task;

  const TaskLongPressSheet._({
    required this.task,
  });

  // 公共的静态 show 方法
  static void show(BuildContext context, TaskModel task) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return TaskLongPressSheet._(task: task);
      },
    );
  }

  @override
  State<TaskLongPressSheet> createState() => _TaskLongPressSheetState();
}

class _TaskLongPressSheetState extends State<TaskLongPressSheet> {
  bool _isDeleting = false;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;
    final task = widget.task;
    final isLoading = _isDeleting;

    return BaseBottomSheet(
      fullWidth: true,
      child: Wrap(
        children: <Widget>[
          // 丢弃任务选项 (仅当任务未完成且未丢弃时显示)
          if (!task.isCompleted && !task.isDropped)
            ListTile(
              leading: const Icon(LucideIcons.undo),
              title: Text(l10n.taskLongPressSheet_drop),
              subtitle: Text(l10n.taskLongPressSheet_dropDesc),
              visualDensity: const VisualDensity(vertical: -4),
              onTap: isLoading ? null : () => _handleDropTask(task, DateTime.now()),
            ),

          // 恢复任务选项 (仅当任务已丢弃时显示)
          if (task.isDropped)
            ListTile(
              leading: const Icon(LucideIcons.redo),
              title: Text(l10n.taskLongPressSheet_restore),
              subtitle: Text(l10n.taskLongPressSheet_restoreDesc),
              visualDensity: const VisualDensity(vertical: -4),
              onTap: isLoading ? null : () => _handleDropTask(task, null),
            ),

          if (!task.isCompleted || task.isDropped) const Divider(),

          // 删除任务选项
          ListTile(
            leading: _isDeleting
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: colorScheme.error,
                  ),
                )
              : Icon(LucideIcons.trash2, color: colorScheme.error),
            title: Text(
              l10n.taskLongPressSheet_delete,
              style: TextStyle(color: colorScheme.error),
            ),
            subtitle: Text(l10n.taskLongPressSheet_deleteDesc),
            visualDensity: const VisualDensity(vertical: -4),
            onTap: isLoading ? null : () => _confirmDeleteTask(context),
          ),
        ],
      ),
    );
  }

  Future<void> _handleDropTask(TaskModel task, DateTime? dropTime) async {
    HapticFeedback.lightImpact();

    final taskState = Provider.of<TaskState>(context, listen: false);
    final navigator = Navigator.of(context);

    final updatedTask = task.copyWith(dropTime: ExplicitValue(dropTime));

    try {
      await taskState.updateTask(updatedTask);
      if (mounted) navigator.pop();
    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.updateTask,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    }
  }

  void _confirmDeleteTask(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    HapticFeedback.lightImpact();

    showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return BaseAlertDialog(
          title: l10n.taskLongPressSheet_confirmDeleteTitle,
          content: l10n.taskLongPressSheet_confirmDeleteContent(widget.task.name),
          actions: <Widget>[
            TextButton(
              child: Text(l10n.common_cancel),
              onPressed: () {
                Navigator.of(dialogContext).pop(false);
              },
            ),
            TextButton(
              style: TextButton.styleFrom(foregroundColor: colorScheme.error),
              child: Text(l10n.common_confirmDelete),
              onPressed: () {
                Navigator.of(dialogContext).pop(true);
              },
            ),
          ],
        );
      },
    ).then((confirmed) {
      if (confirmed == true) {
        _handleDeleteTask();
      }
    });
  }

  Future<void> _handleDeleteTask() async {
    if (!mounted) return;
    setState(() => _isDeleting = true);

    final taskState = Provider.of<TaskState>(context, listen: false);
    final navigator = Navigator.of(context);

    try {
      await taskState.deleteTask(widget.task.id);
      if (mounted) navigator.pop();
    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.deleteTask,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    } finally {
      if (mounted) {
        setState(() => _isDeleting = false);
      }
    }
  }
}
