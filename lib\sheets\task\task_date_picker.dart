// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// tools
import '../../tools/config.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TaskDatePicker extends StatelessWidget {
  final DateTime? selectedDate;
  final Function(DateTime?) onSelected;

  const TaskDatePicker({
    super.key,
    required this.selectedDate,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final String displayDateText = selectedDate == null
        ? l10n.taskDatePicker_noDate
        : _formatDateWithWeekday(selectedDate!, l10n);
    final bool showClearButton = selectedDate != null;

    return InkWell(
      // 当有重复规则时禁用点击
      onTap: () {
        // 增加震动反馈
        HapticFeedback.lightImpact();

        _showDatePicker(context);
      },
      child: Row(
        children: [
          // 左侧图标
          Icon(
            LucideIcons.calendar,
            size: 20,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 24),

          Expanded(
            child: Text(
              displayDateText,
              style: TextStyle(fontSize: 16),
            ),
          ),

          if (showClearButton)
            // 使用 InkWell 包裹 Icon 来创建一个自定义的、无额外边距的点击效果
            // 这样可以避免 IconButton 默认的最小点击区域（48x48）撑大父组件的高度
            InkWell(
              // 设置圆形的水波纹效果，使其看起来更像一个按钮
              customBorder: const CircleBorder(),
              onTap: () => onSelected(null),
              child: const Padding(
                // 给图标添加一些内边距，以扩大点击区域，同时不影响外部布局
                padding: EdgeInsets.symmetric(horizontal: 4.0),
                child: Icon(
                  LucideIcons.x,
                  size: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showDatePicker(BuildContext context) async {
    final DateTime? pickedDate = await showModalBottomSheet<DateTime>(
      context: context,
      isScrollControlled: true,
      builder: (context) => _buildTaskDateSheet(context),
    );
    
    if (pickedDate != null) {
      onSelected(pickedDate);
    }
  }

  Widget _buildTaskDateSheet(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BaseBottomSheet(
      fullWidth: true,
      child: ListView(
        shrinkWrap: true,
        children: [
          // 自适应的快捷选择按钮
          Padding(
            padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 8.0),
            child: Row(
              children: [
                // 使用Expanded让每个按钮平分可用空间
                Expanded(
                  child: _buildQuickButton(context, _getTodayLabel(l10n), _getToday()),
                ),
                const SizedBox(width: 8), // 按钮间距
                Expanded(
                  child: _buildQuickButton(context, _getTomorrowLabel(l10n), _getTomorrow()),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildQuickButton(context, l10n.taskDatePicker_nextMonday, _getNextMonday()),
                ),
              ],
            ),
          ),

          const Divider(height: 32),

          // 修改日历选择器
          CalendarDatePicker(
            initialDate: selectedDate,
            firstDate: Config.app.calendarFirstDay,
            lastDate: Config.app.calendarLastDay,
            onDateChanged: (date) {
              Navigator.pop(context, date);
            },
          ),
        ],
      ),
    );
  }

  // 自适应大小的快捷按钮
  Widget _buildQuickButton(BuildContext context, String label, DateTime date) {
    final colorScheme = Theme.of(context).colorScheme;
    return SizedBox(
      height: 36, // 固定高度使按钮更高
      child: ElevatedButton(
        onPressed: () {
          // 增加震动反馈
          HapticFeedback.lightImpact();
          Navigator.pop(context, date);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          padding: EdgeInsets.zero, // 移除内边距，最大化按钮大小
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: colorScheme.onPrimary,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  // 快捷日期标签
  String _getTodayLabel(AppLocalizations l10n) {
    final now = DateTime.now();
    final weekday = _getWeekdayName(now.weekday, l10n);
    return '${l10n.common_today} $weekday';
  }

  String _getTomorrowLabel(AppLocalizations l10n) {
    final tomorrow = _getTomorrow();
    final weekday = _getWeekdayName(tomorrow.weekday, l10n);
    return '${l10n.common_tomorrow} $weekday';
  }

  // 快捷日期计算方法
  DateTime _getToday() {
    return DateTime.now();
  }

  DateTime _getTomorrow() {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day + 1);
  }

  DateTime _getNextMonday() {
    final now = DateTime.now();
    // 计算到下周一的天数
    int daysUntilNextMonday = DateTime.monday - now.weekday;
    if (daysUntilNextMonday <= 0) {
      // 如果今天是周一或之后，则加7天到下周一
      daysUntilNextMonday += 7;
    }
    return DateTime(now.year, now.month, now.day + daysUntilNextMonday);
  }

  String _getWeekdayName(int weekday, AppLocalizations l10n) {
    final weekdays = l10n.common_weekdays.split(',');
    return weekdays[weekday - 1];
  }

  String _formatDateWithWeekday(DateTime date, AppLocalizations l10n) {
    // 格式化日期部分为 "yyyy-MM-dd"
    final String dateString = DateFormat('yyyy-MM-dd').format(date);
    // 获取星期几的本地化名称，例如 "周四"
    final String weekdayString = _getWeekdayName(date.weekday, l10n);
    // 组合成最终的字符串 "yyyy-MM-dd 周X"
    return '$dateString $weekdayString';
  }
}
