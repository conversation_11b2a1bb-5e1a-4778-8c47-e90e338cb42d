// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:fl_chart/fl_chart.dart';
// models
import '../../models/recurring_model.dart';
// states
import '../../states/timer_state.dart';
import '../../states/task_state.dart';
import '../../states/setting_state.dart';
import '../../states/recurring_state.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class HabitTimerChart extends StatelessWidget {
  final String recurringId;

  const HabitTimerChart({
    super.key,
    required this.recurringId,
  });
  
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Consumer4<TimerNotifier, TaskState, SettingState, RecurringState>(
      builder: (context, timerNotifier, taskState, settingState, recurringState, _) {
        final monthlyData = _getMonthlyTaskDueDateTimerData(
          recurringId,
          taskState,
          timerNotifier,
        );

        // 获取习惯模板
        RecurringModel? habit; 
        try {
          habit = recurringState.recurrings.firstWhere(
            (rec) => rec.id == recurringId,
          );
        } catch (e) {
          habit = null;
        }
        
        if (habit == null) {
          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Center(child: Text(l10n.habitTaskChart_error)),
            ),
          );
        }

        final now = DateTime.now();
        final daysInMonth = DateTime(now.year, now.month + 1, 0).day;

        double maxY = 0;
        if (monthlyData.isNotEmpty) {
          maxY = monthlyData.values.reduce((curr, next) => curr > next ? curr : next);
        }
        maxY = (maxY < 10) ? 10 : ((maxY / 10).ceil() * 10.0);
        if (monthlyData.isEmpty || maxY == 0) maxY = 60; // 如果没有数据或最大值为0，默认60分钟
        final double yInterval = (maxY / 5).toDouble();

        final List<String> bottomTitlesList = List.generate(daysInMonth, (index) => (index + 1).toString());

        final BarChartData barChartData = BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: maxY,
          minY: 0,
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: yInterval,
            getDrawingHorizontalLine: (value) => FlLine(color: theme.dividerColor, strokeWidth: 0.5),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border(
              bottom: BorderSide(color: theme.dividerColor, width: 1.0),
              left: BorderSide(color: theme.dividerColor, width: 1.0),
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 16,
                interval: yInterval,
                getTitlesWidget: (double value, TitleMeta meta) {
                  if (value == 0 && maxY > 0) return Container();
                  if (value == maxY && maxY > 0) return Container();
                  return Text(
                    value.toInt().toString(),
                    style: textTheme.labelSmall,
                    textAlign: TextAlign.left,
                  );
                },
              ),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 28,
                interval: 1,
                getTitlesWidget: (double value, TitleMeta meta) {
                  final index = value.toInt();
                  if (index < 0 || index >= bottomTitlesList.length) return Container();
                  if (daysInMonth > 10 && index % 5 != 0 && index != bottomTitlesList.length - 1 && index != 0) {
                     return Container();
                  }
                  final text = bottomTitlesList[index];
                  return Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(text, style: textTheme.labelSmall),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          barGroups: List.generate(daysInMonth, (index) {
            final dayOfMonth = index + 1; // 日期 1 到 daysInMonth
            final minutes = monthlyData[dayOfMonth] ?? 0.0;
            const double barWidth = 7;
            return BarChartGroupData(
              x: index, // X 轴坐标 0 到 daysInMonth-1
              barRods: [
                BarChartRodData(
                  toY: minutes,
                  color: colorScheme.primary,
                  width: barWidth,
                  borderRadius: const BorderRadius.only(topLeft: Radius.circular(2), topRight: Radius.circular(2)),
                ),
              ],
            );
          }),
          barTouchData: BarTouchData(enabled: false),
        );

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Padding(
            padding: const EdgeInsets.only(top: 12, bottom: 12, left: 12, right: 16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 10.0),
                  child: Text(
                    l10n.statModel_habitTimerName(habit.template.name),
                    style: textTheme.titleMedium?.copyWith(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                AspectRatio(
                  aspectRatio: 1.7,
                  child: BarChart(
                    barChartData,
                    duration: const Duration(milliseconds: 250),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        l10n.habitTimerChart_note,
                        style: textTheme.labelSmall,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Map<int, double> _getMonthlyTaskDueDateTimerData(
    String targetRecurringId,
    TaskState taskState,
    TimerNotifier timerNotifier,
  ) {
    final Map<int, double> dailyTotalMinutes = {};

    final now = DateTime.now();
    final thisMonthStart = DateTime(now.year, now.month, 1);
    final nextMonthStart = DateTime(now.year, now.month + 1, 1);
    
    // 1. 过滤出与目标习惯相关且截止时间在本月的任务
    final relevantTasksThisMonth = taskState.tasks.where((task) {
      if (task.recurringId != targetRecurringId) return false;

      final DateTime? deadline = task.deadlineTime;

      if (deadline == null) return false;

      return !deadline.isBefore(thisMonthStart) && deadline.isBefore(nextMonthStart);
    }).toList();

    if (relevantTasksThisMonth.isEmpty) {
      return dailyTotalMinutes;
    }

    // 2. 获取这些任务的 ID
    final relevantTaskIds = relevantTasksThisMonth.map((task) => task.id).toSet();

    // 3. 创建一个从 taskId 到其总计时时长（分钟）的映射
    final Map<String, double> timerMinutesByTaskId = {};
    for (final timer in timerNotifier.state.timers) {
      if (relevantTaskIds.contains(timer.taskId)) {
        timerMinutesByTaskId.update(
          timer.taskId,
          (value) => value + (timer.durationSeconds / 60.0),
          ifAbsent: () => (timer.durationSeconds / 60.0),
        );
      }
    }

    // 4. 根据任务的截止日期（月份中的某一天）来累加总计时时长
    for (final task in relevantTasksThisMonth) {
      final totalMinutes = timerMinutesByTaskId[task.id];
      if (totalMinutes == null || totalMinutes == 0) continue;

      DateTime? deadline = task.deadlineTime;

      if (deadline != null) {
        final dayOfMonth = deadline.day;
        dailyTotalMinutes.update(
          dayOfMonth,
          (value) => value + totalMinutes,
          ifAbsent: () => totalMinutes,
        );
      }
    }

    return dailyTotalMinutes;
  }
}
