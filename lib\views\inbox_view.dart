// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// states
import '../states/task_state.dart';
// tools
import '../tools/config.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/task_card.dart';
// others
import '../generated/l10n/app_localizations.dart';

class InboxView extends StatelessWidget {
  const InboxView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer<TaskState>(
      builder: (context, taskState, _) {
        final inboxTasks = taskState.getInboxTasks();
        
        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(l10n.inboxView_title),
          ),
          body: CustomScrollView(
            slivers: [
              if (inboxTasks.isEmpty)
                SliverFillRemaining(
                  child: Center(child: Text(l10n.inboxView_emptyMessage)),
                )
              else
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      return TaskCard(task: inboxTasks[index]);
                    },
                    childCount: inboxTasks.length,
                  ),
                ),
              
              // 在最后添加一个底部空间
              // 列表为空时不添加底部空间，否则可以滚动，不符合预期
              if (inboxTasks.isNotEmpty)
                SliverToBoxAdapter(
                  child: SizedBox(height: Config.app.bottomSpace),
                ),
            ],
          ),
        );
      },
    );
  }
}
