// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
// models
import '../models/nav_model.dart';
// states
import '../states/nav_state.dart';
// tools
import '../tools/config.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
// others
import '../generated/l10n/app_localizations.dart';

class EntryView extends StatelessWidget {
  const EntryView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppHeaderBar(
        title: Text(l10n.entryView_title),
      ),
      // 使用 Consumer 监听 SettingState
      body: Consumer<NavState>(
        builder: (context, navState, _) {
          // 从 SettingState 获取所有可用的 NavModel (包括项目派生的)
          // 使用公共属性 allNavModels
          final allNavs = navState.allNavs.values.where((nav) => !nav.isFixed).toList();

          // 按类别分组 NavModel
          final navsByCategory = <NavCategory, List<NavModel>>{};
          for (final nav in allNavs) {
            navsByCategory.putIfAbsent(nav.category, () => []).add(nav);
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 按预定顺序显示类别
                _buildCategorySection(
                  context,
                  l10n.common_categoryTask,
                  navsByCategory[NavCategory.task] ?? [],
                ),
                const SizedBox(height: 24),
                _buildCategorySection(
                  context,
                  l10n.common_categoryTimer,
                  navsByCategory[NavCategory.timer] ?? [],
                ),
                const SizedBox(height: 24),
                _buildCategorySection(
                  context,
                  l10n.common_categoryProject,
                  navsByCategory[NavCategory.project] ?? [],
                ),
                const SizedBox(height: 24),
                _buildCategorySection(
                  context,
                  l10n.common_categoryFunction,
                  navsByCategory[NavCategory.function] ?? [],
                ),

                // 底部留白
                SizedBox(height: Config.app.bottomSpace),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCategorySection(
    BuildContext context,
    String title,
    List<NavModel> navs, // 直接接收 NavModel 列表
  ) {
    if (navs.isEmpty) {
      return const SizedBox.shrink(); // 如果该类别下没有项目，则不显示
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
        const SizedBox(height: 24),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(), // 阻止 GridView 自身滚动
          gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
            maxCrossAxisExtent: 200.0, // 每个子项的最大宽度
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 2.8 / 1, // 调整宽高比
          ),
          itemCount: navs.length,
          itemBuilder: (context, index) {
            // 直接传递 NavModel
            return _buildGridItem(context, navs[index]);
          },
        ),
      ],
    );
  }

  Widget _buildGridItem(
    BuildContext context,
    NavModel nav,
  ) {
    // 从 NavModel 获取信息
    final icon = nav.icon;
    final name = nav.name;
    final iconColor = nav.isProject ? nav.color : Theme.of(context).iconTheme.color;

    return GestureDetector(
      onTap: () => _navigateToView(context, nav),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: iconColor,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  name,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 辅助方法：导航到指定视图
  void _navigateToView(BuildContext context, NavModel nav) {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    // 直接使用 NavModel 中的 widgetBuilder 来构建目标视图
    Widget targetView = nav.page;

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => targetView),
    );
  }
}
