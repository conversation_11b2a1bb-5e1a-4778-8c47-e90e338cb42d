// import 'dart:developer' as developer;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:app_links/app_links.dart';
// states
import '../states/user_state.dart';
import '../states/nav_state.dart';
import '../states/stat_state.dart';
// tools
import '../tools/app_exception_mapper.dart';
// screens
import './home_screen.dart';
// others
import '../generated/l10n/app_localizations.dart';

class InitScreen extends StatefulWidget {
  const InitScreen({super.key});

  @override
  State<InitScreen> createState() => _InitScreenState();
}

class _InitScreenState extends State<InitScreen> with WidgetsBindingObserver {
  late AppLinks _appLinks;
  StreamSubscription<Uri>? _linkSubscription;
  bool _isInitialized = false; // 初始化锁

  @override
  void initState() {
    super.initState();
    _appLinks = AppLinks();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 使用锁来确保 _initDeepLinks 只会被调用一次
    if (!_isInitialized) {
      _isInitialized = true;
      _initDeepLinks();
    }
  }

  @override
  void dispose() {
    _linkSubscription?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // 'app_links' 会自动处理 
  }

  // 深度链接处理
  Future<void> _initDeepLinks() async {
    // 1. 处理应用冷启动时的初始链接
    try {
      final initialUri = await _appLinks.getInitialLink();
      if (initialUri != null) {
        _completeSignInWithEmailLink(initialUri);
      }
    } catch (_) {
      // 忽略获取初始链接的错误
    }

    // 2. 监听应用在后台时的新链接
    _linkSubscription = _appLinks.uriLinkStream.listen(
      (uri) => _completeSignInWithEmailLink(uri),
      onError: (_) {}, // 忽略监听错误
    );
  }

  Future<void> _completeSignInWithEmailLink(Uri uri) async {
    final userState = Provider.of<UserState>(context, listen: false);

    final emailLink = uri.toString();

    await userState.completeSignInWithEmailLink(emailLink);
  }

  @override
  Widget build(BuildContext context) {
    final userState = context.watch<UserState>();
    final l10n = AppLocalizations.of(context)!;

    switch (userState.status) {
      case UserAuthStatus.authenticating:
        return Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 24),
                Text(
                  l10n.initScreen_authenticatingTitle,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 12),
                Text(
                  l10n.initScreen_authenticatingDesc,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        );
      case UserAuthStatus.refreshingSession:
        return Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 24),
                Text(
                  l10n.initScreen_refreshingSessionTitle,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const SizedBox(height: 12),
                Text(
                  l10n.initScreen_refreshingSessionDesc,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        );
      case UserAuthStatus.error:
        final streamError = userState.streamError;
        final howText = appExceptionHowLocaleMapper(context, streamError?.how);
        final whyText = appExceptionWhyLocaleMapper(context, streamError?.why);
        String errorMessage = l10n.appException_template(howText, whyText);
        return Scaffold(
          body: Center(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    errorMessage,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      // 重新执行初始化逻辑
                      context.read<UserState>().initialize();
                    },
                    child: Text(l10n.common_retry),
                  ),
                ],
              ),
            ),
          ),
        );
      case UserAuthStatus.signedIn:
        return const _Initializer(child: HomeScreen());
    }
  }
}

// _Initializer 负责完成 NavModel 和 StatModel 的本地化文本构建
class _Initializer extends StatefulWidget {
  final Widget child;
  const _Initializer({required this.child});

  @override
  State<_Initializer> createState() => _InitializerState();
}

class _InitializerState extends State<_Initializer> {
  @override
  Widget build(BuildContext context) {
    // 将状态变更逻辑延迟到 build 完成 *之后* 运行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 在与 context 交互之前，检查 widget 是否仍然挂载
      if (!mounted) return;

      final navState = context.read<NavState>();
      final statState = context.read<StatState>();
      final l10n = AppLocalizations.of(context)!;

      // 在每次 build 时运行 `rebuild` 来确保 UI 文本与当前的 l10n 同步
      navState.rebuild(l10n);
      statState.rebuild(l10n);
    });

    // 直接返回实际的应用屏幕
    return widget.child;
  }
}
