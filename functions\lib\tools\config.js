import { initializeApp } from 'firebase-admin/app';
import { getFirestore } from 'firebase-admin/firestore';
import { SubscriptionStatus } from './types.js';
// 初始化 Firebase Admin SDK
export const app = initializeApp();
// 获取 Firestore 实例
export const db = getFirestore();
// ====== Firebase ======
export const FIREBASE = {
    REGION: 'us-central1',
    PROJECT_NUMBER: '387043819968',
    PROJECT_ID: 'taskive-4eec0',
};
// ====== Firestore ======
export const DATABASE = {
    USERS_COLLECTION: 'users',
    TASKS_COLLECTION: 'tasks',
    TIMERS_COLLECTION: 'timers',
    PROJECTS_COLLECTION: 'projects',
    RECURRINGS_COLLECTION: 'recurrings',
    SETTINGS_COLLECTION: 'settings',
    BATCH_LIMIT: 499,
    DEFAULT_TIMEZONE: 'Etc/UTC',
};
// ====== 密钥 ======
export const KEY = {
    REVENUECAT_WEBHOOK: 'REVENUECAT_WEBHOOK_KEY',
};
// ====== 认证 ======
// 认证方式集合
// oauth 放在顶部，优先匹配
export const AUTH_PROVIDERS = [
    { id: 'google.com', name: 'google', isOauth: true },
    { id: 'apple.com', name: 'apple', isOauth: true },
    { id: 'password', name: 'email' },
    { id: 'anonymous', name: 'anonymous', isAnonymous: true },
    { id: 'unknown', name: 'unknown', isUnknown: true },
];
// ====== 权限与限制 ======
// 定义哪些订阅状态被视为"高级版"
export const PREMIUM_STATUSES = [
    SubscriptionStatus.TRIAL,
    SubscriptionStatus.ACTIVE,
    SubscriptionStatus.GRACE_PERIOD,
];
// 定义各项功能的数量限制 (-1 表示无限)
export const FEATURE_LIMITS = {
    timers: { free: 100, premium: -1 },
    projects: { free: 2, premium: 1000 },
    recurrings: { free: 2, premium: 1000 },
};
// ====== 周期任务 ======
export const RECURRING = {
    SCHEDULE_TIME: 'every day 00:05', // 每天凌晨 00:05 运行 (避免整点高峰)
    GENERATION_DAYS_AHEAD: 29, // 提前生成未来多少天的任务
    MAX_RETRY_COUNT: 3,
    CONCURRENT_USER_LIMIT: 10,
};
// ====== 用户输入 ======
export const INPUT = {
    USERNAME_MAX_LENGTH: 20,
};
//# sourceMappingURL=config.js.map