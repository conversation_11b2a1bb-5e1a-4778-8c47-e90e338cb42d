// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// widgets
import '../../widgets/common/scrollable_time_picker.dart';
import '../../widgets/common/base_bottom_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

/// 显示用于编辑计时器记录的时间选择 BottomSheet
Future<TimeOfDay?> showTimerClockSheet({
  required BuildContext context,
  required TimeOfDay initialTime,
}) {
  return showModalBottomSheet<TimeOfDay>(
    context: context,
    builder: (context) => TimerClockSheet(
      initialTime: initialTime,
      onConfirm: (time) {
        Navigator.of(context).pop(time); // 点击确认时返回选择的时间
      },
    ),
  );
}

// 注意：这个文件只定义了 BottomSheet 的内容
// 显示它的逻辑在 timer_edit_page.dart 的 showCustomTimePicker 函数中
class TimerClockSheet extends StatefulWidget {
  final TimeOfDay initialTime;
  final Function(TimeOfDay) onConfirm;

  const TimerClockSheet({
    super.key,
    required this.initialTime,
    required this.onConfirm,
  });

  @override
  State<TimerClockSheet> createState() => _TimerClockSheetState();
}

class _TimerClockSheetState extends State<TimerClockSheet> {
  late TimeOfDay selectedTime;

  @override
  void initState() {
    super.initState();
    selectedTime = widget.initialTime;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BaseBottomSheet(
      title: l10n.timerClockSheet_title,
      onConfirm: () => widget.onConfirm(selectedTime),
      child: ScrollableTimePicker(
        initialTime: selectedTime,
        onTimeChanged: (newTime) {
          setState(() {
            selectedTime = newTime;
          });
        },
      ),
    );
  }
}
