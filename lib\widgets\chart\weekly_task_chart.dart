// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
// states
import '../../states/task_state.dart';
import '../../states/setting_state.dart';
// tools
import '../../tools/extensions.dart';
// others
import '../../generated/l10n/app_localizations.dart';

// 用于存储每日任务计数的辅助类
class DailyCounts {
  int due = 0;        // 按截止日期统计的已完成任务数
  int completed = 0;  // 按实际完成日期统计的已完成任务数
}

class WeeklyTaskChart extends StatelessWidget {
  const WeeklyTaskChart({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Consumer2<TaskState, SettingState>(
      builder: (context, taskState, settingState, _) {
        final weekStartDay = settingState.weekStartDay; 
        final weeklyData = _getWeeklyChartData(taskState, settingState);
        
        final completedColor = colorScheme.primary;
        final dueColor = colorScheme.primaryContainer;

        int maxY = 0;
        for (final counts in weeklyData.values) {
          if (counts.due > maxY) maxY = counts.due;
          if (counts.completed > maxY) maxY = counts.completed;
        }
        maxY = (maxY < 5) ? 5 : ((maxY / 5).ceil() * 5);
        final double yInterval = (maxY / 5).toDouble();

        final daySymbols = DateFormat.E(l10n.localeName).dateSymbols.STANDALONESHORTWEEKDAYS;
        
        final List<String> bottomTitlesList = [];
        int currentDay = weekStartDay;
        for (int i = 0; i < 7; i++) {
          bottomTitlesList.add(daySymbols[currentDay % 7]);
          currentDay = (currentDay % 7) + 1;
        }

        final BarChartData barChartData = BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: maxY.toDouble(),
          minY: 0,
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: yInterval,
            getDrawingHorizontalLine: (value) => FlLine(color: theme.dividerColor, strokeWidth: 0.5),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border(
              bottom: BorderSide(color: theme.dividerColor, width: 1.0),
              left: BorderSide(color: theme.dividerColor, width: 1.0),
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 16,
                interval: yInterval,
                getTitlesWidget: (double value, TitleMeta meta) {
                  if (value == 0 || value == maxY) return Container();
                  return Text(
                    value.toInt().toString(),
                    style: textTheme.labelSmall,
                    textAlign: TextAlign.left,
                  );
                },
              ),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 24,
                getTitlesWidget: (double value, TitleMeta meta) {
                  final index = value.toInt();
                  if (index < 0 || index >= bottomTitlesList.length) return Container();
                  final text = bottomTitlesList[index];
                  return Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(text, style: textTheme.labelSmall),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          barGroups: List.generate(7, (index) {
            int dayOfWeek = weekStartDay;
            for (int i = 0; i < index; i++) {
               dayOfWeek = (dayOfWeek % 7) + 1;
            }

            final counts = weeklyData[dayOfWeek] ?? DailyCounts();
            const double barWidth = 10;

            return BarChartGroupData(
              x: index,
              barsSpace: 4,
              barRods: [
                BarChartRodData(
                  toY: counts.due.toDouble(),
                  color: dueColor,
                  width: barWidth,
                  borderRadius: const BorderRadius.only(topLeft: Radius.circular(3), topRight: Radius.circular(3)),
                ),
                BarChartRodData(
                  toY: counts.completed.toDouble(),
                  color: completedColor,
                  width: barWidth,
                  borderRadius: const BorderRadius.only(topLeft: Radius.circular(3), topRight: Radius.circular(3)),
                ),
              ],
            );
          }),
          barTouchData: BarTouchData(enabled: false),
        );

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Padding(
            padding: const EdgeInsets.only(top: 12, bottom: 12, left: 12, right: 16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 10.0),
                  child: Text(
                    l10n.weeklyTaskChart_title,
                    style: textTheme.titleMedium?.copyWith(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                AspectRatio(
                  aspectRatio: 1.8,
                  child: BarChart(
                    barChartData,
                    duration: const Duration(milliseconds: 250),
                    curve: Curves.linear,
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildLegendItem(context, dueColor, l10n.weeklyTaskChart_legendDue),
                    const SizedBox(width: 16),
                    _buildLegendItem(context, completedColor, l10n.weeklyTaskChart_legendCompleted),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 构建图例项的小部件
  Widget _buildLegendItem(BuildContext context, Color color, String text) {
    final textTheme = Theme.of(context).textTheme;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 10,
          height: 10,
          color: color,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: textTheme.labelSmall,
        ),
      ],
    );
  }

  // 获取本周图表的统计数据
  Map<int, DailyCounts> _getWeeklyChartData(TaskState taskState, SettingState settingState) {
    final tasks = taskState.tasks;
    final weekStartDay = settingState.weekStartDay;
    final Map<int, DailyCounts> weeklyData = {
      for (var i = 1; i <= 7; i++) i: DailyCounts(),
    };

    final now = DateTime.now();
    // 使用扩展方法计算本周的日期范围
    final weekDates = now.getDatesOfThisWeek(weekStartDay);
    final startOfWeek = weekDates.first;
    final endOfWeek = weekDates.last.add(const Duration(days: 1)).subtract(const Duration(microseconds: 1)); // 包含周日的最后一刻

    for (final task in tasks) {
      // 只统计已完成且未丢弃的任务
      if (task.isDropped || !task.isCompleted) continue;

      // 按截止日期统计: 统计截止日期在本周内的已完成任务
      final DateTime? deadline = task.deadlineTime;

      if (deadline != null &&
          !deadline.isBefore(startOfWeek) &&
          deadline.isBefore(endOfWeek.add(const Duration(microseconds: 1)))) {
        final dayOfWeek = deadline.weekday;
        weeklyData[dayOfWeek]?.due += 1;
      }

      // 按 completeTime 统计: 统计完成日期在本周内的任务
      if (task.completeTime != null &&
          !task.completeTime!.isBefore(startOfWeek) &&
          task.completeTime!.isBefore(endOfWeek.add(const Duration(microseconds: 1)))) {
        final dayOfWeek = task.completeTime!.weekday;
        weeklyData[dayOfWeek]?.completed += 1;
      }
    }
    return weeklyData;
  }
}
