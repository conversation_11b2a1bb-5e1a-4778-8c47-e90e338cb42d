// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// tools
import '../tools/config.dart';
// others
import '../generated/l10n/app_localizations.dart';

// 1. 定义一个新的枚举来表示所有可用的主题
enum AppTheme {
  forestGreen,
  morningMist;

  // 返回主题的本地化显示名称
  String getLabel(AppLocalizations l10n) {
    switch (this) {
      case AppTheme.forestGreen:
        return l10n.themeModel_forestGreen;
      case AppTheme.morningMist:
        return l10n.themeModel_morningMist;
      default:
        return l10n.common_unknown;
    }
  }
}

// 这是一个辅助类，用于捆绑每个主题的浅色和深色模式
class AppThemeData {
  final ThemeData light;
  final ThemeData dark;

  AppThemeData({required this.light, required this.dark});
}

// 2. 创建一个优化的主题管理类
class AppThemes {
  // 定义新的种子颜色
  static const _forestGreenSeed = Color(0xFFA8D5A8);
  static const _morningMistSeed = Color(0xFFEFF2F5);

  // 定义默认主题
  static const AppTheme defaultTheme = AppTheme.forestGreen;

  // 3. 定义一个所有主题共享的"基础主题"，只包含结构、形状和字体信息
  static final ThemeData _baseTheme = ThemeData(
    useMaterial3: true,

    textTheme: const TextTheme(
      // large
      titleLarge: TextStyle(fontSize: 18.0, fontWeight: FontWeight.w600),
      bodyLarge: TextStyle(fontSize: 16.0),
      labelLarge: TextStyle(fontSize: 14.0),
      // medium
      titleMedium: TextStyle(fontSize: 16.0, fontWeight: FontWeight.w500),
      bodyMedium: TextStyle(fontSize: 14.0),
      labelMedium: TextStyle(fontSize: 12.0),
      // small
      titleSmall: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w400),
      bodySmall: TextStyle(fontSize: 12.0),
      labelSmall: TextStyle(fontSize: 10.0),
    ),

    appBarTheme: AppBarTheme(
      toolbarHeight: Config.app.appBarHeight,
      elevation: 0.0,
      centerTitle: false,
    ),

    navigationBarTheme: NavigationBarThemeData(
      height: Config.app.navBarHeight,
      elevation: 1.0,
      indicatorShape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
    ),

    floatingActionButtonTheme: const FloatingActionButtonThemeData(
      shape: CircleBorder(),
    ),

    cardTheme: CardThemeData(
      margin: EdgeInsets.zero,
      elevation: 0.5,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
    ),

    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        elevation: 0.5,
        minimumSize: const Size(80, 36),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.0)),
      ),
    ),

    listTileTheme: const ListTileThemeData(
      contentPadding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 0.0),
      visualDensity: VisualDensity(horizontal: -4),
      minLeadingWidth: 24,
    ),

    checkboxTheme: CheckboxThemeData(
      shape: const CircleBorder(),
      side: const BorderSide(width: 1.5),
      materialTapTargetSize: MaterialTapTargetSize.padded,
    ),

    inputDecorationTheme: InputDecorationTheme(
      border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
      contentPadding: const EdgeInsets.symmetric(vertical: 0.0),
    ),

    outlinedButtonTheme: OutlinedButtonThemeData(
      style: ButtonStyle(
        padding: WidgetStateProperty.all<EdgeInsets>(const EdgeInsets.all(24)),
        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
          RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
        ),
      ),
    ),

    bottomSheetTheme: const BottomSheetThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(8.0)),
      ),
    ),

    dialogTheme: DialogThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8.0),
      ),
    ),
  );

  // 4. 使用函数来构建具体的主题，以应用颜色
  static AppThemeData _buildThemeData(Color seedColor) {
    final lightColorScheme = ColorScheme.fromSeed(
      seedColor: seedColor,
      brightness: Brightness.light,
    );
    final darkColorScheme = ColorScheme.fromSeed(
      seedColor: seedColor,
      brightness: Brightness.dark,
    );

    return AppThemeData(
      light: _baseTheme.copyWith(
        colorScheme: lightColorScheme,
        scaffoldBackgroundColor: lightColorScheme.surface,
        iconTheme: IconThemeData(color: lightColorScheme.onSurface),
        checkboxTheme: _baseTheme.checkboxTheme.copyWith(
          side: BorderSide(color: lightColorScheme.primary, width: 1.5),
        ),
      ),
      dark: _baseTheme.copyWith(
        brightness: Brightness.dark,
        colorScheme: darkColorScheme,
        scaffoldBackgroundColor: darkColorScheme.surface,
        iconTheme: IconThemeData(color: darkColorScheme.onSurface),
        checkboxTheme: _baseTheme.checkboxTheme.copyWith(
          side: BorderSide(color: darkColorScheme.primary, width: 1.5),
        ),
      ),
    );
  }

  // 5. 定义最终的主题 Map
  static final Map<AppTheme, AppThemeData> _themes = {
    AppTheme.forestGreen: _buildThemeData(_forestGreenSeed),
    AppTheme.morningMist: _buildThemeData(_morningMistSeed),
  };

  // 提供一个静态方法来根据枚举获取对应的主题数据
  static AppThemeData getTheme(AppTheme theme) {
    return _themes[theme]!;
  }
}
