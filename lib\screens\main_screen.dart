// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// states
import '../states/nav_state.dart';
// tools
import '../tools/bottom_slide_route.dart';
// widgets
import '../widgets/timer/timer_floating_bar.dart';
// pages
import '../sheets/task/task_edit_page.dart';

// 主要屏幕组件，负责构建底部导航栏和显示对应的页面视图
class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final navState = context.watch<NavState>();
    final bottomNavs = navState.bottomNavs;

    // 1. 如果没有导航项 (理论上不应该发生，因为 Entry 是固定的)，显示一个空占位符
    if (bottomNavs.isEmpty) {
      return const Scaffold(body: Center(child: Text("No navigation items.")));
    }

    // 2. 构建页面和导航项列表
    final List<Widget> pages = bottomNavs.map((nav) => nav.page).toList();
    final List<NavigationDestination> navItems = bottomNavs
        .map((nav) => NavigationDestination(icon: Icon(nav.icon), label: nav.label))
        .toList();

    return Scaffold(
      body: Stack(
        children: [
          // 使用 IndexedStack 来保持所有页面的状态
          IndexedStack(
            index: navState.currentIndex,
            children: pages,
          ),
          // 计时器浮动条
          const Positioned(
            left: 16,
            right: 88, // 56 (FAB 的宽度) + 16 (左右边距) + 16 (左右边距)
            bottom: 16,
            child: TimerFloatingBar(),
          ),
        ],
      ),
      // 添加新任务的悬浮按钮
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // 增加震动反馈
          HapticFeedback.lightImpact();
          Navigator.of(context).push(BottomSlideRoute(page: const TaskEditPage()));
        },
        child: const Icon(LucideIcons.plus),
      ),
      // 底部导航栏
      // 当只有一个导航项时 (即只有 Entry)，不显示底部导航栏
      bottomNavigationBar: navItems.length >= 2
        ? NavigationBar(
            labelTextStyle: WidgetStateProperty.all(Theme.of(context).textTheme.labelSmall),
            selectedIndex: navState.currentIndex,
            destinations: navItems,
            onDestinationSelected: (index) {
              // 增加震动反馈
              HapticFeedback.lightImpact();
              navState.onNavTapped(index);
            },
          )
        : null,
    );
  }
}
