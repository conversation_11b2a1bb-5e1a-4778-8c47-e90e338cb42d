// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/stat_model.dart';
// states
import '../../states/stat_state.dart';
// tools
import '../../tools/config.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class StatSettingPage extends StatelessWidget {
  const StatSettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.statSettingPage_title),
      ),
      body: Consumer<StatState>(
        builder: (context, statState, _) {
          final selectedStatsForDisplay = statState.selectedStats;
          final selectableStats = statState.selectableStats;
          final bool isCustomListActive = statState.currentStatIds.isNotEmpty;

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.statSettingPage_enabledStats,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                ),

                if (selectedStatsForDisplay.isEmpty)
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Center(
                      child: Text(
                        l10n.statSettingPage_emptyEnabledStats,
                        style: TextStyle(
                          color: colorScheme.onSurfaceVariant,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  )
                else
                  ReorderableListView(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    buildDefaultDragHandles: isCustomListActive,
                    children: [
                      for (int i = 0; i < selectedStatsForDisplay.length; i++)
                        _buildSelectedStat(
                          context,
                          i,
                          selectedStatsForDisplay[i],
                          statState,
                        ),
                    ],
                    onReorder: (oldIndex, newIndex) {
                      // 增加震动反馈
                      HapticFeedback.lightImpact();
                      
                      if (!isCustomListActive) {
                        return;
                      }
                      if (oldIndex < newIndex) {
                        newIndex -= 1;
                      }
                      final reorderedCustomList = List.of(selectedStatsForDisplay);
                      final movedStat = reorderedCustomList.removeAt(oldIndex);
                      reorderedCustomList.insert(newIndex, movedStat);

                      final newCustomOrderIds = reorderedCustomList.map((s) => s.id).toList();
                      statState.reorder(newCustomOrderIds);
                    },
                  ),

                const Divider(height: 32),

                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.statSettingPage_disabledStats,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                ),

                // 构建可选统计图区域
                _buildSelectableCategorySection(
                  context,
                  l10n.statSettingPage_categoryTask,
                  StatCategory.task,
                  selectableStats,
                  statState,
                ),
                _buildSelectableCategorySection(
                  context,
                  l10n.statSettingPage_categoryTimer,
                  StatCategory.timer,
                  selectableStats,
                  statState,
                ),
                _buildSelectableCategorySection(
                  context,
                  l10n.statSettingPage_categoryHabit,
                  StatCategory.habit,
                  selectableStats,
                  statState,
                ),
                 _buildSelectableCategorySection(
                  context,
                  l10n.statSettingPage_categoryOther,
                  StatCategory.other,
                  selectableStats,
                  statState,
                ),

                SizedBox(height: Config.app.bottomSpace),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 构建可选统计图的分类标题
  Widget _buildCategoryHeader(BuildContext context, String title) {
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 4),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: colorScheme.primary.withValues(alpha: 0.8),
        ),
      ),
    );
  }

  /// 构建已选统计图列表项
  Widget _buildSelectedStat(
    BuildContext context,
    int displayIndex, // 在可显示列表中的索引
    StatModel stat,
    StatState statState,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    return ListTile(
      // 使用包含 stat ID 的 Key，确保列表项在重排和更新时能正确识别
      key: ValueKey('selected-${stat.id}'),
      leading: IconButton(
        icon: const Icon(LucideIcons.circleMinus),
        color: colorScheme.error,
        visualDensity: VisualDensity.compact,
        onPressed: () {
          // 增加震动反馈
          HapticFeedback.lightImpact();
          statState.remove(stat.id);
        },
      ),
      title: Text(stat.name, style: TextStyle(fontSize: 14)),
      subtitle: Text(stat.description, style: TextStyle(fontSize: 12)),
      trailing: Icon(LucideIcons.equal),
    );
  }

  /// 构建可选统计图列表项
  Widget _buildSelectableStat(
    BuildContext context,
    StatModel stat,
    StatState statState,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    return ListTile(
      key: ValueKey('selectable-${stat.id}'),
      leading: IconButton(
        icon: const Icon(LucideIcons.circlePlus),
        color: colorScheme.primary,
        visualDensity: VisualDensity.compact,
        onPressed: () {
          // 增加震动反馈
          HapticFeedback.lightImpact();
          statState.add(statState.selectedStats.length, stat);
        },
      ),
      title: Text(stat.name, style: TextStyle(fontSize: 14)),
      subtitle: Text(stat.description, style: TextStyle(fontSize: 12)),
      trailing: Icon(stat.icon),
    );
  }

  /// 构建按分类过滤的可选统计图区域
  Widget _buildSelectableCategorySection(
    BuildContext context,
    String title,
    StatCategory category,
    List<StatModel> selectableStats,
    StatState statState,
  ) {
    final itemsToShow = selectableStats.where((stat) => stat.category == category).toList();
    if (itemsToShow.isEmpty) {
      return const SizedBox.shrink();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCategoryHeader(context, title),
        for (final stat in itemsToShow)
          _buildSelectableStat(
            context,
            stat,
            statState,
          ),
      ],
    );
  }
}
