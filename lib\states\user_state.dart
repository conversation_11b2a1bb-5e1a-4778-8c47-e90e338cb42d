// import 'dart:developer' as developer;
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:google_sign_in/google_sign_in.dart';
// models
import '../models/user_model.dart';
// services
import '../services/user_service.dart';
// tools
import '../tools/config.dart';
import '../tools/app_exception.dart';
import '../tools/app_exception_mapper.dart';

/// 用户认证状态
/// 设计原则：状态尽可能少，职责尽可能清晰
enum UserAuthStatus {
  authenticating,    // 仅用于首次启动时的全局加载
  signedIn,          // 正常状态，用户已登录 (无论是匿名还是正式)，应用可用
  refreshingSession, // 仅用于后台会话失效时的全局加载
  error,             // 发生致命错误，通常在首次启动或会话刷新失败时
}

class UserState extends ChangeNotifier {
  final FirebaseAuth _auth;
  final UserService _userService;
  final String? _cachedUserId;

  /// ====== 私有状态 ======

  StreamSubscription<User?>? _authUserSubscription;
  StreamSubscription<UserModel?>? _firestoreUserSubscription;
  UserModel? _user;
  UserAuthStatus _status; // 初始状态由构造函数决定
  AppException? _streamError;

  /// ====== 构造函数 ======

  UserState(this._auth, this._userService, this._cachedUserId)
    : _status = _cachedUserId != null ? UserAuthStatus.signedIn : UserAuthStatus.authenticating;

  /// ====== 公共状态 ======

  UserModel? get user => _user;
  UserAuthStatus get status => _status;
  AppException? get streamError => _streamError;
  bool get isSignedIn => _status == UserAuthStatus.signedIn && _user != null;
  bool get isAnonymous => _auth.currentUser?.isAnonymous ?? false;
  bool get isGoogleUser => UserAuthProvider.isGoogleUser(_auth.currentUser);
  bool get isAppleUser => UserAuthProvider.isAppleUser(_auth.currentUser);

  /// ====== 流监听 ======

  /// [架构核心] 启动后台监听与验证
  void initialize() {
    _authUserSubscription?.cancel();
    _authUserSubscription = null;
    _firestoreUserSubscription?.cancel();
    _firestoreUserSubscription = null;

    if (_cachedUserId != null) {
      // 后续进入应用，命中缓存
      _listenToFirestoreUserChanges(_cachedUserId);
    } else {
      // 首次进入应用，需要账户初始化
      _status = UserAuthStatus.authenticating;
      notifyListeners();
    }
    
    // auth.userChanges 会响应 auth 的所有变化
    // auth.authStateChanges 只会响应登录和登出事件
    _authUserSubscription = _auth.userChanges().listen(
      _listenToAuthUserChanged,
      onError: (error, stack) {
        handleStreamError(error, stack);
      },
    );
  }

  /// [架构核心] Auth 状态的唯一响应者 (决策树和交通警察)
  ///
  /// 该方法负责将 Firebase Auth 的状态变化映射到具体的应用业务流程
  /// 
  /// 它精确处理了以下由用户操作或应用生命周期触发的核心场景：
  ///
  ///   - 进入：首次进入应用
  ///     [1️⃣.1️⃣ -> 2️⃣.1️⃣] [launch -> null -> 匿名账户]
  ///   - 进入：后续进入应用
  ///     [2️⃣.1️⃣] [匿名账户或正式账户] (没有状态变化，就像进入离线应用)
  ///   - 登录：匿名账户升级为正式账户
  ///     [2️⃣.2️⃣.1️⃣] [匿名账户 -- 正式账户] (升级，UID 没有变)
  ///   - 登录：匿名账户切换到已有的正式账户
  ///     [2️⃣.2️⃣.2️⃣] [匿名账户 -> 正式账户] (切换，UID 有变化)
  ///   - 登出：登出正式账户再创建匿名账户
  ///     [1️⃣.1️⃣ -> 2️⃣.1️⃣] [正式账户 -> null -> 匿名账户]
  ///   - 删除：删除正式账户再创建匿名账户
  ///     [1️⃣.1️⃣ -> 2️⃣.1️⃣] [正式账户 -> null -> 匿名账户]
  ///   - 刷新：处理意外的会话失效
  ///     [1️⃣.2️⃣ -> 2️⃣.2️⃣.2️⃣] [正式账户 -> null -> 匿名账户]
  ///
  /// 变量定义:
  /// 
  ///   - AuthUser: 来自 `_auth.userChanges()` 流的最新 Firebase Auth 用户状态。可为 null
  ///   - AppUser: 当前 `UserState` 内部缓存的、来自 Firestore 的用户模型。可为 null
  ///
  Future<void> _listenToAuthUserChanged(User? user) async {
    try {
    // =======================================================================
    // 决策分支 1️⃣: AuthUser == null (Auth 说：当前未登录)
    // =======================================================================
    if (user == null) {
      if (_user == null) {
        // -------------------------------------------------------------------
        // 场景 1️⃣.1️⃣: AuthUser == null, AppUser == null
        // -------------------------------------------------------------------
        // 触发时机:
        //   - 首次进入应用
        //   - 用户主动登出或删除账户后（`_signOutInternal` 先将 `_user` 置为 null）
        // -------------------------------------------------------------------

        await _auth.signInAnonymously();
      } else {
        // -------------------------------------------------------------------
        // 场景 1️⃣.2️⃣: AuthUser == null, AppUser != null
        // -------------------------------------------------------------------
        // 触发时机:
        //   - 意外会话丢失（Token 刷新失败、账户在别处被禁用/删除）。
        //   - 这是最需要向用户明确展示全局加载的状态。
        // -------------------------------------------------------------------

        _status = UserAuthStatus.refreshingSession;
        notifyListeners();
        await _auth.signInAnonymously();
      }
    }
    // =======================================================================
    // 决策分支 2️⃣: AuthUser != null (Auth 说：当前已登录)
    // =======================================================================
    else {
      if (_user == null) {
        // -------------------------------------------------------------------
        // 场景 2️⃣.1️⃣: AuthUser != null, AppUser == null
        // -------------------------------------------------------------------
        // 触发时机:
        //   - 首次进入应用
        //   - 后续进入应用
        //   - 用户主动登出或删除账户后
        // 这是所有“新会话建立”的安全入口点。
        // -------------------------------------------------------------------

        await _userService.setLocalStorageUserId(user.uid);
        await _listenToFirestoreUserChanges(user.uid);
        await _signInInternal(user);
      } else {
        // -------------------------------------------------------------------
        // 场景 2️⃣.2️⃣: AuthUser != null, AppUser != null
        // -------------------------------------------------------------------
        if (_user!.id == user.uid) {
          // -----------------------------------------------------------------
          // 场景 2️⃣.2️⃣.1️⃣: UID 相同
          // -----------------------------------------------------------------
          // 触发时机:
          //   - 匿名账户升级正式账户 (UID 不变)
          //   - Auth Token 在后台刷新
          // -----------------------------------------------------------------

          // 只有从匿名账户升级到正式账户这一种情况，才需要调用升级账户函数
          // 上述情况只走这个分支，所以在这里判断即可
          final authUserProvider = UserAuthProvider.fromAuth(user);
          final appUserProvider = _user!.authProvider;
          final isUpgrade = authUserProvider != appUserProvider;
          await _signInInternal(user, isUpgrade: isUpgrade);
        } else {
          // -----------------------------------------------------------------
          // 场景 2️⃣.2️⃣.2️⃣: UID 不同 (AuthUser 为 A, AppUser 为 B)
          // -----------------------------------------------------------------
          // 触发时机:
          //   - 匿名账户切换正式账户
          //   - 意外会话丢失
          // 这是处理“直接账户切换”的【关键安全保障】
          //   主要发生在：当一个匿名用户，尝试用一个【已存在的】谷歌账户登录时，
          //   Firebase Auth 可能会直接将状态从 user B 切换到 user A
          //   跳过了我们期望的 `null` 中间状态
          // 核心风险:
          //   - 导致下游 State 产生竞态条件和权限错误
          // 解决方案: 强制执行“先解散，后集合”流程
          //   我们在此处手动广播一个 `null` 信号，强制所有下游 State 先清理自己
          // -----------------------------------------------------------------
          
          // 步骤 A: 广播“解散”信号
          _user = null;
          notifyListeners();
          // 等待一帧，确保 Provider 将 null 状态传播并被下游处理
          await Future.delayed(Duration.zero);

          // 步骤 B: 现在 App 处于一个干净的状态 (符合场景 2.1 的前提)
          //   因此我们执行与场景 2.1 完全相同的初始化代码
          await _userService.setLocalStorageUserId(user.uid);
          await _listenToFirestoreUserChanges(user.uid);
          await _signInInternal(user);
        }
      }
    }
    } catch (error, stack) {
      handleStreamError(error, stack);
    }
  }

  /// [核心流程] 监听并处理用户 Firestore 文档
  /// 
  /// _listenToAuthUserChanged 是主动的指挥官
  /// _listenToFirestoreUserChanges 是被动的消费者
  /// 
  Future<void> _listenToFirestoreUserChanges(String userId) async {
    await _firestoreUserSubscription?.cancel();
    _firestoreUserSubscription = null;

    _firestoreUserSubscription = _userService.getUserStream(userId).listen(
      (userModel) {
        if (userModel == null) return;

        _user = userModel;

        if (_status != UserAuthStatus.signedIn) {
          _status = UserAuthStatus.signedIn;
        }

        notifyListeners();
      },
      onError: (error, stack) {
        handleStreamError(error, stack);
      },
    );
  }
  
  /// [内部事务] 登录后的后台任务（同步数据、登录第三方服务等）
  Future<void> _signInInternal(User authUser, {bool isUpgrade = false}) async {
    if (isUpgrade) {
      await _userService.upgradeAccount();
    } else {
      await _userService.recordSignIn();
    }

    final packageInfo = await PackageInfo.fromPlatform();
    if (packageInfo.packageName == Config.app.packageName) {
      await Purchases.logIn(authUser.uid);
    }
  }

  /// [内部事务] 登出时的核心清理逻辑
  Future<void> _signOutInternal() async {
    // 步骤 1: 先登出 Firebase Auth
    // 这是最关键的、会改变全局认证状态的操作，应该最先执行
    // 登出后，userChanges() 会自动触发并引导到创建新匿名用户的流程
    await _auth.signOut();

    // 步骤 2: 在 Auth 登出后，再执行所有客户端的本地清理
    // 因为此时的认证状态已经明确为“未认证”，所以这些清理操作不会有权限冲突
    await _firestoreUserSubscription?.cancel();
    _firestoreUserSubscription = null;
    _user = null;
    notifyListeners();

    final packageInfo = await PackageInfo.fromPlatform();
    if (packageInfo.packageName == Config.app.packageName) {
      await Purchases.logOut();
    }

    await _userService.clearLocalStorageUserId();
  }

  /// ====== 网络请求 ======
  
  /// Exception 由相应 UI 自行展示
  Future<void> signInWithEmailLink(String email) async {
    final actionCodeSettings = ActionCodeSettings(
      url: '${Config.service.domain}${Config.service.finishSignInPath}',
      androidPackageName: Config.app.packageName,
      androidMinimumVersion: '12',
      handleCodeInApp: true,
      androidInstallApp: true,
    );

    await _auth.sendSignInLinkToEmail(
      email: email,
      actionCodeSettings: actionCodeSettings,
    );

    await _userService.setLocalStorageUserEmail(email);
  }

  /// Exception 由 InitScreen 全局展示
  Future<void> completeSignInWithEmailLink(String link) async {
    // 阻止其他链接
    if (!_auth.isSignInWithEmailLink(link)) return;

    AuthCredential? credential;

    try {
      final email = await _userService.getLocalStorageUserEmail();

      // 没有缓存邮箱
      if (email == null) return;

      credential = EmailAuthProvider.credentialWithLink(
        email: email,
        emailLink: link,
      );

      // 路径 1: 匿名账户链接到邮箱
      // 路径 2.1: 链接到邮箱发现已存在，报错
      await _auth.currentUser!.linkWithCredential(credential); // 断言失败直接报错
      
      // 成功后清理缓存的邮箱
      await _userService.clearLocalStorageUserEmail();

    } on FirebaseAuthException catch (e) {
      final why = appExceptionWhyMapper(e.code);
      if (why == AppExceptionWhy.credentialAlreadyInUse || why == AppExceptionWhy.emailAlreadyInUse) {
        // 路径 2.2: 捕获到报错，直接登录
        await _auth.signInWithCredential(credential!);

        // 成功后清理缓存的邮箱
        await _userService.clearLocalStorageUserEmail();
      } else {
        rethrow;
      }
    } catch (error, stack) {
      handleStreamError(error, stack);
    }
  }

  /// Exception 由 InitScreen 全局展示
  Future<void> signInWithGoogle() async {
    GoogleSignInAccount? googleUser;
    OAuthCredential? credential;

    try {
      googleUser = await GoogleSignIn().signIn();
      
      // 用户取消了谷歌登录
      if (googleUser == null) return;

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // 路径 1: 匿名账户链接到谷歌账户
      // 路径 2.1: 链接到谷歌账户发现已存在，报错
      await _auth.currentUser!.linkWithCredential(credential); // 断言失败直接报错

    } on FirebaseAuthException catch (e) {
      final why = appExceptionWhyMapper(e.code);
      if (why == AppExceptionWhy.credentialAlreadyInUse || why == AppExceptionWhy.emailAlreadyInUse) {
        // 路径 2.2: 捕获到报错，直接登录
        await _auth.signInWithCredential(credential!);
      } else {
        rethrow;
      }
    } catch (error, stack) {
      handleStreamError(error, stack);
    }
  }

  /// Exception 由相应 UI 自行展示
  Future<void> signOut() async {
    await _signOutInternal();
  }

  /// Exception 由相应 UI 自行展示
  Future<void> updateUsername(String newName) async {
    if (newName.isEmpty || newName.length > Config.app.usernameMaxLength) {
      throw AppException(
        how: AppExceptionHow.updateUsername,
        why: AppExceptionWhy.invalidArgument,
      );
    }

    await _userService.updateUsername(newName);
  }

  /// Exception 由相应 UI 自行展示
  Future<void> deleteUser() async {
    // 步骤 1: 命令后端删除
    try {
      await _userService.deleteUser();
    } finally {
      // 步骤 2: 无论后端命令是否成功（或是否抛出预期的异常），
      // 总是执行客户端的清理和会话重建流程。
      // 这确保了用户的界面总能恢复到一个可用的状态。
      await _signOutInternal();
    }
  }

  /// ====== 工具方法 ======

  void handleStreamError(error, stack) {
    _status = UserAuthStatus.error;
    _streamError = appExceptionMapper(
      error: error as Exception,
      how: AppExceptionHow.initializeApp,
      stack: stack,
    );
    notifyListeners();
  }

  @override
  void dispose() {
    _authUserSubscription?.cancel();
    _firestoreUserSubscription?.cancel();
    super.dispose();
  }
}
