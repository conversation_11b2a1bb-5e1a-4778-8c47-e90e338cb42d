// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
// models
import '../../models/task_model.dart';
// states
import '../../states/task_state.dart';
import '../../states/timer_state.dart';
import '../../states/user_state.dart';
// services
import '../../services/permission_service.dart';
// tools
import '../../tools/config.dart';
import '../../tools/validators.dart';
import '../../tools/explicit_value.dart';
import '../../tools/app_exception.dart';
import '../../tools/app_exception_mapper.dart';
// widgets
import '../../widgets/timer/timer_action.dart';
import '../../widgets/task/task_priority.dart';
import '../../widgets/common/app_exception_snackbar.dart';
// sheets
import './task_date_picker.dart';
import './task_project_picker.dart';
import './task_clock_picker.dart';
import '../common/subscribe_guide_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TaskEditPage extends StatefulWidget {
  final TaskModel? task; // 如果是编辑普通任务则传入

  const TaskEditPage({
    super.key,
    this.task,
  });

  @override
  State<TaskEditPage> createState() => _TaskEditPageState();
}

class _TaskEditPageState extends State<TaskEditPage> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late FocusNode _nameFocusNode;

  DateTime? _dueDate;
  TimeOfDay? _dueClock;
  String? _projectId;
  bool _isImportant = false;
  bool _isUrgent = false;

  bool get _isEditMode => widget.task != null;

  bool _isNameEmpty = true;

  @override
  void initState() {
    super.initState();
    // 初始化控制器
    _nameController = TextEditingController();
    _nameFocusNode = FocusNode();

    // 设置初始值
    if (_isEditMode) {
      // --- 编辑现有任务 ---
      final task = widget.task!;
      _nameController.text = task.name;
      _projectId = task.projectId;
      _isImportant = task.isImportant;
      _isUrgent = task.isUrgent;

      // --- 根据新的时间模型设置初始值 ---
      if (task.dueTime != null) {
        // 精确时刻任务
        _dueDate = task.dueTime;
        _dueClock = TimeOfDay.fromDateTime(task.dueTime!);
      } else if (task.dueDate != null) {
        // 全天任务
        _dueDate = DateTime.tryParse(task.dueDate.toString());
        _dueClock = null;
      } else {
        // 没有截止时间的任务
        _dueDate = null;
        _dueClock = null;
      }
    } else {
      // --- 新建任务 ---
      _dueDate = DateTime.now();
      _dueClock = null;
    }

    // 初始化 _isNameEmpty
    _isNameEmpty = _nameController.text.trim().isEmpty;

    // 添加文本变化监听
    _nameController.addListener(_updateNameState);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;
    // 监听 UserState，确保用户信息加载后能重建 UI
    context.watch<UserState>();

    const double rowSpacing = 32.0; // 行间距
    const double sideSpacing = 16.0; // 左右边距

    final timerNotifier = Provider.of<TimerNotifier>(context);
    final hasRunningTimer = timerNotifier.state.hasRunningTimer;

    final task = widget.task; // task 可能为 null
    bool isDroppedTask = _isEditMode && task!.isDropped;
    bool isCompletedTask = _isEditMode && task!.isCompleted;
    bool isRecurringInstance = _isEditMode && task!.recurringId != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditMode ? l10n.taskEditPage_titleEdit : l10n.taskEditPage_titleCreate),
        actions: [
          // 只有未丢弃的任务才显示保存按钮
          if (!isDroppedTask)
            IconButton(
              icon: const Icon(LucideIcons.check),
              onPressed: _saveTask,
            ),
        ],
      ),
      // 使用 SingleChildScrollView 和 Form 防止内容溢出
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start, // 保持左对齐
            children: [

              // ========== 提示横幅 ==========

              // 如果是已丢弃任务，显示提示横幅
              if (isDroppedTask)
                Container(
                  width: double.infinity,
                  color: colorScheme.tertiaryContainer,
                  padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: sideSpacing),
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: Text(
                    l10n.taskEditPage_droppedWarning,
                    style: TextStyle(color: colorScheme.onTertiaryContainer),
                  ),
                ),
              // 如果是周期任务实例，显示提示横幅
              if (isRecurringInstance)
                Container(
                  width: double.infinity,
                  color: colorScheme.tertiaryContainer,
                  padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: sideSpacing),
                  margin: const EdgeInsets.only(bottom: 8.0),
                  child: Text(
                    l10n.taskEditPage_recurringWarning,
                    style: TextStyle(color: colorScheme.onTertiaryContainer),
                  ),
                ),

              // ========== 第一分区 ==========

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: sideSpacing),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 任务名称输入框
                    TextFormField(
                      controller: _nameController,
                      focusNode: _nameFocusNode,
                      decoration: InputDecoration(
                        hintText: l10n.taskEditPage_nameHint,
                        hintStyle: TextStyle(color: colorScheme.onSurfaceVariant.withValues(alpha: 0.6)),
                        border: InputBorder.none,
                      ),
                      style: const TextStyle(fontSize: 18),
                      minLines: 1,
                      maxLines: Config.app.taskNameMaxLines,
                      keyboardType: TextInputType.multiline,
                      textInputAction: TextInputAction.newline,
                      validator: (value) => Validators.validateTaskName(value, l10n),
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                    ),

                    const SizedBox(height: rowSpacing / 2),

                    // 截止日期选择器
                    TaskDatePicker(
                      selectedDate: _dueDate,
                      onSelected: (date) {
                        // 这个选择器行为正常，保留立即执行的 unfocus
                        _nameFocusNode.unfocus();
                        setState(() {
                          _dueDate = date;
                          if (date == null) {
                            _dueClock = null;
                          }
                        });
                      },
                    ),

                    const SizedBox(height: rowSpacing),

                    // 所属项目选择器
                    TaskProjectPicker(
                      selectedProjectId: _projectId,
                      onSelected: (projectId) {
                        setState(() {
                          _projectId = projectId;
                        });
                        // 延迟执行，以确保在 Flutter 的焦点恢复逻辑之后再移除焦点
                        Future.delayed(const Duration(milliseconds: 10), () {
                          if (mounted) _nameFocusNode.unfocus();
                        });
                      },
                    ),

                    const SizedBox(height: rowSpacing),

                    // 时间选择器组件
                    TaskClockPicker(
                      selectedTime: _dueClock,
                      dueDate: _dueDate, // 直接传入日期，可能为null
                      onSelected: (time) {
                        setState(() {
                          _dueClock = time;
                          if (time != null && _dueDate == null) {
                            _dueDate = DateTime.now();
                          }
                        });
                        Future.delayed(const Duration(milliseconds: 10), () {
                          if (mounted) _nameFocusNode.unfocus();
                        });
                      },
                      onSetDueDateToToday: _onSetDueDateToToday,
                    ),

                    const SizedBox(height: rowSpacing),
                  ],
                ),
              ),

              // 分隔线
              Divider(color: colorScheme.outline.withValues(alpha: 0.12), height: 1, thickness: 1),

              // ========== 第二分区 ==========

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: sideSpacing),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: rowSpacing - 8.0),

                    () {
                      final bool canCreate = timerNotifier.canStartTimer();
                      return TimerAction(
                        // 计时器禁用条件：
                        // 正在计时
                        // 任务已完成或已丢弃
                        status: _getTimerActionStatus(
                          hasRunningTimer,
                          isCompletedTask,
                          isDroppedTask,
                          canCreate,
                        ),
                        taskName: _nameController.text.trim(),
                        onTap: () {
                          if (canCreate) {
                            // 权限足够，开始计时
                            _startTiming();
                          } else {
                            // 权限不足，显示引导页
                            SubscribeGuideSheet.show(
                              context,
                              feature: Feature.timer,
                            );
                          }
                        },
                        onEmptyTitle: () {
                          // 任务名为空时，触发表单验证
                          _formKey.currentState?.validate();
                        },
                      );
                    }(),

                    const SizedBox(height: rowSpacing - 8.0),
                  ],
                ),
              ),

              // 分隔线
              Divider(color: colorScheme.outline.withValues(alpha: 0.12), height: 1, thickness: 1),

              // ========== 第三分区 ==========

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: sideSpacing),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: rowSpacing - 8.0),

                    // 优先级选择器组件
                    TaskPriority(
                      isUrgent: _isUrgent,
                      isImportant: _isImportant,
                      onSelected: (important, urgent) {
                        setState(() {
                          _isImportant = important;
                          _isUrgent = urgent;
                        });
                      },
                    ),

                    SizedBox(height: Config.app.bottomSpace),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // _isNameEmpty 的作用是：
  // 当任务名称从"空 -> 非空"或从"非空 -> 空"时触发组件重建
  void _updateNameState() {
    final isEmpty = _nameController.text.trim().isEmpty;
    if (isEmpty != _isNameEmpty) {
      setState(() => _isNameEmpty = isEmpty);
    }
  }

  // 获取计时器状态
  ({bool disabled, String text}) _getTimerActionStatus(
    bool hasRunningTimer,
    bool isCompletedTask,
    bool isDroppedTask,
    bool canCreate,
  ) {
    final l10n = AppLocalizations.of(context)!;
    if (hasRunningTimer) {
      return (disabled: true, text: l10n.taskEditPage_timerActionStatusRunning);
    }
    if (_isEditMode && isCompletedTask) {
      return (disabled: true, text: l10n.taskEditPage_timerActionStatusCompleted);
    }
    if (_isEditMode && isDroppedTask) {
      return (disabled: true, text: l10n.taskEditPage_timerActionStatusDropped);
    }
    if (!canCreate) {
      return (disabled: false, text: l10n.taskEditPage_timerActionStatusLimitReached);
    }
    return (disabled: false, text: l10n.taskEditPage_timerActionStatusReady);
  }

  void _onSetDueDateToToday() {
    setState(() => _dueDate = DateTime.now());
  }

  // 保存任务
  void _saveTask() async {
    HapticFeedback.lightImpact();
    final navigator = Navigator.of(context);
    // 调用新的核心方法，如果成功则关闭页面
    final String taskId = await _prepareAndSaveTask();

    if (taskId.isNotEmpty) {
      if (mounted) navigator.pop();
    }
  }

  // 开始计时
  void _startTiming() async {
    final timerNotifier = Provider.of<TimerNotifier>(context, listen: false);
    final navigator = Navigator.of(context);

    // 调用核心方法准备并保存任务
    final String taskId = await _prepareAndSaveTask();
    
    // 如果任务保存失败，会有错误提示，不进行计时
    if (taskId.isEmpty) return;
    
    try {
      // 任务保存成功后开始计时
      await timerNotifier.startTimer(taskId);
      if (mounted) navigator.pop();
    } catch (error, stack) {
      // 这个 catch 块专门用来捕获 startTimer 可能抛出的异常
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.createTimer,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    }
  }
  
  /// 核心方法：准备并保存任务数据
  /// 成功则返回 taskId，失败则返回 null
  Future<String> _prepareAndSaveTask() async {
    if (!(_formKey.currentState?.validate() ?? false)) return '';

    final taskState = Provider.of<TaskState>(context, listen: false);

    try {
      final name = _nameController.text.trim();
      
      int? dueDate;
      DateTime? dueTime;
      String? timezone;
      
      if (_dueDate != null) {
        if (_dueClock == null) {
          // 全天任务
          dueDate = int.tryParse(DateFormat('yyyyMMdd').format(_dueDate!));
        } else {
          // 精确时刻任务
          dueTime = DateTime(
            _dueDate!.year,
            _dueDate!.month,
            _dueDate!.day,
            _dueClock!.hour,
            _dueClock!.minute,
          );
        }
      }

      if (_isEditMode) {
        final originalTask = widget.task!;
        // 编辑模式：仅当截止信息改变时才更新时区
        final bool timeChanged = dueDate != originalTask.dueDate || dueTime != originalTask.dueTime;
        if (timeChanged) {
          timezone = await FlutterTimezone.getLocalTimezone();
        } else {
          timezone = originalTask.timezone; // 沿用旧的时区
        }
      } else {
        // 新建模式：总是获取当前时区
        timezone = await FlutterTimezone.getLocalTimezone();
      }
      
      String taskId = '';

      if (_isEditMode) {
        // 更新现有任务
        final updatedTask = widget.task!.copyWith(
          name: ExplicitValue(name),
          timezone: ExplicitValue(timezone),
          dueDate: ExplicitValue(dueDate),
          dueTime: ExplicitValue(dueTime),
          projectId: ExplicitValue(_projectId),
          isImportant: ExplicitValue(_isImportant),
          isUrgent: ExplicitValue(_isUrgent),
        );

        // 任务数据有变化才更新 (Equatable 使得比较变得简单)
        if (updatedTask != widget.task) {
          await taskState.updateTask(updatedTask);
        }
        taskId = updatedTask.id;
      } else {
        // 创建新任务
        final newTask = TaskModel(
          id: '',
          userId: '',
          createTime: DateTime.now(),
          updateTime: DateTime.now(),
          name: name,
          timezone: timezone,
          dueDate: dueDate,
          dueTime: dueTime,
          projectId: _projectId,
          isImportant: _isImportant,
          isUrgent: _isUrgent,
        );

        final createdTask = await taskState.createTask(newTask);
        taskId = createdTask?.id ?? '';
      }
      
      return taskId;

    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception, 
          how: _isEditMode ? AppExceptionHow.updateTask : AppExceptionHow.createTask,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
      return '';
    }
  }

  @override
  void dispose() {
    _nameController.removeListener(_updateNameState);
    _nameController.dispose();
    _nameFocusNode.dispose();
    super.dispose();
  }
}
