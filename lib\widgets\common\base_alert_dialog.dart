// import 'dart:developer' as developer;
import 'package:flutter/material.dart';

class Base<PERSON>lertDialog extends StatelessWidget {
  final String title;
  final String content;
  final List<Widget> actions;

  const BaseAlertDialog({
    super.key,
    required this.title,
    required this.content,
    required this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      insetPadding: const EdgeInsets.all(12.0),
      titleTextStyle: Theme.of(context).textTheme.titleMedium,
      titlePadding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 16.0, bottom: 0.0),
      contentPadding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 16.0, bottom: 8.0),
      actionsPadding: const EdgeInsets.only(left: 4.0, right: 4.0, top: 0.0, bottom: 2.0),
      buttonPadding: EdgeInsets.zero,
      title: Text(title),
      content: Text(content),
      actions: actions,
    );
  }
}
