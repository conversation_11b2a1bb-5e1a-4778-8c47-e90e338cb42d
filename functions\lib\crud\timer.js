import { onCall, HttpsError } from 'firebase-functions/v2/https';
import * as logger from 'firebase-functions/logger';
import { db, FIREBASE, DATABASE } from '../tools/config.js';
/**
 * 删除一个计时器。
 */
export const deleteTaskTimer = onCall({ region: FIREBASE.REGION }, async (request) => {
    if (!request.auth) {
        throw new HttpsError('unauthenticated', 'Authentication is required for this operation');
    }
    const uid = request.auth.uid;
    const { timerId } = request.data;
    if (!timerId || typeof timerId !== 'string') {
        throw new HttpsError('invalid-argument', 'A valid timerId must be provided');
    }
    try {
        const docRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid).collection(DATABASE.TIMERS_COLLECTION).doc(timerId);
        await docRef.delete();
        return { success: true };
    }
    catch (error) {
        logger.error(`为用户 ${uid} 删除计时器 ${timerId} 失败:`, error);
        throw new HttpsError('internal', 'An unknown error occurred while deleting the timer');
    }
});
//# sourceMappingURL=timer.js.map