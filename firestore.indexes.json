{"indexes": [{"collectionGroup": "tasks", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "recurringId", "order": "ASCENDING"}, {"fieldPath": "completeTime", "order": "ASCENDING"}]}, {"collectionGroup": "tasks", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "recurringId", "order": "ASCENDING"}, {"fieldPath": "dropTime", "order": "ASCENDING"}]}, {"collectionGroup": "tasks", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "recurringId", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}]}, {"collectionGroup": "tasks", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "recurringId", "order": "ASCENDING"}, {"fieldPath": "dueTime", "order": "ASCENDING"}]}, {"collectionGroup": "tasks", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "recurringId", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "DESCENDING"}]}, {"collectionGroup": "tasks", "queryScope": "COLLECTION_GROUP", "fields": [{"fieldPath": "recurringId", "order": "ASCENDING"}, {"fieldPath": "dueTime", "order": "DESCENDING"}]}], "fieldOverrides": []}