// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// states
import '../../states/project_state.dart';
// services
import '../../services/permission_service.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
// sheets
import '../project/project_edit_sheet.dart';
import '../common/subscribe_guide_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TaskProjectPicker extends StatelessWidget {
  final String? selectedProjectId;
  final Function(String?) onSelected;

  const TaskProjectPicker({
    super.key,
    required this.selectedProjectId,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final projectState = context.watch<ProjectState>();
    final bool showClearButton = selectedProjectId != null;
    final selectedProject = projectState.getProjectById(selectedProjectId ?? '');

    return InkWell(
      // 将过滤后的列表传递给 _showProjectSheet
      onTap: () {
        // 增加震动反馈
        HapticFeedback.lightImpact();

        _showProjectSheet(context);
      },
      child: Row(
        children: [
          // 左侧图标
          Icon(
            LucideIcons.folder,
            size: 20,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 24),

          if (selectedProject != null)
            Container(
              width: 12,
              height: 12,
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                color: selectedProject.color,
                shape: BoxShape.circle,
              ),
            ),

          Expanded(
            child: Text(
              selectedProject?.name ?? l10n.taskProjectPicker_noProject,
              style: const TextStyle(fontSize: 16),
            ),
          ),

          if (showClearButton)
            InkWell(
              customBorder: const CircleBorder(),
              onTap: () => onSelected(null),
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 4.0),
                child: Icon(
                  LucideIcons.x,
                  size: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showProjectSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => _buildTaskProjectSheet(context),
    );
  }

  Widget _buildTaskProjectSheet(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final projectState = context.watch<ProjectState>();
    final activeProjects = projectState.projects.where((p) => !p.isArchived).toList();

    return BaseBottomSheet(
      fullWidth: true,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 项目列表
          if (activeProjects.isEmpty)
            Center(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.0, vertical: 32.0),
                child: Text(l10n.taskProjectPicker_emptyMessage),
              ),
            )
          else
            ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.4,
              ),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: activeProjects.length,
                itemBuilder: (context, index) {
                  final project = activeProjects[index];
                  return ListTile(
                    visualDensity: const VisualDensity(vertical: -2),
                    leading: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: project.color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    title: Text(project.name, style: const TextStyle(fontSize: 16)),
                    onTap: () {
                      // 增加震动反馈
                      HapticFeedback.lightImpact();
                      // 关闭底部弹出框
                      onSelected(project.id);
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),

          const Divider(height: 32),

          // 创建新项目按钮
          Center(
            child: TextButton.icon(
              icon: const Icon(LucideIcons.plus, size: 16),
              label: Text(l10n.taskProjectPicker_createProject),
              onPressed: () async {
                // 增加震动反馈
                HapticFeedback.lightImpact();
                // 先关闭底部弹出框
                Navigator.pop(context);
                if (projectState.canCreateProject()) {
                  final newProject = await ProjectEditSheet.show(context);
                  if (newProject != null) {
                    onSelected(newProject.id);
                  }
                } else {
                  SubscribeGuideSheet.show(
                    context,
                    feature: Feature.project,
                  );
                }
              },
            ),
          ),
        ],
      ),
    );
  }
}
