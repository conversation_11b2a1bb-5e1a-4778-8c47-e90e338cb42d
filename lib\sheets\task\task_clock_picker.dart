// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// widgets
import '../../widgets/common/scrollable_time_picker.dart';
import '../../widgets/common/base_bottom_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TaskClockPicker extends StatelessWidget {
  final TimeOfDay? selectedTime;
  final Function(TimeOfDay?) onSelected;
  final DateTime? dueDate; // 可能为 null
  final VoidCallback onSetDueDateToToday; // 当需要设置日期为今天时的回调

  const TaskClockPicker({
    super.key,
    required this.selectedTime,
    required this.onSelected,
    required this.dueDate,
    required this.onSetDueDateToToday,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final bool showClearButton = selectedTime != null;

    return InkWell(
      onTap: () {
        // 增加震动反馈
        HapticFeedback.lightImpact();

        _showBottomSheet(context);
      },
      child: Row(
        children: [
          // 左侧图标
          Icon(
            LucideIcons.clock,
            size: 20,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 24),

          Expanded(
            child: Text(
              selectedTime == null ? l10n.taskClockPicker_noDueClock : _formatTime(selectedTime!),
              style: const TextStyle(fontSize: 16),
            ),
          ),
          if (showClearButton)
            InkWell(
              customBorder: const CircleBorder(),
              onTap: () => onSelected(null),
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 4.0),
                child: Icon(
                  LucideIcons.x,
                  size: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  // 底部弹出选择器
  void _showBottomSheet(BuildContext context) {
    if (dueDate == null) {
      // 回调让 setState 执行，确保父组件的 dueDate 已设置
      onSetDueDateToToday();
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => TaskClockSheet(
        initialTime: selectedTime ?? TimeOfDay.now(),
        onConfirm: (time) {
          onSelected(time);
          Navigator.of(context).pop();
        },
      ),
    );
  }

  String _formatTime(TimeOfDay time) {
    final hours = time.hour.toString().padLeft(2, '0');
    final minutes = time.minute.toString().padLeft(2, '0');
    return '$hours:$minutes';
  }
}

class TaskClockSheet extends StatefulWidget {
  final TimeOfDay initialTime;
  final Function(TimeOfDay) onConfirm;

  const TaskClockSheet({
    super.key,
    required this.initialTime,
    required this.onConfirm,
  });

  @override
  State<TaskClockSheet> createState() => _TaskClockSheetState();
}

class _TaskClockSheetState extends State<TaskClockSheet> {
  late TimeOfDay selectedTime;

  @override
  void initState() {
    super.initState();
    selectedTime = widget.initialTime;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return BaseBottomSheet(
      title: l10n.taskClockPicker_title,
      onConfirm: () => widget.onConfirm(selectedTime),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 快捷选择按钮
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Row(
              children: [
                Expanded(
                  child: _buildQuickButton(context, '09:00', const TimeOfDay(hour: 9, minute: 0)),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildQuickButton(context, '12:00', const TimeOfDay(hour: 12, minute: 0)),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildQuickButton(context, '15:00', const TimeOfDay(hour: 15, minute: 0)),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildQuickButton(context, '18:00', const TimeOfDay(hour: 18, minute: 0)),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildQuickButton(context, '21:00', const TimeOfDay(hour: 21, minute: 0)),
                ),
              ],
            ),
          ),
          
          const Divider(height: 32),

          // 使用新的时间选择器组件
          ScrollableTimePicker(
            initialTime: selectedTime,
            onTimeChanged: (newTime) {
              setState(() {
                selectedTime = newTime;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildQuickButton(BuildContext context, String label, TimeOfDay time) {
    final colorScheme = Theme.of(context).colorScheme;
    return SizedBox(
      height: 36,
      child: ElevatedButton(
        onPressed: () {
          // 增加震动反馈
          HapticFeedback.lightImpact();
          widget.onConfirm(time);
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          padding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: colorScheme.onPrimary,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
