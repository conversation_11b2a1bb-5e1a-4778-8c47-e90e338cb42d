// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/user_model.dart';
// states
import '../../states/user_state.dart';
// tools
import '../../tools/extensions.dart';
import '../../tools/app_exception.dart';
import '../../tools/app_exception_mapper.dart';
// widgets
import '../../widgets/common/base_action_dialog.dart';
import '../../widgets/common/app_exception_snackbar.dart';
// sheets
import '../../sheets/setting/change_username_sheet.dart';
import '../../sheets/setting/delete_account_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class AccountPage extends StatefulWidget {
  const AccountPage({super.key});

  @override
  State<AccountPage> createState() => _AccountPageState();
}

class _AccountPageState extends State<AccountPage> {
  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final userState = context.watch<UserState>();
    final UserModel? user = userState.user;

    if (user == null || user.isAnonymous) {
      return Scaffold(
        appBar: AppBar(title: Text(l10n.accountPage_title)),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    final subscription = user.subscription;
    final int calendarDays = _calculateCalendarDays(user.createTime);

    // 获取订阅日期和状态文本

    return Scaffold(
      appBar: AppBar(title: Text(l10n.accountPage_title)),
      body: Column(
        children: [
          Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 700),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Column(
                  children: <Widget>[
                    const SizedBox(height: 8),

                    // 用户信息行
                    // 卡片1: 用户信息
                    Card(
                      clipBehavior: Clip.antiAlias,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const SizedBox(height: 4.0), // 把文字往下压一点
                                  Text(user.name, style: textTheme.titleLarge),
                                  const SizedBox(height: 4.0),
                                  Text(
                                    user.email ?? "anonymous",
                                    style: textTheme.bodyLarge?.copyWith(
                                      color: colorScheme.onSurfaceVariant,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 16.0),
                            _buildUserAvatar(context, user),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 12),

                    // 订阅信息行
                    Row(
                      children: [
                        // 卡片2: 当前方案
                        Expanded(
                          child: _buildInfoCard(
                            context: context,
                            icon: LucideIcons.badgeCheck,
                            title: l10n.accountPage_currentPlan,
                            value: _getPlanText(context, subscription),
                            color: colorScheme.primaryContainer,
                          ),
                        ),
                        const SizedBox(width: 12),
                        // 卡片3: 到期/续订日
                        Expanded(
                          child: _buildInfoCard(
                            context: context,
                            icon: LucideIcons.badgePlus,
                            title: subscription.isRenewal
                              ? l10n.accountPage_renewsOn
                              : l10n.accountPage_expiresOn,
                            value: _getExpiryText(context, subscription),
                            color: colorScheme.primaryContainer,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 12),

                    // 注册信息行
                    Row(
                      children: [
                        // 卡片4: 注册天数
                        Expanded(
                          child: _buildInfoCard(
                            context: context,
                            icon: LucideIcons.bean,
                            title: l10n.accountPage_joined,
                            value: l10n.accountPage_days(calendarDays),
                          ),
                        ),
                        const SizedBox(width: 12),
                        // 卡片5: 注册方式
                        Expanded(
                          child: _buildInfoCard(
                            context: context,
                            icon: LucideIcons.link2,
                            title: l10n.accountPage_signUpMethod,
                            value: user.authProvider.name.toCapitalized(),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24.0),
                  ],
                ),
              ),
            ),
          ),
          
          // 设置项
          Expanded(
            child: ListView(
              children: [
                const Divider(),
                ListTile(
                  leading: const Icon(LucideIcons.drama),
                  title: Text(l10n.accountPage_changeUsername),
                  onTap: () {
                    showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      builder: (BuildContext context) {
                        return const ChangeUsernameSheet();
                      },
                    );
                  },
                ),
                const Divider(),
                ListTile(
                  leading: Icon(Icons.logout, color: colorScheme.error),
                  title: Text(l10n.accountPage_signOut, style: TextStyle(color: colorScheme.error)),
                  onTap: _handleSignOut,
                ),
                const Divider(),
              ],
            ),
          ),

          // 删除按钮
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: TextButton(
              onPressed: () {
                showModalBottomSheet(
                  context: context,
                  isScrollControlled: true,
                  builder: (BuildContext context) {
                    return const DeleteAccountSheet();
                  },
                );
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Icon(
                    LucideIcons.trash2,
                    color: colorScheme.error,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    l10n.accountPage_deleteAccount,
                    style: TextStyle(
                      color: colorScheme.error,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserAvatar(BuildContext context, UserModel user) {
    final colorScheme = Theme.of(context).colorScheme;
    final bool hasPhotoURL = user.photoURL != null && user.photoURL!.isNotEmpty;

    if (hasPhotoURL) {
      return CircleAvatar(
        radius: 24,
        backgroundImage: NetworkImage(user.photoURL!),
        backgroundColor: Colors.transparent,
      );
    } else {
      return CircleAvatar(
        radius: 24,
        backgroundColor: Colors.transparent,
        child: Icon(
          LucideIcons.circleUserRound,
          size: 48,
          color: colorScheme.primary,
        ),
      );
    }
  }

  Widget _buildInfoCard({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String value,
    Color? color,
  }) {
    return Card(
      color: color,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(icon, size: 24),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.labelMedium,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    value,
                    style: Theme.of(context).textTheme.titleMedium,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  String _getPlanText(BuildContext context, UserSubscription subscription) {
    final l10n = AppLocalizations.of(context)!;
    
    // 1. 优先处理非订阅情况，提前返回
    if (!subscription.isPremium) return '--';

    // 2. 只有订阅用户才会执行 switch
    switch (subscription.plan) {
      case SubscriptionPlan.monthly:
        return l10n.accountPage_planMonthly;
      case SubscriptionPlan.yearly:
        return l10n.accountPage_planYearly;
      case SubscriptionPlan.lifetime:
        return l10n.accountPage_planLifetime;
      default:
        return l10n.common_unknown;
    }
  }

  String _getExpiryText(BuildContext context, UserSubscription subscription) {
    final l10n = AppLocalizations.of(context)!;
    final formatter = DateFormat('yyyy-MM-dd');

    // 1. 优先处理非订阅情况
    if (!subscription.isPremium) return '--';

    // 2. 处理终身订阅
    if (subscription.plan == SubscriptionPlan.lifetime) {
      return l10n.accountPage_planLifetime;
    }

    // 3. 安全地处理普通订阅的日期
    if (subscription.endTime != null) {
      return formatter.format(subscription.endTime!);
    } else {
      return '--'; // 如果 endTime 为空，提供一个回退值
    }
  }

  int _calculateCalendarDays(DateTime from) {
    // 忽略具体时间，只计算日期差值
    // 避免 3 日晚上注册，4 日凌晨不足一天的问题
    final now = DateTime.now();
    final startOfToday = DateTime(now.year, now.month, now.day);
    final startOfFromDay = DateTime(from.year, from.month, from.day);
    final difference = startOfToday.difference(startOfFromDay).inDays;
    return difference;
  }

  void _handleSignOut() async {
    final l10n = AppLocalizations.of(context)!;

    final result = await BaseActionDialog.show(
      context,
      title: l10n.accountPage_confirmSignOutTitle,
      content: l10n.accountPage_confirmSignOutContent,
      cancelText: l10n.common_cancel,
      confirmText: l10n.common_confirm,
      onConfirm: () => context.read<UserState>().signOut(),
    );

    // 处理异步操作的结果
    if (result == true) {
      // 登出成功
      if (mounted) Navigator.of(context).pop();
    } else if (result is Exception) {
      // 登出失败
      if (mounted) {
        final appException = appExceptionMapper(
          error: result,
          how: AppExceptionHow.signOut,
        );
        showAppExceptionSnackBar(context, appException);
      }
    }
  }
}
