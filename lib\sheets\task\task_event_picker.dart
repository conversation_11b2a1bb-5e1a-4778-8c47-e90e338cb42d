// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
import '../../widgets/common/scrollable_time_picker.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TaskEventPicker extends StatelessWidget {
  final DateTime? startDateTime;
  final DateTime? endDateTime;
  final Function(DateTime?, DateTime?) onSelected;
  final DateTime? dueDate; // 可能为 null
  final VoidCallback onSetDueDateToToday;

  const TaskEventPicker({
    super.key,
    required this.startDateTime,
    required this.endDateTime,
    required this.onSelected,
    required this.dueDate,
    required this.onSetDueDateToToday,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final bool showClearButton = startDateTime != null || endDateTime != null;

    return InkWell(
      onTap: () {
        // 增加震动反馈
        HapticFeedback.lightImpact();

        _showBottomSheet(context);
      },
      child: Row(
        children: [
          // 左侧图标
          Icon(
            LucideIcons.briefcase,
            size: 20,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 24),
          
          Expanded(
            child: Text(
              _getDisplayText(l10n),
              style: const TextStyle(fontSize: 16),
            ),
          ),
          
          // 清除按钮（仅当有值时显示）
          if (showClearButton)
            InkWell(
              customBorder: const CircleBorder(),
              onTap: () => onSelected(null, null),
              child: const Padding(
                padding: EdgeInsets.symmetric(horizontal: 4.0),
                child: Icon(
                  LucideIcons.x,
                  size: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 获取显示文本
  String _getDisplayText(AppLocalizations l10n) {
    final defaultText = l10n.taskEventPicker_noEventClock;
    if (startDateTime == null && endDateTime == null) {
      return defaultText;
    }
    
    final formatter = DateFormat('HH:mm');
    final startText = startDateTime != null ? formatter.format(startDateTime!) : defaultText;
    final endText = endDateTime != null ? formatter.format(endDateTime!) : defaultText;
    return '$startText - $endText';
  }

  // 底部弹出选择器
  void _showBottomSheet(BuildContext context) {
    if (dueDate == null) {
      // 回调让 setState 执行，确保父组件的 dueDate 已设置
      onSetDueDateToToday();
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => TaskEventSheet(
        // 回调的结果是异步更新的，所以如果父组件没有提供日期，要设置为当前日期
        baseDate: dueDate ?? DateTime.now(),
        startTime: startDateTime,
        endTime: endDateTime,
        onConfirm: (start, end) {
          onSelected(start, end);
          Navigator.of(context).pop();
        },
      ),
    );
  }
}

// 底部弹出的时间选择器组件
class TaskEventSheet extends StatefulWidget {
  final DateTime? baseDate;
  final DateTime? startTime;
  final DateTime? endTime;
  final Function(DateTime?, DateTime?) onConfirm;

  const TaskEventSheet({
    super.key,
    required this.baseDate,
    required this.startTime,
    required this.endTime,
    required this.onConfirm,
  });

  @override
  State<TaskEventSheet> createState() => _TaskEventSheetState();
}

class _TaskEventSheetState extends State<TaskEventSheet> {
  late DateTime? _startDateTime;
  late DateTime? _endDateTime;
  bool _editingStartTime = true; // 标记当前编辑的是开始时间还是结束时间

  @override
  void initState() {
    super.initState();
    _startDateTime = widget.startTime;
    _endDateTime = widget.endTime;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BaseBottomSheet(
      title: l10n.taskEventPicker_title,
      onConfirm: () {
        if (_startDateTime == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(l10n.taskEventPicker_errorNoStartClock)),
          );
          return;
        }
        if (_endDateTime == null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(l10n.taskEventPicker_errorNoEndClock)),
          );
          return;
        }
        if (_endDateTime!.isBefore(_startDateTime!)) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(l10n.taskEventPicker_errorEndClockBeforeStartClock)),
          );
          return;
        }
        widget.onConfirm(_startDateTime, _endDateTime);
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间选择部分
          Row(
            children: [
              Expanded(
                child: _buildTimeButton(
                  context,
                  l10n.taskEventPicker_startClockLabel,
                  _startDateTime,
                  true,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildTimeButton(
                  context,
                  l10n.taskEventPicker_endClockLabel,
                  _endDateTime,
                  false,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          // 使用新的时间选择器组件，添加关键的key
          ScrollableTimePicker(
            // 添加key，强制在切换编辑对象时重建组件
            key: ValueKey(_editingStartTime),
            initialTime: TimeOfDay.fromDateTime(
                (_editingStartTime ? _startDateTime : _endDateTime) ?? DateTime.now()),
            onTimeChanged: (newTime) {
              // 将 TimeOfDay 转换为 DateTime 并处理
              final date = widget.baseDate ?? DateTime.now();
              final newDateTime =
                  DateTime(date.year, date.month, date.day, newTime.hour, newTime.minute);
              _handleTimeSelection(newDateTime);
            },
          ),
        ],
      ),
    );
  }

  // 构建时间按钮
  Widget _buildTimeButton(
    BuildContext context,
    String label,
    DateTime? time,
    bool isStart,
  ) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;
    final isSelected = isStart == _editingStartTime;
    
    return InkWell(
      onTap: () {
        setState(() {
          _editingStartTime = isStart;
        });
      },
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        margin: const EdgeInsets.only(top: 8),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected 
                ? colorScheme.primary 
                : colorScheme.outline,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(8),
          color: isSelected 
              ? colorScheme.primary.withValues(alpha: 0.1)
              : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label, 
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: isSelected 
                    ? colorScheme.primary
                    : colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              time == null ? l10n.taskEventPicker_selectClock : DateFormat('HH:mm').format(time),
              style: TextStyle(
                fontSize: 16,
                color: isSelected 
                    ? colorScheme.primary
                    : colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 处理时间选择 - 只更新对应的值，不做任何自动操作
  void _handleTimeSelection(DateTime newTime) {
    setState(() {
      if (_editingStartTime) {
        _startDateTime = newTime;
      } else {
        _endDateTime = newTime;
      }
    });
  }
}
