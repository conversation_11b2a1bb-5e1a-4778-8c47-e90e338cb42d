import { QueryDocumentSnapshot, Timestamp } from 'firebase-admin/firestore';
import { onSchedule, ScheduledEvent, ScheduleOptions } from 'firebase-functions/v2/scheduler';
import * as logger from 'firebase-functions/logger';
import { db, DATABASE, RECURRING } from '../tools/config.js';
import type { RecurringModel, TaskModel } from '../tools/types.js';
import TimeUtils from '../tools/time.js';
import { generateTasksForPeriod } from './utils.js';


const scheduleOptions: ScheduleOptions = {
  schedule: RECURRING.SCHEDULE_TIME,
  timeZone: DATABASE.DEFAULT_TIMEZONE,
};


/**
 * 计划函数 (每日运行)：为所有用户的周期性任务按需生成未来的任务实例
 * 功能：遍历所有用户和他们的周期任务，检查并补充尚未生成的任务实例，以确保持续生成到未来 N 天
 * 这个函数是幂等且可自愈的
 */
export const onRecurringSchedule = onSchedule(
  scheduleOptions,
  async (event: ScheduledEvent) => {
    logger.info('onRecurringSchedule 正在运行', { timestamp: event.scheduleTime });

    try {
      const usersSnapshot = await db.collection(DATABASE.USERS_COLLECTION).get();
      if (usersSnapshot.empty) {
        logger.info('计划任务：未找到任何用户');
        return;
      }

      // 分批并发处理用户，避免内存压力
      const userIds = usersSnapshot.docs.map(doc => doc.id);
      const batchSize = RECURRING.CONCURRENT_USER_LIMIT;
      
      for (let i = 0; i < userIds.length; i += batchSize) {
        const batch = userIds.slice(i, i + batchSize);
        const batchPromises = batch.map(userId => 
          processUserForScheduledTasks(userId).catch(error => {
            logger.error(`处理用户 ${userId} 失败`, { error: error.message });
            // 不抛出错误，继续处理其他用户
          })
        );
        
        await Promise.all(batchPromises);
        logger.info(`已处理用户批次 ${Math.floor(i/batchSize) + 1}/${Math.ceil(userIds.length/batchSize)}`);
      }

      logger.info('周期性任务生成计划已完成处理所有用户');
    } catch (e: unknown) {
      const error = e as Error;
      logger.error('onRecurringSchedule 出错', { error: error.message });
      throw error;
    }
  }
);


/**
 * 获取周期任务的最新任务日期
 */
async function getLatestTaskDate(
  tasksColRef: FirebaseFirestore.CollectionReference,
  recurringId: string,
  isAllDayTask: boolean,
  timezone: string,
): Promise<Date | null> {
  const latestTaskQuery = tasksColRef.where('recurringId', '==', recurringId).orderBy(isAllDayTask ? 'dueDate' : 'dueTime', 'desc').limit(1);
  
  const latestTaskSnapshot = await latestTaskQuery.get();
  
  if (latestTaskSnapshot.empty) return null;

  const latestTask = latestTaskSnapshot.docs[0].data() as TaskModel;
  
  if (isAllDayTask) {
    return TimeUtils.dueDateToUTC(timezone, latestTask.dueDate!);
  } else {
    return (latestTask.dueTime as Timestamp).toDate();
  }
}


/**
 * 辅助函数：为单个用户处理周期任务，确保任务实例生成到未来 N 天
 * 此函数是幂等且可自愈的
 * @param {string} userId - 用户 ID
 * @returns {Promise<void>}
 */
async function processUserForScheduledTasks(userId: string): Promise<void> {
  const recurringsColRef = db.collection(DATABASE.USERS_COLLECTION).doc(userId).collection(DATABASE.RECURRINGS_COLLECTION);
  const tasksColRef = db.collection(DATABASE.USERS_COLLECTION).doc(userId).collection(DATABASE.TASKS_COLLECTION);

  try {
    const recurringsSnapshot = await recurringsColRef.get();
    if (recurringsSnapshot.empty) return;

    for (const recurringDoc of recurringsSnapshot.docs as QueryDocumentSnapshot<RecurringModel>[]) {
      const recurringId = recurringDoc.id;
      const recurring = recurringDoc.data();
      
      if (!recurring.rule || !recurring.template || !recurring.timezone) {
        logger.warn('跳过周期任务，因其缺少必要数据', { userId, recurringId });
        continue;
      }

      const timezone = recurring.timezone;
      const isAllDayTask = TimeUtils.isAllDayTask(recurring.template);
      
      // 计算生成范围
      const { startDate, endDate: generationEndDate } = TimeUtils.calculateGenerationRange(recurring.timezone);
      // 获取最新任务日期
      const latestTaskDate = await getLatestTaskDate(tasksColRef, recurringId, isAllDayTask, timezone);
      // 计算最终的生成开始日期
      const generationStartDate = latestTaskDate ? TimeUtils.getNextDayStart(timezone, latestTaskDate) : startDate;

      // 检查是否需要生成
      if (generationStartDate > generationEndDate) {
        logger.debug(`周期任务 ${recurringId} 的任务已生成到指定范围，无需操作`, { userId });
        continue;
      }

      // 生成任务
      logger.debug(`为周期任务 ${recurringId} 生成任务，范围从 ${generationStartDate.toISOString()} 到 ${generationEndDate.toISOString()}`, { userId });
      await generateTasksForPeriod(userId, recurringId, recurring, generationStartDate, generationEndDate);
    }
  } catch (e: unknown) {
    const error = e as Error;
    logger.error(`为用户 ${userId} 处理计划任务时出错`, { error: error.message, stack: error.stack });
    throw error;
  }
}
