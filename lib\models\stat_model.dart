// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// import 'package:equatable/equatable.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import './recurring_model.dart';
// states
import '../states/recurring_state.dart';
// widgets
import '../widgets/chart/weekly_task_chart.dart';
import '../widgets/chart/weekly_timer_chart.dart';
import '../widgets/chart/habit_task_chart.dart';
import '../widgets/chart/habit_timer_chart.dart';
import '../widgets/chart/time_progress_chart.dart';
// others
import '../../generated/l10n/app_localizations.dart';

/// 统计图展示分类
enum StatCategory {
  task,             // 任务相关
  timer,            // 计时相关
  habit,            // 习惯追踪相关
  other,            // 其他
}

/// 统计图数据类型
enum StatDataType {
  task,             // 任务
  timer,            // 计时
  other,            // 其他
}

class StatModel {
  final String id;              // 唯一ID (拼接结构：baseId:dataType)
  final String baseId;          // 基础ID (标准统计图用预定义ID, 习惯统计图用 recurring ID)
  final StatDataType dataType;  // 数据类型
  String name;                  // 名称
  String description;           // 描述
  final IconData icon;          // 图标 (用于描述是什么类型的图表，比如barchart、linechart)
  final StatCategory category;  // 展示分类
  final Widget Function(BuildContext context, {String? recurringId}) widgetBuilder;

  StatModel({
    required this.id,
    required this.baseId,
    required this.dataType,
    this.name = '',
    this.description = '',
    required this.icon,
    required this.category,
    required this.widgetBuilder,
  });

  // --- 预定义的标准统计图 ---

  static final StatModel weeklyTaskChart = StatModel(
    id: 'J3mK7nP1xV4bT9cE6qR2g:task',
    baseId: 'J3mK7nP1xV4bT9cE6qR2g',
    dataType: StatDataType.task,
    icon: LucideIcons.chartColumn,
    category: StatCategory.task,
    widgetBuilder: _weeklyTaskBuilder,
  );
  // 使用静态方法引用，这样可以保留 StatModel 的 const 声明
  static Widget _weeklyTaskBuilder(BuildContext context, {String? recurringId}) => const WeeklyTaskChart();

  static final StatModel weeklyTimerChart = StatModel(
    id: 'B2xH5eT7qJ1oK8vM0nW4u:timer',
    baseId: 'B2xH5eT7qJ1oK8vM0nW4u',
    dataType: StatDataType.timer,
    icon: LucideIcons.chartLine,
    category: StatCategory.timer,
    widgetBuilder: _weeklyTimerBuilder,
  );
  static Widget _weeklyTimerBuilder(BuildContext context, {String? recurringId}) => const WeeklyTimerChart();

  static final StatModel timeProgressChart = StatModel(
    id: 'Z6tA9yD0mN8wF3rL5sC1h:other',
    baseId: 'Z6tA9yD0mN8wF3rL5sC1h',
    dataType: StatDataType.other,
    icon: LucideIcons.loaderCircle,
    category: StatCategory.other,
    widgetBuilder: _timeProgressBuilder,
  );
  static Widget _timeProgressBuilder(BuildContext context, {String? recurringId}) => const TimeProgressChart();

  // --- 辅助方法和列表 ---

  /// 获取所有预定义的统计类型
  static List<StatModel> get values => [
    weeklyTaskChart,
    weeklyTimerChart,
    timeProgressChart,
  ];

  /// 从 RecurringModel 创建 StatModel (用于习惯追踪图)
  factory StatModel.fromRecurring(RecurringModel recurring, StatDataType dataType) {
    IconData iconData = LucideIcons.activity; // Default icon
    Widget Function(BuildContext, {String? recurringId}) builder;

    if (dataType == StatDataType.task) {
      iconData = LucideIcons.calendar;
      builder = (context, {recurringId}) => HabitTaskChart(recurringId: recurring.id);
    } else if (dataType == StatDataType.timer) {
      iconData = LucideIcons.chartColumn;
      builder = (context, {recurringId}) => HabitTimerChart(recurringId: recurring.id);
    } else {
      // 习惯追踪图的 dataType 只有两种类型
      throw ArgumentError('Unsupported dataType: $dataType');
    }

    return StatModel(
      id: '${recurring.id}:${dataType.name}', // 拼接ID
      baseId: recurring.id,
      dataType: dataType,
      icon: iconData,
      category: StatCategory.habit, // 所有 recurring 衍生的图都属于 habit 分类
      widgetBuilder: builder,
    );
  }

  /// 根据ID查找预定义的标准统计类型
  /// 注意：这个方法 *不会* 查找动态生成的习惯统计图，因为它们不在这里定义
  static StatModel? fromId(String id) {
    try {
      return values.firstWhere((stat) => stat.id == id);
    } catch (e) {
      return null;
    }
  }

  static StatModel? fromConcatenatedId(String concatenatedId, RecurringState recurringState) {
    final parts = concatenatedId.split(':');
    if (parts.length != 2) {
      // 无效的ID格式
      return null;
    }
    final baseId = parts[0];
    final dataTypeName = parts[1];

    StatDataType? dataType;
    try {
      dataType = StatDataType.values.firstWhere((e) => e.name == dataTypeName);
    } catch (e) {
      // 无效的 dataType 名称
      return null;
    }

    // 1. 尝试从预定义的标准图表中查找
    // 标准图表的 StatModel 实例在其定义时就已经包含了正确的 baseId 和 dataType
    for (final standardStat in values) {
      if (standardStat.baseId == baseId && standardStat.dataType == dataType) {
        // 确保ID也完全匹配，因为不同的标准图可能共享dataType但有不同baseId (虽然目前没有这种情况)
        if (standardStat.id == concatenatedId) {
           return standardStat;
        }
      }
    }

    // 2. 如果不是标准图表，则尝试作为习惯图表处理
    // 这意味着 baseId 应该是一个 recurringId
    RecurringModel? recurring;
    try {
      recurring = recurringState.recurrings.firstWhere(
        (r) => r.id == baseId,
      );
    } catch(e) {
      // 没找到
    }

    if (recurring != null) {
      // 如果找到了 recurringModel，并且 dataType 是任务或计时，则构建
      if (dataType == StatDataType.task || dataType == StatDataType.timer) {
        return StatModel.fromRecurring(recurring, dataType);
      }
    }

    // 如果以上都未匹配成功
    return null;
  }

  /// 使用 l10n 对象重建 UI 文本
  void rebuild(AppLocalizations l10n, {RecurringModel? recurring}) {
    if (category == StatCategory.habit) {
      final habitName = recurring?.template.name ?? '';
      if (dataType == StatDataType.task) {
        name = l10n.statModel_habitTaskName(habitName);
        description = l10n.statModel_habitTaskDescription(habitName);
      } else { // timer
        name = l10n.statModel_habitTimerName(habitName);
        description = l10n.statModel_habitTimerDescription(habitName);
      }
    } else {
      if (baseId == weeklyTaskChart.baseId) {
        name = l10n.statModel_weeklyTaskName;
        description = l10n.statModel_weeklyTaskDescription;
      }
      
      else if (baseId == weeklyTimerChart.baseId) {
        name = l10n.statModel_weeklyTimerName;
        description = l10n.statModel_weeklyTimerDescription;
      }
      
      else if (baseId == timeProgressChart.baseId) {
        name = l10n.statModel_timeProgressName;
        description = l10n.statModel_timeProgressDescription;
      }
    }
  }

  // Equatable 需要比较的属性
  // @override
  // List<Object?> get props => [id];
}
