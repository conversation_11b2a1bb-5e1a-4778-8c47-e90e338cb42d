import { onCall, HttpsError } from 'firebase-functions/v2/https';
import * as logger from 'firebase-functions/logger';
import { db, FIREBASE, DATABASE } from '../tools/config.js';


/**
 * 删除一个周期任务及其所有关联的、未完成的任务实例。
 */
export const deleteRecurring = onCall({ region: FIREBASE.REGION }, async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'Authentication is required for this operation');
  }

  const uid = request.auth.uid;
  const { recurringId } = request.data;

  if (!recurringId || typeof recurringId !== 'string') {
    throw new HttpsError('invalid-argument', 'A valid recurringId must be provided');
  }

  const recurringRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid).collection(DATABASE.RECURRINGS_COLLECTION).doc(recurringId);
  const tasksRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid).collection(DATABASE.TASKS_COLLECTION);

  try {
    const batch = db.batch();
    
    // 1. 找到所有关联的、未完成的任务并删除
    //    我们只删除未完成的，已完成/丢弃的任务作为历史记录保留。
    const tasksQuery = await tasksRef
      .where('recurringId', '==', recurringId)
      .where('completeTime', '==', null)
      .where('dropTime', '==', null)
      .get();
      
    tasksQuery.forEach(doc => {
      batch.delete(doc.ref);
    });
    
    // 2. 删除周期任务定义本身
    batch.delete(recurringRef);

    await batch.commit();
    return { success: true };
  } catch (error) {
    logger.error(`为用户 ${uid} 删除周期任务 ${recurringId} 及其关联任务实例失败:`, error);
    throw new HttpsError('internal', 'An unknown error occurred while deleting the recurring task');
  }
});
