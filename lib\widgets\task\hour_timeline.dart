// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// tools
import '../../tools/config.dart';

class HourTimeline extends StatelessWidget {
  final ScrollController scrollController;
  final double timelineWidth;
  final double untimedHeight;
  final double hourHeight;

  const HourTimeline({
    super.key,
    required this.scrollController,
    required this.timelineWidth,
    required this.untimedHeight,
    required this.hourHeight,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: timelineWidth,
      decoration: BoxDecoration(
        border: Border(right: BorderSide(color: Theme.of(context).colorScheme.outlineVariant)),
      ),
      child: ListView.builder(
        controller: scrollController,
        itemCount: 26, // 显示26个元素：1个未规划区域 + 25个时间刻度（00:00到00:00）
        // 添加底部padding，确保最后一个元素完全可见
        padding: EdgeInsets.only(bottom: Config.app.bottomSpace), // 底部留白，防止右下角的添加按钮遮挡内容
        itemBuilder: (context, index) {
          if (index == 0) {
            // 第一个元素只占用未规划区域的高度，不显示内容
            return SizedBox(
              height: untimedHeight,
            );
          } else {
            // 所有时间刻度使用统一样式（包括最后一个00:00）
            final hour = (index - 1) % 24;
            return Container(
              height: hourHeight,
              alignment: Alignment.topCenter,
              child: Text(
                '${hour.toString().padLeft(2, '0')}:00',
                style: TextStyle(fontSize: 10, color: Theme.of(context).colorScheme.onSurfaceVariant),
              ),
            );
          }
        },
      ),
    );
  }
}
