// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// models
import '../../models/task_model.dart';
// widgets
import '../../widgets/task/date_grouped_tasks.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class MatrixTasksPage extends StatelessWidget {
  final String quadrantTitle;
  final List<TaskModel> tasks;
  
  const MatrixTasksPage({
    super.key,
    required this.quadrantTitle,
    required this.tasks,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: AppBar(
        title: Text(quadrantTitle),
      ),
      // 根据DateGroupedTasks的实际需求，使用CustomScrollView
      body: tasks.isEmpty
          ? Center(
              child: Text(
                l10n.eisenhowerMatrixView_emptyMessage,
                style: TextStyle(color: colorScheme.onSurfaceVariant),
              ),
            )
          : CustomScrollView(
              slivers: [
                // 使用DateGroupedTasks - 它返回SliverList
                DateGroupedTasks(tasks: tasks),
              ],
            ),
    );
  }
}
