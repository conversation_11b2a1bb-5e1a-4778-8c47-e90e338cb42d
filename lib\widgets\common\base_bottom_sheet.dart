// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';

class BaseBottomSheet extends StatelessWidget {
  /// 弹窗的标题。如果提供，则必须同时提供 `onConfirm`
  final String? title;

  /// 点击确认按钮的回调。如果提供，则必须同时提供 `title`
  final VoidCallback? onConfirm;

  /// 内容区域是否占据全宽。默认为 `false`
  final bool fullWidth;

  /// 弹窗的主体内容。
  final Widget child;

  const BaseBottomSheet({
    super.key,
    this.title,
    this.onConfirm,
    this.fullWidth = false,
    required this.child,
  }) : assert(
    (title == null && onConfirm == null) || (title != null && onConfirm != null),
    '标题和确认回调必须同时提供或同时不提供',
  );

  @override
  Widget build(BuildContext context) {
    // 整个弹窗的结构，包含头部和安全区域处理
    return Padding(
      // 底部内边距，用于避开系统UI（如键盘）
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 高度自适应
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 可选标题栏
          ...(
            title != null
            ? [
                const SizedBox(height: 12.0),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        title!,
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      InkWell(
                        customBorder: const CircleBorder(),
                        onTap: () {
                          // 增加震动反馈
                          HapticFeedback.lightImpact();
                          onConfirm?.call();
                        },
                        child: const Padding(
                          padding: EdgeInsets.symmetric(horizontal: 4.0),
                          child: Icon(
                            LucideIcons.check,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10.0),
                const Divider(height: 1, indent: 16.0, endIndent: 16.0),
                // 顶部预留空间
                const SizedBox(height: 16.0),
              ]
            : [
                // 顶部预留空间
                const SizedBox(height: 16.0),
              ]
          ),

          // 内容区域
          Flexible(
            // 根据需要添加水平内边距
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: !fullWidth ? 16.0 : 0.0),
              child: child,
            ),
          ),

          // 底部预留空间
          const SizedBox(height: 32.0),
        ],
      ),
    );
  }
}
