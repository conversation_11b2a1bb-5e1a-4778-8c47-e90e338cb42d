// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// models
import '../models/stat_model.dart';
// states
import '../states/stat_state.dart';
// tools
import '../tools/config.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
// others
import '../generated/l10n/app_localizations.dart';

class StatisticsView extends StatelessWidget {
  const StatisticsView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer<StatState>(
      builder: (context, statState, _) {
        final statsToDisplay = statState.selectedStats;

        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(l10n.statisticsView_title),
          ),
          body: _buildPlaceholder(context, statsToDisplay.isEmpty) ?? ListView.separated(
            itemCount: statsToDisplay.length + 1, // 增加一项用于底部留白
            itemBuilder: (context, index) {
              // 列表末尾添加底部留白
              if (index == statsToDisplay.length) {
                return SizedBox(height: Config.app.bottomSpace);
              }
              final stat = statsToDisplay[index];
              // 调用 StatModel 中定义的 widgetBuilder 来构建每个统计图
              return stat.widgetBuilder(
                context,
                recurringId: stat.category == StatCategory.habit ? stat.baseId : null,
              );
            },
            separatorBuilder: (context, index) => const SizedBox(height: 4),
          ),
        );
      },
    );
  }

  Widget? _buildPlaceholder(BuildContext context, bool isEmpty) {
    final l10n = AppLocalizations.of(context)!;
    return isEmpty ? Center(child: Text(l10n.statisticsView_emptyMessage)) : null;
  }
}
