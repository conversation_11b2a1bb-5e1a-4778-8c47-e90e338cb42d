// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// 一个自定义的、可滚动的24小时制时间选择器
class ScrollableTimePicker extends StatefulWidget {
  final TimeOfDay initialTime;
  final Function(TimeOfDay) onTimeChanged;

  const ScrollableTimePicker({
    super.key,
    required this.initialTime,
    required this.onTimeChanged,
  });

  @override
  State<ScrollableTimePicker> createState() => _ScrollableTimePickerState();
}

class _ScrollableTimePickerState extends State<ScrollableTimePicker> {
  late FixedExtentScrollController _hourController;
  late FixedExtentScrollController _minuteController;

  @override
  void initState() {
    super.initState();
    _hourController = FixedExtentScrollController(initialItem: widget.initialTime.hour);
    _minuteController = FixedExtentScrollController(initialItem: widget.initialTime.minute);
  }
  
  // 当外部的 initialTime 发生变化时，同步更新滚轮的位置
  @override
  void didUpdateWidget(covariant ScrollableTimePicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialTime.hour != oldWidget.initialTime.hour) {
      // 找到离当前位置最近的目标索引，以避免不必要的循环动画
      final targetHourIndex = _findClosestItemIndex(
        _hourController.selectedItem,
        widget.initialTime.hour,
        24,
      );
      _hourController.animateToItem(
        targetHourIndex, 
        duration: const Duration(milliseconds: 200), 
        curve: Curves.easeOut,
      );
    }
    if (widget.initialTime.minute != oldWidget.initialTime.minute) {
      final targetMinuteIndex = _findClosestItemIndex(
        _minuteController.selectedItem,
        widget.initialTime.minute,
        60,
      );
      _minuteController.animateToItem(
        targetMinuteIndex, 
        duration: const Duration(milliseconds: 200), 
        curve: Curves.easeOut,
      );
    }
  }

  // 计算离当前索引最近的、与目标值匹配的索引
  int _findClosestItemIndex(int currentItem, int targetValue, int modulo) {
    final int base = (currentItem / modulo).round() * modulo;

    // 检查三个可能的候选位置：当前循环、上一个循环、下一个循环
    final int option1 = base + targetValue;
    final int option2 = base - modulo + targetValue;
    final int option3 = base + modulo + targetValue;

    // 计算哪个选项离当前位置最近
    final int dist1 = (option1 - currentItem).abs();
    final int dist2 = (option2 - currentItem).abs();
    final int dist3 = (option3 - currentItem).abs();

    if (dist1 <= dist2 && dist1 <= dist3) {
      return option1;
    } else if (dist2 < dist1 && dist2 < dist3) {
      return option2;
    } else {
      return option3;
    }
  }

  @override
  void dispose() {
    _hourController.dispose();
    _minuteController.dispose();
    super.dispose();
  }

  // 当滚动结束时，统一更新时间
  void _onScrollEnd() {
    final newTime = TimeOfDay(
      hour: _hourController.selectedItem % 24,
      minute: _minuteController.selectedItem % 60,
    );

    // 只有当时间确实发生变化时才通知父组件，避免不必要的重建
    if (widget.initialTime.hour != newTime.hour || widget.initialTime.minute != newTime.minute) {
      widget.onTimeChanged(newTime);
    }
  }

  @override
  Widget build(BuildContext context) {
    const double itemHeight = 40.0;
    const double itemWidth = 80.0;

    return SizedBox(
      height: itemHeight * 3.5, // 垂直显示大约3-4个数字
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 时钟和分钟的滚动轮
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildWheel(
                controller: _hourController,
                itemCount: 24,
                width: itemWidth,
                itemHeight: itemHeight,
              ),
              // 用 Padding 向上微调冒号的位置，使其视觉上垂直居中
              Padding(
                padding: const EdgeInsets.only(bottom: 4.0),
                child: Text(
                  ':',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
              _buildWheel(
                controller: _minuteController,
                itemCount: 60,
                width: itemWidth,
                itemHeight: itemHeight,
              ),
            ],
          ),
          // 覆盖在中间的"高亮框"，包含两条横线
          _buildHighlightBox(itemHeight, itemWidth * 2 + 40),
        ],
      ),
    );
  }

  // 构建一个独立的"高亮框"小组件，它不会响应点击事件
  Widget _buildHighlightBox(double itemHeight, double width) {
    return IgnorePointer(
      child: Container(
        width: width,
        height: itemHeight,
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(color: Theme.of(context).colorScheme.outlineVariant, width: 1.5),
            bottom: BorderSide(color: Theme.of(context).colorScheme.outlineVariant, width: 1.5),
          ),
        ),
      ),
    );
  }

  // 构建单个滚轮，并应用渐变透明效果
  Widget _buildWheel({
    required FixedExtentScrollController controller,
    required int itemCount,
    required double width,
    required double itemHeight,
  }) {
    return SizedBox(
      width: width,
      height: itemHeight * 3.5,
      child: NotificationListener<ScrollNotification>(
        onNotification: (notification) {
          if (notification is ScrollEndNotification) {
            _onScrollEnd();
          }
          return true; // 继续传递通知
        },
        child: ShaderMask(
          shaderCallback: (Rect bounds) {
            // 创建一个线性渐变着色器
            return const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [Colors.transparent, Colors.white, Colors.white, Colors.transparent],
              stops: [0.0, 0.4, 0.6, 1.0], // 控制渐变的范围和强度
            ).createShader(bounds);
          },
          blendMode: BlendMode.dstIn, // 将渐变效果应用到子组件
          child: ListWheelScrollView.useDelegate(
            controller: controller,
            itemExtent: itemHeight,
            physics: const FixedExtentScrollPhysics(),
            // 每当中心数字变化时（"咔哒"一下），触发轻微震动
            onSelectedItemChanged: (index) => HapticFeedback.lightImpact(),
            childDelegate: ListWheelChildLoopingListDelegate(
              children: List<Widget>.generate(itemCount, (index) {
                return Center(
                  child: Text(
                    index.toString().padLeft(2, '0'),
                    style: TextStyle(
                      fontSize: 22,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                );
              }),
            ),
          ),
        ),
      ),
    );
  }
}
