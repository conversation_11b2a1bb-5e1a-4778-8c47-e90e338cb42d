// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';

/// 显示用于编辑计时器记录的日期选择 BottomSheet
Future<DateTime?> showTimerDateSheet({
  required BuildContext context,
  required DateTime initialDate,
  required DateTime firstDate,
  required DateTime lastDate,
}) {
  return showModalBottomSheet<DateTime>(
    context: context,
    builder: (context) => TimerDateSheet(
      initialDate: initialDate,
      firstDate: firstDate,
      lastDate: lastDate,
      onConfirm: (date) {
        Navigator.of(context).pop(date); // 确认时返回日期
      },
    ),
  );
}

// --- Date Picker BottomSheet 组件 ---
class TimerDateSheet extends StatelessWidget {
  final DateTime initialDate;
  final DateTime firstDate;
  final DateTime lastDate;
  final Function(DateTime) onConfirm;

  const TimerDateSheet({
    super.key,
    required this.initialDate,
    required this.firstDate,
    required this.lastDate,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
      fullWidth: true,
      child: CalendarDatePicker(
        initialDate: initialDate,
        firstDate: firstDate,
        lastDate: lastDate,
        onDateChanged: onConfirm,
      ),
    );
  }
}
