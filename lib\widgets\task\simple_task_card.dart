// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
// models
import '../../models/task_model.dart';

class SimpleTaskCard extends StatelessWidget {
  final TaskModel task;
  
  const SimpleTaskCard({
    super.key,
    required this.task,
  });
  
  @override
  Widget build(BuildContext context) {
    // 移除InkWell和点击编辑功能
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 4.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 任务名称
          Text(
            task.name,
            style: TextStyle(
              fontSize: 14,
              // 已完成任务显示为灰色
              color: task.isCompleted ? Theme.of(context).colorScheme.onSurfaceVariant : null,
              // 已完成任务添加删除线
              decoration: task.isCompleted ? TextDecoration.lineThrough : null,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          
          // 日期和时间
          if (!task.hasNoDue)
            Text(
              _formatDateTime(task),
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
        ],
      ),
    );
  }
  
  String _formatDateTime(TaskModel task) {
    final dateFormatter = DateFormat('MM-dd');
    
    if (task.dueTime != null) {
      // 有具体截止时间
      final timeFormatter = DateFormat('HH:mm');
      return '${dateFormatter.format(task.dueTime!)} ${timeFormatter.format(task.dueTime!)}';
    } else if (task.dueDate != null) {
      // 只有截止日期
      final date = DateTime.tryParse(task.dueDate.toString());
      if (date != null) {
        return dateFormatter.format(date);
      }
    }
    
    return '';
  }
}
