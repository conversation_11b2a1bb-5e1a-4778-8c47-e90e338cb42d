// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';
// models
import '../models/task_model.dart';
// states
import '../states/task_state.dart';
import '../states/setting_state.dart';
// tools
import '../tools/config.dart';
import '../tools/extensions.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/date_cell.dart';
// pages
import '../pages/task/day_tasks_page.dart';
// others
import '../generated/l10n/app_localizations.dart';

class MonthCalendarView extends StatefulWidget {
  const MonthCalendarView({super.key});

  @override
  State<MonthCalendarView> createState() => _MonthCalendarViewState();
}

class _MonthCalendarViewState extends State<MonthCalendarView> {
  // _focusedDay 控制 TableCalendar 当前显示的月份
  DateTime _focusedDay = DateTime.now();

  @override
  void initState() {
    super.initState();
  }

  // 导航到指定日期的任务列表页面 (DayTasksPage)
  void _navigateToDay(BuildContext context, DateTime day, TaskState taskState) {
    // 使用TaskState的get1DayTasks直接获取选中日期的任务
    final tasks = taskState.get1DayTasks(day);
    
    // 使用 Navigator push 启动新页面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DayTasksPage( // 确保 DayTasksPage 存在并接受这些参数
          selectedDate: day, // 传递选中的日期
          initialTasks: tasks, // 传递该日期的任务列表
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 在 build 方法开头获取 Provider 状态，避免在 builder 回调中重复获取
    final taskState = Provider.of<TaskState>(context);
    final settingsProvider = Provider.of<SettingState>(context);

    // 获取用户设置的一周起始日（周日或周一）
    final weekStartsOnSunday = settingsProvider.weekStartDay == 7;
    final startingDayOfWeek = weekStartsOnSunday ?
        StartingDayOfWeek.sunday : StartingDayOfWeek.monday;

    // --- 高度计算 ---

    final viewHeight = context.getViewHeight();

    // 默认星期标题行的高度
    final double daysOfWeekHeight = 16.0;
    // 日历的上下边距
    final double tablePadding = 10.0;
    // 期望当月份只有 5 行且基本填满屏幕时，日历底部距离屏幕底部的边距
    final double bottomPageMargin = 20.0;

    // 可用屏幕高度：计算基于 5 行填充可用空间的【固定】行高
    final heightForFiveRows = viewHeight - daysOfWeekHeight - tablePadding*2 - bottomPageMargin;
    final double fixedRowHeight = heightForFiveRows / 5;

    // --- 高度计算结束 ---

    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppHeaderBar(
        title: Text(l10n.monthCalendarView_title),
        actions: [
          // "今天"按钮，点击后聚焦到当前日期
          TextButton(
            onPressed: () {
              setState(() {
                _focusedDay = DateTime.now();
              });
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: Text(l10n.common_today),
          ),
        ],
      ),
      body: SingleChildScrollView( // 使用 SingleChildScrollView 包裹，确保内容过高（6行）时可以滚动
        child: Column(
          children: [
            // 包裹日历的容器
            TableCalendar<TaskModel>(
              // 不显示日历头部（就是 titleText、formatButton、leftChevron、rightChevron 那一行）
                headerVisible: false,
              // --- 核心属性 ---
              firstDay: Config.app.calendarFirstDay,
              lastDay: Config.app.calendarLastDay,
              focusedDay: _focusedDay, // 当前聚焦的日期，决定显示哪个页面（月份）
              calendarFormat: CalendarFormat.month, // 强制使用月视图格式
              availableCalendarFormats: {
                CalendarFormat.month: "Month", // 仅允许月视图格式
              },
              startingDayOfWeek: startingDayOfWeek,

              // --- 行高 ---
              rowHeight: fixedRowHeight, // 应用计算出的【固定】行高

              // --- 日历主体样式 (CalendarStyle) ---
              calendarStyle: CalendarStyle(
                tablePadding: EdgeInsets.symmetric(vertical: tablePadding),
                // 控制非当前月份的日期是否可见
                outsideDaysVisible: true,
                // 设置非当前月份日期的文本样式（例如，灰色）
                outsideTextStyle: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
              ),

              // --- 选择逻辑（现在由 DateCellWidget 处理点击） ---
              onDaySelected: (selectedDay, focusedDay) {
                  // 点击日期时触发导航
                  _navigateToDay(context, selectedDay, taskState);
                  // 确保组件还在树上
                  if (selectedDay.month != _focusedDay.month) {
                    setState(() {
                      // 更新 _focusedDay 到被点击日期所在的月份
                      _focusedDay = DateTime(selectedDay.year, selectedDay.month, 1);
                    });
                  }
              },

              // --- 自定义构建器 (CalendarBuilders) ---
              calendarBuilders: CalendarBuilders(
                // 构建当前月份内的日期单元格
                defaultBuilder: (context, day, focusedDay) {
                  // 获取该日期的任务列表
                  final tasks = taskState.get1DayTasks(day);
                  // 返回自定义的 DateCellWidget
                  return DateCell(
                    date: day,
                    isOutside: false, // 在当前月份内
                    isToday: day.isSameDay(DateTime.now()),
                    tasks: tasks, // 传递任务列表
                  );
                },
                // 构建今天的日期单元格
                todayBuilder: (context, day, focusedDay) {
                  final tasks = taskState.get1DayTasks(day);
                  return DateCell(
                    date: day,
                    isOutside: false, // 今天肯定在当前月内（逻辑上）
                    isToday: true, // 明确是今天
                    tasks: tasks,
                  );
                },
                // 构建非当前月份的日期单元格
                outsideBuilder: (context, day, focusedDay) {
                  final tasks = taskState.get1DayTasks(day);
                  return DateCell(
                    date: day,
                    isOutside: true, // 明确是在当前月份之外
                    isToday: day.isSameDay(DateTime.now()), // 非当前月日期也可能是今天（虽然少见）
                    tasks: tasks,
                  );
                },
              ),

              // --- 行为标志 ---
              // 是否强制显示 6 行，即使当前月份只需要 5 行。设为 false 让它自然显示 5 或 6 行。
              sixWeekMonthsEnforced: false,
              // 是否强制 TableCalendar 拉伸以填充其父容器的高度。设为 false 让它根据内容自适应高度。
              shouldFillViewport: false,

              // --- 页面（月份）切换回调 ---
              onPageChanged: (focusedDay) {
                // 当用户通过滑动或点击箭头切换月份时，更新 _focusedDay
                setState(() {
                  _focusedDay = focusedDay;
                });
              },
            ),

            // 底部留白，防止被 FAB 或遮挡
            SizedBox(height: Config.app.bottomSpace),
          ],
        ),
      ),
    );
  }
}
