// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/task_model.dart';
// tools
import '../../tools/config.dart';

/// TaskCheckbox 组件，用图标代替复选框表示多种状态
/// 未完成：LucideIcons.circle 可点击
/// 已逾期：LucideIcons.circle 红色可点击
/// 已完成：LucideIcons.circleCheck 可点击
/// 已丢弃：LucideIcons.circleSlash 灰色不可点击
class TaskCheckbox extends StatefulWidget {
  final TaskModel task;
  final bool hasRunningTimer;
  final ValueChanged<bool?>? onChanged;

  const TaskCheckbox({
    super.key,
    required this.task,
    required this.hasRunningTimer,
    required this.onChanged,
  });

  @override
  State<TaskCheckbox> createState() => _TaskCheckboxState();
}

class _TaskCheckboxState extends State<TaskCheckbox> with SingleTickerProviderStateMixin {
  late final AudioPlayer _audioPlayer;
  
  @override
  void initState() {
    super.initState();

    // *** 初始化 AudioPlayer ***
    _audioPlayer = AudioPlayer();
    // 可选：设置播放器模式为低延迟，适合音效
    _audioPlayer.setPlayerMode(PlayerMode.lowLatency);
    // 可选：设置释放模式，播放完后释放资源（对短音效影响不大，但好习惯）
    _audioPlayer.setReleaseMode(ReleaseMode.release);
  }
  
  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 定义图标尺寸
    const double iconSize = 28.0;

    // 获取任务状态
    final bool isOverdue = widget.task.isOverdue;
    final bool isDropped = widget.task.isDropped;
    final bool isCompleted = widget.task.isCompleted;
    // 获取主题色
    final Color primaryColor = Theme.of(context).colorScheme.primary;
    final Color onSurfaceVariant = Theme.of(context).colorScheme.onSurfaceVariant;
    final Color errorColor = Theme.of(context).colorScheme.error;

    // 根据任务状态选择合适的图标和颜色
    IconData iconData;
    Color iconColor;
    if (isDropped) {
      // 已丢弃的任务
      iconData = LucideIcons.circleSlash;
      iconColor = onSurfaceVariant;
    } else if (isCompleted) {
      // 已完成的任务
      iconData = LucideIcons.circleCheck;
      iconColor = primaryColor;
    } else {
      // 未完成的任务
      iconData = LucideIcons.circle;
      iconColor = isOverdue ? errorColor : primaryColor;
    }
    if (widget.hasRunningTimer) {
      // 如果有正在运行的计时器，禁用点击（最高优先级）
      iconData = LucideIcons.circlePlay;
      iconColor = onSurfaceVariant;
    }

    return Stack(
      alignment: Alignment.center, // 确保 ConfettiWidget 的中心和 IconButton 对齐
      children: [
        SizedBox(
          width: iconSize + 15, // 考虑 IconButton 的默认 padding 或额外触摸区域
          height: iconSize + 10,
          child: IconButton(
            icon: Icon(
              iconData,
              color: iconColor,
              size: iconSize,
            ),
            padding: const EdgeInsets.only(left: 5.0, right: 10.0),
            constraints: const BoxConstraints(), // 使用外部 SizedBox 控制大小
            splashRadius: iconSize * 0.8,
            alignment: Alignment.center, // 确保 Icon 在 IconButton 内居中
            onPressed: isDropped
              ? null
              : () async {
                  // 增加震动反馈
                  HapticFeedback.lightImpact();

                  bool wasCompleted = isCompleted;
                  if (!wasCompleted) {
                    _playCompleteTaskSound();
                  }
                  widget.onChanged?.call(!wasCompleted);
                },
          ),
        ),
      ],
    );
  }

  // *** 播放音效的辅助方法 ***
  Future<void> _playCompleteTaskSound() async {
    try {
      await _audioPlayer.play(AssetSource(Config.asset.gameBonus));
    } catch (e) {
      // 静默处理
    }
  }
}
