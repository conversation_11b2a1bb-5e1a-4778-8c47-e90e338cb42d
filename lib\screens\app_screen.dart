// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// states
import '../states/setting_state.dart';
import '../states/theme_state.dart';
// tools
import '../tools/custom_scroll_behavior.dart';
import '../models/theme_model.dart';
// screens
import './init_screen.dart';
// others
import '../generated/l10n/app_localizations.dart';

class AppScreen extends StatelessWidget {
  const AppScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // 监听 SettingState (用于语言区域) 和 ThemeState (用于主题) 的变化
    final settingState = context.watch<SettingState>();
    final themeState = context.watch<ThemeState>();
    final selectedThemeData = AppThemes.getTheme(themeState.currentTheme);

    return MaterialApp(
      title: 'Taskive',

      home: const InitScreen(),

      // 开发模式不显示 debug 横幅
      debugShowCheckedModeBanner: false,

      // 应用自定义滚动行为，隐藏所有滚动条
      scrollBehavior: CustomScrollBehavior(),

      // --- 本地化配置 ---
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
      locale: settingState.locale,

      // --- 主题配置 ---
      theme: selectedThemeData.light,
      darkTheme: selectedThemeData.dark,
      themeMode: ThemeMode.system,
    );
  }
}
