import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// 应用操作异常的动作
enum AppExceptionHow {
  // 通用
  initializeApp,
  // 账户
  signIn,
  signOut,
  updateUsername,
  deleteAccount,
  // 任务
  loadTask,
  createTask,
  updateTask,
  deleteTask,
  // 计时
  loadTimer,
  createTimer,
  updateTimer,
  deleteTimer,
  // 项目
  loadProject,
  createProject,
  updateProject,
  deleteProject,
  // 周期任务
  loadRecurring,
  createRecurring,
  updateRecurring,
  deleteRecurring,
  // 设置
  loadSetting,
  updateSetting,
  // 未知
  unknown,
}

/// 应用操作异常的原因
enum AppExceptionWhy {
  // 通用
  unauthenticated, // 用户未登录
  permissionDenied, // 权限不足
  serverError, // 服务器错误
  operationFailed, // 后端通用操作失败
  notFound, // 未找到目标资源
  alreadyExists, // 资源已存在
  invalidArgument, // 无效参数
  // 登录
  credentialAlreadyInUse, // 凭证已被使用 (通常用于链接账户)
  emailAlreadyInUse, // 邮箱已被使用 (通常用于链接账户)
  accountExistsWithDifferentCredential, // 用户已存在，但登录凭证不同
  userDisabled, // 用户账户已被禁用
  operationNotAllowed, // 操作不被允许 (例如，Email/Password 登录未开启)
  networkRequestFailed, // 网络请求失败
  // 订阅
  featureLimited, // 非订阅用户功能使用已达上限
  // 未知
  unknown, // 未知错误
}

/// 自定义的、用于包装错误的异常类
class AppException implements Exception {
  final AppExceptionHow how;
  final AppExceptionWhy why;
  final String? message; // 给开发者看的原始消息
  final StackTrace? stack; // 错误堆栈

  AppException({required this.how, required this.why, this.message, this.stack}) {
    if (kDebugMode) {
      developer.log(
        '⛔ AppException Log ▶ how: ${how.name}, why: ${why.name}, message: $message, stack: $stack',
        name: 'AppException',
      );
    }
  }

  @override
  String toString() => 'AppException(how: ${how.name}, why: ${why.name}, message: $message, stack: $stack)';
}
