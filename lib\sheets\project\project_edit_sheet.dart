// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/project_model.dart';
// states
import '../../states/project_state.dart';
// tools
import '../../tools/validators.dart';
import '../../tools/explicit_value.dart';
import '../../tools/app_exception.dart';
import '../../tools/app_exception_mapper.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
import '../../widgets/common/app_exception_snackbar.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class ProjectEditSheet extends StatefulWidget {
  final ProjectModel? project; // 如果是编辑则传入现有项目

  const ProjectEditSheet({
    super.key,
    this.project,
  });

  static Future<ProjectModel?> show(BuildContext context, {ProjectModel? project}) {
    return showModalBottomSheet<ProjectModel>(
      context: context,
      // 使弹窗能响应键盘，自动调整大小
      isScrollControlled: true,
      builder: (context) {
        // 在底部弹窗中构建 ProjectEditPage
        return ProjectEditSheet(project: project);
      },
    );
  }

  @override
  State<ProjectEditSheet> createState() => _ProjectEditPageState();
}

class _ProjectEditPageState extends State<ProjectEditSheet> {
  late TextEditingController _nameController;
  late Color _selectedColor;
  final _formKey = GlobalKey<FormState>();
  
  bool get _isEditMode => widget.project != null;

  // 预定义的颜色选项
  final List<Color> _colorOptions = [
    Colors.red,
    Colors.pink,
    Colors.purple,
    Colors.deepPurple,
    Colors.indigo,
    Colors.blue,
    Colors.lightBlue,
    Colors.cyan,
    Colors.teal,
    Colors.green,
    Colors.lightGreen,
    Colors.lime,
    Colors.yellow,
    Colors.amber,
    Colors.orange,
    Colors.deepOrange,
    Colors.brown,
    Colors.grey,
    Colors.blueGrey,
  ];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.project?.name ?? '');
    _selectedColor = widget.project?.color ?? Colors.blue;
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BaseBottomSheet(
      title: _isEditMode ? l10n.projectEditSheet_titleEdit : l10n.projectEditSheet_titleCreate,
      onConfirm: _saveProject,
      child: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 项目名称输入框 - 无边框设计
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  hintText: l10n.projectEditSheet_nameHint,
                  hintStyle: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.6)),
                  border: InputBorder.none, // 移除边框
                ),
                style: const TextStyle(fontSize: 16), // 输入文本样式
                maxLines: 1,
                validator: (value) => Validators.validateProjectName(value, l10n),
                autovalidateMode: AutovalidateMode.onUserInteraction,
              ),

              const SizedBox(height: 16),

              _buildColorPicker(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildColorPicker() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _colorOptions.map((color) {
        final isSelected = _areColorsEqual(color, _selectedColor);

        return GestureDetector(
          onTap: () {
            setState(() => _selectedColor = color);
          },
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            // 添加选中标记
            child: isSelected ? Icon(LucideIcons.check, color: Theme.of(context).colorScheme.onPrimary, size: 20) : null,
          ),
        );
      }).toList(),
    );
  }

  // 比较两个颜色是否相等 - 使用 toARGB32()
  bool _areColorsEqual(Color color1, Color color2) {
    return color1.toARGB32() == color2.toARGB32();
  }

  void _saveProject() async {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    if (!(_formKey.currentState?.validate() ?? false)) return;

    final projectState = Provider.of<ProjectState>(context, listen: false);

    ProjectModel? resultProject;
    
    try {
      if (_isEditMode) {
        // 更新项目
        resultProject = widget.project!.copyWith(
          name: ExplicitValue(_nameController.text.trim()),
          color: ExplicitValue(_selectedColor),
        );

        // 仅在数据有变化时才执行更新
        if (resultProject != widget.project) {
          await projectState.updateProject(resultProject);
        }
      } else {
        // 创建新项目
        final project = ProjectModel(
          id: '',
          userId: '',
          createTime: DateTime.now(),
          updateTime: DateTime.now(),
          name: _nameController.text.trim(),
          color: _selectedColor,
          isArchived: false,
        );

        resultProject = await projectState.createProject(project);
      }
      
      // 返回创建或更新后的项目，以便在任务编辑页面使用
      if (mounted) Navigator.pop(context, resultProject);

    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: _isEditMode ? AppExceptionHow.updateProject : AppExceptionHow.createProject,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    }
  }
}
