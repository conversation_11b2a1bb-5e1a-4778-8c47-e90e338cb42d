// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// screens
import './main_screen.dart';

// HomeScreen 主要负责 Navigator
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // PopScope 拦截安卓物理返回键或 iOS 侧滑返回手势
    return PopScope(
      // canPop 设置为 false，表示我们要手动接管返回事件
      canPop: false,
      // 当返回事件被触发时 (无论是被允许还是被阻止)
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        // 如果 didPop 为 true，说明 Pop 已经被执行了，我们无需任何操作
        if (didPop) return;
        // 检查 _navigatorKey 关联的嵌套 Navigator 是否可以 pop
        final NavigatorState? navigator = _navigatorKey.currentState;
        if (navigator != null && navigator.canPop()) {
          // 如果可以，则弹出嵌套 Navigator 的页面
          navigator.pop();
        } else {
          // 如果不可以，说明嵌套 Navigator 已经处于顶层
          // 此时我们手动弹出根 Navigator 的页面，以实现退出应用的效果
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        body: Navigator(
          key: _navigatorKey,
          onGenerateRoute: (settings) {
            return MaterialPageRoute(
              settings: settings,
              builder: (context) {
                return const MainScreen();
              },
            );
          },
        ),
      ),
    );
  }
}
