// models
import '../models/user_model.dart';
// tools
import '../tools/app_exception.dart';

/// 功能特性枚举
/// 用于标识需要进行权限检查的功能点
enum Feature {
  timer,
  project,
  recurring,
}

////////////////////////////////////////////////
/// 权限服务
///
/// 集中处理应用中的所有权限相关逻辑。
/// 依赖于其他服务来获取数据（例如，项目数量）和用户信息。
///
/// 使用方法:
/// 1. UI层 (前置检查): 调用 `canPerformAction()` 来确定是否启用某个操作按钮。
/// 2. 状态管理层 (执行操作): 调用 `executeWithPermission()` 来执行创建等操作，
///    该方法会先进行权限检查，如果通过则执行操作，否则抛出 [AppException]。
////////////////////////////////////////////////
class PermissionService {
  // 静态方法，获取不同功能的限制数量
  static ({int free, int premium}) getFeatureLimits(Feature feature) {
    switch (feature) {
      case Feature.timer:
        return (free: 100, premium: -1); // -1 表示无限
      case Feature.project:
        return (free: 2, premium: 1000);
      case Feature.recurring:
        return (free: 2, premium: 1000);
      default:
        return (free: 0, premium: 0);
    }
  }

  /// 检查用户是否可以执行某个操作 (用于UI层的前置判断)
  ///
  /// [feature]: 要检查的功能点
  /// [currentUsage]: 用户当前对该功能的使用量 (例如，已创建的项目数)
  /// [currentUser]: 当前登录的用户模型
  bool canPerformAction(Feature feature, int currentUsage, UserModel currentUser) {
    final limits = getFeatureLimits(feature);
    final isPremium = currentUser.subscription.isPremium;
    final limit = isPremium ? limits.premium : limits.free;

    if (limit == -1) { // -1 代表无限
      return true;
    }
    return currentUsage < limit;
  }

  /// 在执行操作前检查权限
  ///
  /// [feature]: 要检查的功能点
  /// [currentUsage]: 用户当前对该功能的使用量
  /// [currentUser]: 当前登录的用户模型
  /// [action]: 如果权限检查通过，则执行此异步回调函数
  ///
  /// 如果权限不足，会抛出 [AppException]
  Future<T> executeWithPermission<T>({
    required Feature feature,
    required int currentUsage,
    required UserModel currentUser,
    required Future<T> Function() action,
  }) async {
    final canPerform = canPerformAction(feature, currentUsage, currentUser);
    if (canPerform) {
      return await action();
    } else {
      final how = _mapFeatureToHow(feature);
      final why = AppExceptionWhy.featureLimited;
      final message = 'Feature(${feature}) limit reached';
      throw AppException(how: how, why: why, message: message);
    }
  }

  AppExceptionHow _mapFeatureToHow(feature) {
    switch (feature) {
      case Feature.timer:
        return AppExceptionHow.createTimer;
      case Feature.project:
        return AppExceptionHow.createProject;
      case Feature.recurring:
        return AppExceptionHow.createRecurring;
      default:
        return AppExceptionHow.unknown;
    }
  }
}
