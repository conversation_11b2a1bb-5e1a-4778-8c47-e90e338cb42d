// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// states
import '../../states/user_state.dart';
// tools
import '../../tools/app_exception.dart';
import '../../tools/app_exception_mapper.dart';
// widgets
import '../../widgets/common/app_exception_snackbar.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class SignInPage extends StatefulWidget {
  const SignInPage({super.key});

  @override
  State<SignInPage> createState() => _SignInPageState();
}

class _SignInPageState extends State<SignInPage> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  final _emailController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  // 处理邮箱链接登录
  Future<void> _signInWithEmailLink() async {
    if (!_formKey.currentState!.validate()) return;

    _formKey.currentState!.save();

    setState(() => _isLoading = true);

    final email = _emailController.text.trim();
    final userState = Provider.of<UserState>(context, listen: false);
    final l10n = AppLocalizations.of(context)!;

    try {
      await userState.signInWithEmailLink(email);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(l10n.signInPage_linkSent)),
        );
      }
    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.signIn,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  // 处理 Google 授权登录
  Future<void> _signInWithGoogle() async {
    if (_isLoading) return;

    setState(() => _isLoading = true);

    final userState = Provider.of<UserState>(context, listen: false);
    final navigator = Navigator.of(context);

    await userState.signInWithGoogle();
    
    // 如果所有操作都成功，关闭页面
    if (mounted) {
      navigator.pop();
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.signInPage_title),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 顶部图标
              const Icon(LucideIcons.fingerprint, size: 80),

              const SizedBox(height: 24),

              // 邮箱输入框
              TextFormField(
                controller: _emailController,
                decoration: InputDecoration(
                  isDense: true,
                  contentPadding: EdgeInsets.symmetric(vertical: 14.0, horizontal: 12.0),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty || !value.contains('@')) {
                    return l10n.signInPage_invalidEmail;
                  }
                  return null;
                },
              ),

              const SizedBox(height: 24),

              // 提交按钮
              if (_isLoading)
                const Center(child: CircularProgressIndicator())
              else
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size(80, 44),
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                  ),
                  onPressed: _signInWithEmailLink,
                  child: Text(l10n.signInPage_sendLinkButton),
                ),
              
              const SizedBox(height: 24),

              // 分割线
              Row(
                children: [
                  Expanded(child: Divider()),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 8.0),
                    child: Text(l10n.signInPage_orDivider),
                  ),
                  Expanded(child: Divider()),
                ],
              ),

              const SizedBox(height: 24),

              // Google 授权登录按钮
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  minimumSize: const Size(80, 44),
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                ),
                onPressed: _signInWithGoogle,
                child: Text(l10n.signInPage_googleButton),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
