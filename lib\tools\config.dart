// import 'dart:developer' as developer;

/// 应用全局配置
class Config {
  // 私有构造函数，防止实例化
  Config._();

  // 使用嵌套类组织相关常量
  static final AppConstants app = AppConstants();
  static const ServiceConstants service = ServiceConstants();
  static const AssetConstants asset = AssetConstants();
}

/// 应用常量
class AppConstants {
  AppConstants();

  /// ====== App ======

  // 应用名称
  final String appName = 'Taskive';
  // 应用包名
  final String packageName = 'com.taskivestudio.taskive';

  // 用户自定义最大导航数
  // 加上默认的 entry 导航，导航数量总区间: [0, maxNavCount + 1]
  final int maxUserNavCount = 7;
  // 导航总数 (包括 entry 导航) 大于等于这个数时，要隐藏标签
  final int hideLabelNavCount = 6;

  // 应用栏的高度
  final double appBarHeight = 56.0;
  final double navBarHeight = 64.0;
  final double bottomSpace = 100.0;

  /// ====== Task ======

  // 任务名称最大行数
  final int taskNameMaxLines = 5;

  /// ====== Validation ======
  
  // 用户名
  final int usernameMaxLength = 20;
  // 任务名
  final int taskNameMaxLength = 200;
  // 项目名
  final int projectNameMaxLength = 30;

  /// ====== Time ======
  
  // 默认时区
  final String defaultTimezone = 'Etc/UTC';

  // 日历的日期范围
  final DateTime calendarFirstDay = DateTime.utc(2020, 1, 1);
  final DateTime calendarLastDay = DateTime.utc(2030, 12, 31);
}

/// 服务常量
class ServiceConstants {
  const ServiceConstants();

  /// ====== Firebase ======
  
  // 区域
  final String firebaseRegion = 'us-central1';

  /// ====== Cloud Function ======

  // 用户相关
  final String recordSignInFn = 'recordSignIn';
  final String upgradeAccountFn = 'upgradeAccount';
  final String updateUsernameFn = 'updateUsername';
  final String deleteUserFn = 'deleteUser';

  // 任务相关
  final String deleteTaskFn = 'deleteTask';

  // 计时相关
  final String deleteTimerFn = 'deleteTimer';

  // 项目相关
  final String deleteProjectOnlyFn = 'deleteProjectOnly';
  final String deleteProjectWithTasksFn = 'deleteProjectWithTasks';

  // 周期任务相关
  final String deleteRecurringFn = 'deleteRecurring';

  /// ====== Firestore ======

  // 集合名称
  final String usersCollection = 'users';
  final String tasksCollection = 'tasks';
  final String timersCollection = 'timers';
  final String projectsCollection = 'projects';
  final String recurringsCollection = 'recurrings';
  final String settingsCollection = 'settings';

  // 设置子集合下只需要一个文档，所以设计一个随机的文档 id
  final String settingsDocId = 'XaB3kPq9m2N5vL6Y7wR4';

  // 设置项的 key
  final String navVerKey = 'navVer';
  final String statVerKey = 'statVer';
  final String navHorKey = 'navHor';
  final String statHorKey = 'statHor';
  final String weekStartKey = 'weekStartDay';
  final String showCompletedTasksKey = 'showCompletedTasks';
  final String showDroppedTasksKey = 'showDroppedTasks';
  final String showOverdueTasksKey = 'showOverdueTasks';

  /// ====== Local Storage ======
  
  // 用户 ID (用于获取 firstore 缓存)
  final String appUserIdKey = 'app_user_id';
  // 用户邮箱 (用于邮箱链接登录)
  final String appUserEmailKey = 'app_user_email';
  // 应用主题
  final String appThemeCodeKey = 'app_theme_code';
  // 应用语言
  final String appLanguageCodeKey = 'app_language_code';

  /// ====== Hosting ======

  // 域名 (用于邮箱链接登录)
  final String domain = 'https://taskive-4eec0.firebaseapp.com';
  // 完成登录路径 (用于邮箱链接登录)
  final String finishSignInPath = '/finishSignIn';
}

/// 资源常量
class AssetConstants {
  const AssetConstants();

  // 音效文件路径
  final String sounds = 'assets/sounds/';

  // 任务完成音效
  final String gameBonus = 'sounds/game_bonus.mp3';
}
