// import 'dart:developer' as developer;
import 'package:flutter/material.dart';

/// 自定义滚动行为，去除所有滚动指示器（滚动条）
class CustomScrollBehavior extends ScrollBehavior {
  /// 不显示滚动条但保留滚动物理效果
  @override
  Widget buildScrollbar(BuildContext context, Widget child, ScrollableDetails details) {
    // 返回原始子组件，不添加滚动条
    return child;
  }

  /// 保留原始的滚动物理效果
  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    // 将 ClampingScrollPhysics 设置为 AlwaysScrollableScrollPhysics 的 parent，组合这两种物理效果的行为
    // AlwaysScrollableScrollPhysics 确保了视图始终可以启动滚动
    // ClampingScrollPhysics 会应用钳制效果，阻止视图无限拖动，并且在您松手后，确保视图回到正常的边界位置
    return const ClampingScrollPhysics(parent: AlwaysScrollableScrollPhysics());
  }
}
