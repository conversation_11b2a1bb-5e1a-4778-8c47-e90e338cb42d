// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
// models
import '../../models/recurring_model.dart';
// states
import '../../states/recurring_state.dart';
// tools
import '../../tools/config.dart';
import '../../tools/validators.dart';
import '../../tools/explicit_value.dart';
import '../../tools/app_exception.dart';
import '../../tools/app_exception_mapper.dart';
// widgets
import '../../widgets/task/task_priority.dart';
import '../../widgets/common/app_exception_snackbar.dart';
// sheets
import './recurring_rules_picker.dart';
import '../task/task_project_picker.dart';
import '../task/task_clock_picker.dart';
// others
import '../../generated/l10n/app_localizations.dart';

/// 命名为 sheet，实际上是一个 page
class RecurringEditPage extends StatefulWidget {
  final RecurringModel? recurring; // 如果是编辑现有周期任务则传入

  const RecurringEditPage({
    super.key,
    this.recurring,
  });

  @override
  State<RecurringEditPage> createState() => _RecurringEditPageState();
}

class _RecurringEditPageState extends State<RecurringEditPage> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late FocusNode _nameFocusNode;

  TimeOfDay? _dueClock;
  String? _projectId;
  bool _isImportant = false;
  bool _isUrgent = false;
  RecurringRule? _recurringRule;
  bool _isNameEmpty = true;

  bool get _isEditMode => widget.recurring != null;

  @override
  void initState() {
    super.initState();
    // 初始化控制器
    _nameController = TextEditingController();
    _nameFocusNode = FocusNode();

    // 设置初始值
    if (_isEditMode) {
      // --- 编辑现有周期任务 RecurringModel ---
      final recurring = widget.recurring!;
      final template = recurring.template;
      _nameController.text = template.name;
      _projectId = template.projectId;
      _isImportant = template.isImportant;
      _isUrgent = template.isUrgent;

      _recurringRule = recurring.rule; // 加载周期规则

      // 从模板加载时间设置
      if (template.dueClock != null) {
        _dueClock = TimeOfDay(
          hour: template.dueClock!.hour,
          minute: template.dueClock!.minute,
        );
      }
    } else {
      // --- 新建周期任务 ---
      // 控制器已初始化为空
      // 需要初始化 recurringRule 的 startTime
      _recurringRule = RecurringRule(
        frequency: RecurringFrequency.daily, // 默认每天
        startTime: DateTime.now(), // 默认从今天开始
      );
    }

    // 初始化 _isNameEmpty
    _isNameEmpty = _nameController.text.trim().isEmpty;

    // 添加文本变化监听
    _nameController.addListener(_updateNameState);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;
    const double rowSpacing = 32.0; // 行间距
    const double sideSpacing = 16.0; // 左右边距

    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditMode ? l10n.recurringEditPage_titleEdit : l10n.recurringEditPage_titleCreate),
        actions: [
          IconButton(
            icon: const Icon(LucideIcons.check),
            onPressed: _saveRecurring,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [

              // ========== 提示横幅 ==========

              Container(
                width: double.infinity,
                color: colorScheme.tertiaryContainer,
                padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: sideSpacing),
                margin: const EdgeInsets.only(bottom: 8.0),
                child: Text(
                  l10n.recurringEditPage_creationHint,
                  style: TextStyle(color: colorScheme.onTertiaryContainer),
                ),
              ),

              // ========== 第一分区 ==========

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: sideSpacing),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 任务名称输入框
                    TextFormField(
                      controller: _nameController,
                      focusNode: _nameFocusNode,
                      decoration: InputDecoration(
                        hintText: l10n.recurringEditPage_nameHint,
                        hintStyle: TextStyle(color: colorScheme.onSurfaceVariant.withValues(alpha: 0.6)),
                        border: InputBorder.none,
                      ),
                      style: const TextStyle(fontSize: 18),
                      minLines: 1,
                      maxLines: Config.app.taskNameMaxLines,
                      keyboardType: TextInputType.multiline,
                      textInputAction: TextInputAction.newline,
                      validator: (value) => Validators.validateTaskName(value, l10n),
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                    ),

                    const SizedBox(height: rowSpacing / 2),

                    // 周期规则选择器
                    RecurringRulesPicker(
                      selectedRule: _recurringRule,
                      onSelected: (rule) {
                        setState(() {
                          _recurringRule = rule;
                        });
                        // 延迟执行 unfocus
                        Future.delayed(const Duration(milliseconds: 10), () {
                          if (mounted) _nameFocusNode.unfocus();
                        });
                      },
                    ),

                    const SizedBox(height: rowSpacing),

                    // 项目选择器
                    TaskProjectPicker(
                      selectedProjectId: _projectId,
                      onSelected: (projectId) {
                        setState(() {
                          _projectId = projectId;
                        });
                        // 延迟执行 unfocus
                        Future.delayed(const Duration(milliseconds: 10), () {
                          if (mounted) _nameFocusNode.unfocus();
                        });
                      },
                    ),

                    const SizedBox(height: rowSpacing),

                    // 时间选择器组件 - 周期任务不需要设置日期，只需设置时间
                    TaskClockPicker(
                      selectedTime: _dueClock,
                      dueDate: null, // 不需要日期
                      onSelected: (time) {
                        setState(() {
                          _dueClock = time;
                        });
                        // 延迟执行 unfocus
                        Future.delayed(const Duration(milliseconds: 10), () {
                          if (mounted) _nameFocusNode.unfocus();
                        });
                      },
                      onSetDueDateToToday: () {}, // 提供空回调
                    ),

                    const SizedBox(height: rowSpacing),
                  ],
                ),
              ),

              // 分隔线
              Divider(color: colorScheme.outline.withValues(alpha: 0.12), height: 1, thickness: 1),

              // ========== 第二分区 ==========

              Padding(
                padding: const EdgeInsets.symmetric(horizontal: sideSpacing),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: rowSpacing - 8.0),

                    // 任务优先级选择
                    TaskPriority(
                      isUrgent: _isUrgent,
                      isImportant: _isImportant,
                      onSelected: (important, urgent) {
                        setState(() {
                          _isImportant = important;
                          _isUrgent = urgent;
                        });
                      },
                    ),

                    SizedBox(height: Config.app.bottomSpace),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // _isNameEmpty 的作用是：
  // 只有当任务名称从"空"变为"非空"或从"非空"变为"空"时，才触发组件重建
  void _updateNameState() {
    final isEmpty = _nameController.text.trim().isEmpty;
    if (isEmpty != _isNameEmpty) {
      setState(() {
        _isNameEmpty = isEmpty;
      });
    }
  }

  // 保存周期任务 (创建或更新)
  void _saveRecurring() async {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final recurringState = Provider.of<RecurringState>(context, listen: false);
    final l10n = AppLocalizations.of(context)!;
    final navigator = Navigator.of(context);

    // 触发校验
    if (!(_formKey.currentState?.validate() ?? false)) return;

    try {
      final name = _nameController.text.trim();
      
      // 确保有重复规则被选中 (新建时已默认，编辑时从 initState 加载)
      if (_recurringRule == null) {
         if (mounted) {
            // 这里我们用一个临时的 SnackBar，因为这不是一个典型的“请求失败”
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(l10n.recurringEditPage_errorRuleEmpty)),
            );
         }
         return;
      }

      // --- 准备：模板 template ---

      // 如果设置了时间，需要将 TimeOfDay 转换为 DateTime
      // 由于周期任务模板的时间不关心日期，所以这里用一个固定的虚拟日期（例如1970-1-1）
      final DateTime? dueClock = _dueClock != null
        ? DateTime(1970, 1, 1, _dueClock!.hour, _dueClock!.minute)
        : null;

      final template = RecurringTemplate(
        name: name,
        projectId: _projectId,
        dueClock: dueClock,
        isImportant: _isImportant,
        isUrgent: _isUrgent,
      );

      // --- 准备：规则 rule ---

      // --- 时区逻辑 ---
      String? timezone;
      if (_isEditMode) {
        final originalRecurring = widget.recurring!;
        // 编辑模式：仅当时间或规则改变时才更新时区
        final bool timeChanged = dueClock != originalRecurring.template.dueClock;
        final bool ruleChanged = _recurringRule != originalRecurring.rule;
        if (timeChanged || ruleChanged) {
          timezone = await FlutterTimezone.getLocalTimezone();
        } else {
          timezone = originalRecurring.timezone; // 沿用旧的时区
        }
      } else {
        // 新建模式：总是获取当前时区
        timezone = await FlutterTimezone.getLocalTimezone();
      }


      // --- 判断是创建还是更新 ---
      if (_isEditMode) {
        // --- 更新现有周期任务 ---
        final updatedRecurring = widget.recurring!.copyWith(
          template: ExplicitValue(template),
          rule: ExplicitValue(_recurringRule!),
          timezone: ExplicitValue(timezone),
        );

        // 只有当数据真的发生变化时才执行更新
        if (updatedRecurring != widget.recurring) {
          await recurringState.updateRecurring(updatedRecurring);
        }
      } else {
        // --- 创建新周期任务 ---
        final newRecurring = RecurringModel(
          id: '', // ID 由后端生成
          userId: '', // UserID 由 Service 添加
          createTime: DateTime.now(),
          updateTime: DateTime.now(),
          template: template,
          rule: _recurringRule!,
          timezone: timezone,
        );

        await recurringState.createRecurring(newRecurring);
      }

      // 关闭底部弹窗
      if (mounted) navigator.pop();

    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: _isEditMode ? AppExceptionHow.updateRecurring : AppExceptionHow.createRecurring,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    }
  }

  @override
  void dispose() {
    _nameController.removeListener(_updateNameState);
    _nameController.dispose();
    super.dispose();
  }
}
