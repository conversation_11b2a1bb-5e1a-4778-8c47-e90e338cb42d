// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// states
import '../states/task_state.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/today_glance.dart';
import '../widgets/task/date_grouped_tasks.dart';
// others
import '../generated/l10n/app_localizations.dart';

class Next7DaysView extends StatelessWidget {
  const Next7DaysView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer<TaskState>(
      builder: (context, taskState, _) {
        // 获取7天任务
        final next7daysTasks = taskState.getNextDaysTasks(7);
        
        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(l10n.next7DaysView_title),
          ),
          body: CustomScrollView(
            slivers: [
              // 今日一览作为头部
              SliverToBoxAdapter(child: TodayGlance()),
              
              // 任务列表
              if (next7daysTasks.isEmpty)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(child: Text(l10n.next7DaysView_emptyMessage)),
                )
              else
                DateGroupedTasks(tasks: next7daysTasks),
            ],
          ),
        );
      },
    );
  }
}
