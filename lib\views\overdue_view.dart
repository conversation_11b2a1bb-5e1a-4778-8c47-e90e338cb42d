// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// states
import '../states/task_state.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/date_grouped_tasks.dart';
// others
import '../generated/l10n/app_localizations.dart';

class OverdueView extends StatelessWidget {
  const OverdueView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer<TaskState>(
      builder: (context, taskState, _) {
        // 获取所有逾期任务 (未结束已逾期)
        final overdueTasks = taskState.getOverdueTasks();
        
        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(l10n.overdueView_title),
          ),
          body: CustomScrollView(
            slivers: [
              if (overdueTasks.isEmpty)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(child: Text(l10n.overdueView_emptyMessage)),
                )
              else
                DateGroupedTasks(tasks: overdueTasks),
            ],
          ),
        );
      },
    );
  }
}
