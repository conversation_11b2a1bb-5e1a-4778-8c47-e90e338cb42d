// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
// states
import '../../states/timer_state.dart';
import '../../states/setting_state.dart';
// tools
import '../../tools/extensions.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class WeeklyTimerChart extends StatelessWidget {
  const WeeklyTimerChart({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;

    return Consumer2<TimerNotifier, SettingState>(
      builder: (context, timerNotifier, settingState, _) {
        final timers = timerNotifier.state.timers;
        final weekStartDay = settingState.weekStartDay; // 1=Mon, 7=Sun
        final now = DateTime.now();

        final weekDates = now.getDatesOfThisWeek(weekStartDay);
        final firstDayOfWeek = weekDates.first;
        final lastDayOfWeek = weekDates.last;

        final dailyMinutes = <int, double>{}; // key: weekday (1-7), value: total minutes
        for (int i = 0; i < 7; i++) {
          dailyMinutes[i + 1] = 0.0;
        }

        for (final timer in timers) {
          if (timer.endTime.isAfter(firstDayOfWeek.subtract(const Duration(microseconds: 1))) &&
              timer.startTime.isBefore(lastDayOfWeek.add(const Duration(days: 1)))) {
             if (timer.endTime.weekday >= DateTime.monday && timer.endTime.weekday <= DateTime.sunday) {
                final dayOfWeek = timer.endTime.weekday; // 1=Mon, 7=Sun
                dailyMinutes[dayOfWeek] = (dailyMinutes[dayOfWeek] ?? 0.0) + (timer.durationSeconds / 60.0);
             }
          }
        }

        final spots = <FlSpot>[];
        double maxY = 5; 
        
        final daySymbols = DateFormat.E(l10n.localeName).dateSymbols.STANDALONESHORTWEEKDAYS;
        final List<String> bottomTitles = [];

        int currentWeekday = weekStartDay;
        for (int i = 0; i < 7; i++) {
           final minutes = dailyMinutes[currentWeekday] ?? 0.0;
           spots.add(FlSpot(i.toDouble(), minutes));
           bottomTitles.add(daySymbols[currentWeekday % 7]);
           if (minutes > maxY) {
               maxY = minutes;
           }
           currentWeekday = (currentWeekday % 7) + 1;
        }
        maxY = (maxY * 1.2).ceilToDouble();

        final LineChartData lineChartData = LineChartData(
          gridData: const FlGridData(show: false),
          titlesData: FlTitlesData(
            show: true,
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 28, 
                interval: (maxY / 4).ceilToDouble(),
                getTitlesWidget: (value, meta) {
                  if (value == 0 || value == meta.max) return Container();
                  return Padding(
                    padding: const EdgeInsets.only(right: 4.0),
                    child: Text(
                      value.toInt().toString(),
                      style: textTheme.labelSmall,
                      textAlign: TextAlign.right,
                    ),
                  );
                },
              ),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 22,
                interval: 1,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index < 0 || index >= bottomTitles.length) return Container();
                  return Padding(
                    padding: const EdgeInsets.only(top: 4.0),
                    child: Text(bottomTitles[index], style: textTheme.labelSmall),
                  );
                },
              ),
            ),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border(
              bottom: BorderSide(color: theme.dividerColor, width: 1.0),
              left: BorderSide(color: theme.dividerColor, width: 1.0),
            ),
          ),
          minX: 0,
          maxX: 6,
          minY: 0,
          maxY: maxY,
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: colorScheme.primary,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: const FlDotData(show: true),
              belowBarData: BarAreaData(
                show: true,
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primary.withValues(alpha: 0.3),
                    colorScheme.primary.withValues(alpha: 0.1),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
                cutOffY: 0,
                applyCutOffY: true,
              ),
            ),
          ],
          lineTouchData: LineTouchData(enabled: false),
        );

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Padding(
            padding: const EdgeInsets.only(left: 12, top: 16, right: 16, bottom: 12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 10.0),
                  child: Text(
                    l10n.weeklyTimerChart_title,
                    style: textTheme.titleMedium?.copyWith(
                      fontSize: 13,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                AspectRatio(
                  aspectRatio: 1.8,
                  child: LineChart(
                    lineChartData
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
