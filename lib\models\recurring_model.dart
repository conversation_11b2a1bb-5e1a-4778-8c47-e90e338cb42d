// import 'dart:developer' as developer;
import 'package:intl/intl.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
// tools
import '../tools/utils.dart';
import '../tools/config.dart';
import '../tools/explicit_value.dart';
// others
import '../generated/l10n/app_localizations.dart';

/// ⚠️ 这段注释不要删除：
/// ⚠️ 如果 Models 字段有改动
/// ⚠️ 请提醒我同步更新 Cloud Functions 和 Firebase Rules

class RecurringModel extends Equatable {
  final String id;                           // 唯一 id
  final String userId;                       // 关联用户 id
  final DateTime createTime;                 // 创建时间
  final DateTime updateTime;                 // 更新时间
  final String timezone;                     // 时区
  final RecurringTemplate template;          // 任务数据
  final RecurringRule rule;                  // 周期任务的规则

  const RecurringModel({
    required this.id,
    required this.userId,
    required this.createTime,
    required this.updateTime,
    required this.timezone,
    required this.template,
    required this.rule,
  });

  RecurringModel copyWith({
    ExplicitValue<String>? id,
    ExplicitValue<String>? userId,
    ExplicitValue<DateTime>? createTime,
    ExplicitValue<DateTime>? updateTime,
    ExplicitValue<String>? timezone,
    ExplicitValue<RecurringTemplate>? template,
    ExplicitValue<RecurringRule>? rule,
  }) {
    return RecurringModel(
      id: id == null ? this.id : id.value,
      userId: userId == null ? this.userId : userId.value,
      createTime: createTime == null ? this.createTime : createTime.value,
      updateTime: updateTime == null ? this.updateTime : updateTime.value,
      timezone: timezone == null ? this.timezone : timezone.value,
      template: template == null ? this.template : template.value,
      rule: rule == null ? this.rule : rule.value,
    );
  }

  factory RecurringModel.fromDatabase(Map<String, dynamic> json) {
    return RecurringModel(
      id: json['id'] as String? ?? '', // ID 由 Service 添加
      userId: json['userId'] as String? ?? '',
      createTime: (json['createTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updateTime: (json['updateTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      timezone: json['timezone'] as String? ?? Config.app.defaultTimezone,
      template: RecurringTemplate.fromDatabase(json['template'] ?? {}),
      rule: RecurringRule.fromDatabase(json['rule'] ?? {}),
    );
  }

  Map<String, dynamic> toDatabase() {
    return {
      'timezone': timezone,
      'template': template.toDatabase(),
      'rule': rule.toDatabase(),
    };
  }

  // Equatable 需要比较的属性
  @override
  List<Object?> get props => [
    id,
    userId,
    createTime,
    updateTime,
    timezone,
    template,
    rule,
  ];
}

////////////////////////////////////////////////
/// 周期任务模板
////////////////////////////////////////////////
class RecurringTemplate extends Equatable {
  final String name;
  final String? projectId;
  final DateTime? dueClock; // 模板中的时间是仅时钟分钟部分, 日期部分是占位符
  final bool isImportant;
  final bool isUrgent;

  const RecurringTemplate({
    required this.name,
    this.projectId,
    this.dueClock,
    this.isImportant = false,
    this.isUrgent = false,
  });

  RecurringTemplate copyWith({
    ExplicitValue<String>? name,
    ExplicitValue<String?>? projectId,
    ExplicitValue<DateTime?>? dueClock,
    ExplicitValue<bool>? isImportant,
    ExplicitValue<bool>? isUrgent,
  }) {
    return RecurringTemplate(
      name: name == null ? this.name : name.value,
      projectId: projectId == null ? this.projectId : projectId.value,
      dueClock: dueClock == null ? this.dueClock : dueClock.value,
      isImportant: isImportant == null ? this.isImportant : isImportant.value,
      isUrgent: isUrgent == null ? this.isUrgent : isUrgent.value,
    );
  }

  factory RecurringTemplate.fromDatabase(Map<String, dynamic> json) {
    return RecurringTemplate(
      name: json['name'] as String? ?? '',
      projectId: json['projectId'] as String?, // 可空
      dueClock: (json['dueClock'] as Timestamp?)?.toDate(), // 可空
      isImportant: json['isImportant'] as bool? ?? false,
      isUrgent: json['isUrgent'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toDatabase() {
    return {
      'name': name,
      'projectId': projectId,
      'dueClock': dueClock != null ? Timestamp.fromDate(dueClock!) : null,
      'isImportant': isImportant,
      'isUrgent': isUrgent,
    };
  }

  // Equatable 需要比较的属性
  @override
  List<Object?> get props => [
    name,
    projectId,
    dueClock,
    isImportant,
    isUrgent,
  ];
}

////////////////////////////////////////////////
/// 周期任务规则
////////////////////////////////////////////////

// 定义频率枚举
enum RecurringFrequency {
  daily,
  weekly,
  monthly,
  yearly,
}

// 扩展以获取中文名称
extension RecurringFrequencyExtension on RecurringFrequency {
  String getLocalizedName(AppLocalizations l10n) {
    switch (this) {
      case RecurringFrequency.daily:
        return l10n.common_day;
      case RecurringFrequency.weekly:
        return l10n.common_week;
      case RecurringFrequency.monthly:
        return l10n.common_month;
      case RecurringFrequency.yearly:
        return l10n.common_year;
    }
  }

  // 从字符串获取枚举值，提供默认值
  static RecurringFrequency fromString(String? freqString) {
    switch (freqString?.toLowerCase()) {
      case 'daily':
        return RecurringFrequency.daily;
      case 'weekly':
        return RecurringFrequency.weekly;
      case 'monthly':
        return RecurringFrequency.monthly;
      case 'yearly':
        return RecurringFrequency.yearly;
      default:
        return RecurringFrequency.daily; // 默认返回 daily
    }
  }
}

class RecurringRule extends Equatable {
  final RecurringFrequency frequency;        // 频率枚举
  final int interval;                        // 间隔：例如每 1 天、每 2 周，每 3 月
  final List<int>? daysOfWeek;               // 每周的哪几天（1-7, Monday-Sunday）
  final List<int>? daysOfMonth;              // 每月的哪几天（1-31）
  final List<MonthDay>? daysOfYear;          // 每年的哪几天，格式: [{m: 3, d: 1}, {m: 9, d: 1}]
  final DateTime startTime;                  // 周期规则的开始日期（通常只关心日期部分）
  final DateTime? endTime;                   // 周期规则的结束日期（可选，通常只关心日期部分）
  final int? repeatCount;                    // 重复次数，与结束时间只能二选一

  const RecurringRule({
    required this.frequency,
    this.interval = 1, // 默认间隔为 1
    this.daysOfWeek,
    this.daysOfMonth,
    this.daysOfYear,
    required this.startTime,
    this.endTime,
    this.repeatCount,
  });

  // 获取规则的本地化描述字符串
  String getLocalizedDescription(AppLocalizations l10n) {
    String freqPart = l10n.recurringModel_every;
    
    String intervalPart = (interval > 1) ? '$interval' : '';

    String freqName = frequency.getLocalizedName(l10n);

    String datePart = '';
    switch (frequency) {
      case RecurringFrequency.weekly:
        if (daysOfWeek != null && daysOfWeek!.isNotEmpty) {
          final sortedDays = List<int>.from(daysOfWeek!)..sort();
          final weekdays = l10n.common_weekdays.split(',');
          final dayNames = sortedDays.map((d) => weekdays[d - 1]).toList();
          if (dayNames.isNotEmpty) {
            datePart = ' ${dayNames.join(', ')}';
          }
        }
        break;
      case RecurringFrequency.monthly:
        if (daysOfMonth != null && daysOfMonth!.isNotEmpty) {
          final sortedDays = List<int>.from(daysOfMonth!)..sort();
          datePart = ' ${sortedDays.join(', ')}';
        }
        break;
      case RecurringFrequency.yearly:
        if (daysOfYear != null && daysOfYear!.isNotEmpty) {
          final sortedDays = List<MonthDay>.from(daysOfYear!)..sort((a, b) => a.compareTo(b));
          final formatter = DateFormat(l10n.recurringModel_doyFormat, l10n.localeName);
          datePart = ' ${sortedDays.map((md) => formatter.format(DateTime(2024, md.month, md.day))).join(', ')}';
        } else {
          final formatter = DateFormat(l10n.recurringModel_doyFormat, l10n.localeName);
          datePart = ' ${formatter.format(startTime)}';
        }
        break;
      case RecurringFrequency.daily:
        break;
    }

    return l10n.recurringModel_description(
      freqPart,
      intervalPart,
      freqName,
      datePart,
    );
  }

  String getLocalizedTimeRange(AppLocalizations l10n) {
    final DateFormat formatter = DateFormat('yyyy-MM-dd', l10n.localeName);
    final String start = formatter.format(startTime);
    String end = '';

    if (endTime != null) {
      end = l10n.recurringModel_toDate(formatter.format(endTime!));
    } else if (repeatCount != null) {
      end = l10n.recurringModel_repeat(repeatCount!);
    } else {
      end = l10n.recurringModel_toInfinite;
    }
    return '$start $end';
  }

  RecurringRule copyWith({
    ExplicitValue<RecurringFrequency>? frequency,
    ExplicitValue<int>? interval,
    ExplicitValue<List<int>?>? daysOfWeek,
    ExplicitValue<List<int>?>? daysOfMonth,
    ExplicitValue<List<MonthDay>?>? daysOfYear,
    ExplicitValue<DateTime>? startTime,
    ExplicitValue<DateTime?>? endTime,
    ExplicitValue<int?>? repeatCount,
  }) {
    return RecurringRule(
      frequency: frequency == null ? this.frequency : frequency.value,
      interval: interval == null ? this.interval : interval.value,
      daysOfWeek: daysOfWeek == null ? this.daysOfWeek : daysOfWeek.value,
      daysOfMonth: daysOfMonth == null ? this.daysOfMonth : daysOfMonth.value,
      daysOfYear: daysOfYear == null ? this.daysOfYear : daysOfYear.value,
      startTime: startTime == null ? this.startTime : startTime.value,
      endTime: endTime == null ? this.endTime : endTime.value,
      repeatCount: repeatCount == null ? this.repeatCount : repeatCount.value,
    );
  }

  factory RecurringRule.fromDatabase(Map<String, dynamic> json) {
    // 处理 daysOfYear
    List<MonthDay>? parsedDaysOfYear;
    if (json['daysOfYear'] != null && json['daysOfYear'] is List) {
      parsedDaysOfYear = (json['daysOfYear'] as List<dynamic>)
        .map((e) => MonthDay.fromDatabase(e as Map<String, dynamic>))
        .toList();
    }

    return RecurringRule(
      frequency: Utils.getEnumFromString(RecurringFrequency.values, json['frequency'] as String?) ?? RecurringFrequency.daily,
      interval: json['interval'] as int? ?? 1,
      // Firestore 存储 List<dynamic>，需要转换成 List<int>
      daysOfWeek: (json['daysOfWeek'] as List<dynamic>?)?.map((e) => e as int).toList(), // 可空
      daysOfMonth: (json['daysOfMonth'] as List<dynamic>?)?.map((e) => e as int).toList(), // 可空
      daysOfYear: parsedDaysOfYear, // 可空
      startTime: (json['startTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      endTime: (json['endTime'] as Timestamp?)?.toDate(), // 可空
      repeatCount: json['repeatCount'] as int?, // 可空
    );
  }

  Map<String, dynamic> toDatabase() {
    return {
      'frequency': frequency.name, // 存储枚举的名称（字符串）
      'interval': interval,
      'daysOfWeek': daysOfWeek,
      'daysOfMonth': daysOfMonth,
      'daysOfYear': daysOfYear?.map((md) => md.toDatabase()).toList(),
      'startTime': Timestamp.fromDate(startTime),
      'endTime': endTime != null ? Timestamp.fromDate(endTime!) : null,
      'repeatCount': repeatCount,
    };
  }

  // Equatable 需要比较的属性
  @override
  List<Object?> get props => [
    frequency,
    interval,
    daysOfWeek,
    daysOfMonth,
    daysOfYear,
    startTime,
    endTime,
    repeatCount,
  ];
}

// 定义 月/日 结构
class MonthDay extends Equatable {
  final int month;
  final int day;

  const MonthDay({
    required this.month,
    required this.day,
  });

  // fromDatabase 构造函数
  factory MonthDay.fromDatabase(Map<String, dynamic> json) {
    return MonthDay(
      month: json['month'] ?? 1, // 提供默认值
      day: json['day'] ?? 1, // 提供默认值
    );
  }

  // toDatabase 方法
  Map<String, int> toDatabase() {
    return {'month': month, 'day': day};
  }

  // 比较函数，用于排序
  int compareTo(MonthDay other) {
    if (month != other.month) {
      return month.compareTo(other.month);
    }
    return day.compareTo(other.day);
  }

  // Equatable 需要比较的属性
  @override
  List<Object?> get props => [month, day];
}
