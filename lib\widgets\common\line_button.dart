// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 按钮组件
/// 
/// 一个统一样式的按钮组件，封装了ElevatedButton
/// 提供了统一的样式和常用的配置选项
class LineButton extends StatelessWidget {
  /// 按钮文本
  final String text;
  
  /// 可选图标
  final IconData? icon;
  
  /// 点击事件处理函数
  final VoidCallback? onPressed;
  
  /// 是否禁用按钮
  final bool disabled;

  final bool isLoading;
  
  /// 是否为危险操作按钮（红色）
  final bool isDanger;

  const LineButton({
    super.key,
    required this.text,
    this.icon,
    required this.onPressed,
    this.disabled = false,
    this.isLoading = false,
    this.isDanger = false,
  });

  @override
  Widget build(BuildContext context) {
    final buttonStyle = _getButtonStyle(context);

    // 加载中使用 CircularProgressIndicator 按钮
    if (isLoading) {
      return ElevatedButton(
        onPressed: null,
        style: buttonStyle,
        child: const SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      );
    }
    
    // 有图标时使用 ElevatedButton.icon
    if (icon != null) {
      return ElevatedButton.icon(
        onPressed: () {
          // 增加震动反馈
          HapticFeedback.lightImpact();
          if (!disabled) {
            onPressed?.call();
          }
        },
        icon: Icon(icon),
        label: Text(text),
        style: buttonStyle,
      );
    }
    
    // 无图标时使用普通的 ElevatedButton
    return ElevatedButton(
      onPressed: () {
        // 增加震动反馈
        HapticFeedback.lightImpact();
        if (!disabled) {
          onPressed?.call();
        }
      },
      style: buttonStyle,
      child: Text(
        text,
        style: const TextStyle(
          fontSize: 16, 
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  ButtonStyle _getButtonStyle(BuildContext context) {
    final themeButtonStyle = Theme.of(context).elevatedButtonTheme.style ?? const ButtonStyle();

    if (isDanger) {
      // 危险操作按钮样式 - 使用 error color
      return themeButtonStyle.copyWith(
        backgroundColor: WidgetStateProperty.all(Theme.of(context).colorScheme.error),
        foregroundColor: WidgetStateProperty.all(Theme.of(context).colorScheme.onError),
      );
    } else {
      // 普通按钮样式 - 默认样式已经很好，但我们可以确保它使用了 colorScheme
      return themeButtonStyle.copyWith(
        backgroundColor: WidgetStateProperty.all(Theme.of(context).colorScheme.primary),
        foregroundColor: WidgetStateProperty.all(Theme.of(context).colorScheme.onPrimary),
      );
    }
  }
}
