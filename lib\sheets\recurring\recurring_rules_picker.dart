// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/recurring_model.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
// sheets
import './rules_mode_picker.dart';
import './rules_dow_picker.dart';
import './rules_dom_picker.dart';
import './rules_doy_picker.dart';
import './rules_start_picker.dart';
import './rules_end_picker.dart';
import './rules_count_picker.dart';
// others
import '../../generated/l10n/app_localizations.dart';

typedef RecurringRuleCallback = void Function(RecurringRule? rule);

class RecurringRulesPicker extends StatelessWidget {
  final RecurringRule? selectedRule;
  final RecurringRuleCallback onSelected;

  const RecurringRulesPicker({
    super.key,
    this.selectedRule,
    required this.onSelected,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return InkWell(
      onTap: () => _showRecurringPicker(context),
      child: Row(
        children: [
          // 左侧图标
          Icon(
            LucideIcons.repeat,
            size: 20,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 24),

          Expanded(
            child: Text(
              selectedRule?.getLocalizedDescription(l10n) ?? l10n.recurringRulesPicker_noRule,
              style: const TextStyle(fontSize: 16),
            ),
          ),
          // 重复规则不需要清除按钮
        ],
      ),
    );
  }

  void _showRecurringPicker(BuildContext context) {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    showModalBottomSheet(
      context: context,
      // 强制使用自定义的高度
      isScrollControlled: true,
      builder: (BuildContext context) {
        // 使用 StatefulWidget 来管理底部弹窗内的状态
        return _RecurringRulesSheet(
          initialRule: selectedRule,
          onRuleChanged: onSelected,
        );
      },
    );
  }
}

// 底部弹窗内容的 StatefulWidget
class _RecurringRulesSheet extends StatefulWidget {
  final RecurringRule? initialRule;
  final RecurringRuleCallback onRuleChanged;

  const _RecurringRulesSheet({
    this.initialRule,
    required this.onRuleChanged,
  });

  @override
  State<_RecurringRulesSheet> createState() => _RecurringRulesSheetState();
}

class _RecurringRulesSheetState extends State<_RecurringRulesSheet> {
  late RecurringFrequency _selectedFrequency;
  late int _selectedInterval;
  List<int>? _selectedDaysOfWeek;
  List<int>? _selectedDaysOfMonth;
  List<MonthDay>? _selectedDaysOfYear; // 更新类型
  late DateTime _selectedStartDate;
  DateTime? _selectedEndDate;
  int? _selectedRepeatCount;

  @override
  void initState() {
    super.initState();
    _selectedFrequency = widget.initialRule?.frequency ?? RecurringFrequency.daily;
    _selectedInterval = widget.initialRule?.interval ?? 1;
    _selectedDaysOfWeek = widget.initialRule?.daysOfWeek;
    _selectedDaysOfMonth = widget.initialRule?.daysOfMonth;
    _selectedDaysOfYear = widget.initialRule?.daysOfYear; // 更新类型
    _selectedStartDate = widget.initialRule?.startTime ?? DateTime.now();
    _selectedEndDate = widget.initialRule?.endTime;
    _selectedRepeatCount = widget.initialRule?.repeatCount;
  }

  @override
  void dispose() {
    super.dispose();
  }

  void _confirmAndClose() {
    List<int>? daysOfWeek = (_selectedFrequency == RecurringFrequency.weekly) ? _selectedDaysOfWeek : null;
    List<int>? daysOfMonth = (_selectedFrequency == RecurringFrequency.monthly) ? _selectedDaysOfMonth : null;
    List<MonthDay>? daysOfYear = (_selectedFrequency == RecurringFrequency.yearly) ? _selectedDaysOfYear : null;

    final DateTime? effectiveEndTime = _selectedRepeatCount == null ? _selectedEndDate : null;
    final int? effectiveRepeatCount = _selectedEndDate == null ? _selectedRepeatCount : null;

    final newRule = RecurringRule(
      frequency: _selectedFrequency,
      interval: _selectedInterval,
      startTime: _selectedStartDate,
      endTime: effectiveEndTime,
      repeatCount: effectiveRepeatCount,
      daysOfWeek: daysOfWeek,
      daysOfMonth: daysOfMonth,
      daysOfYear: daysOfYear,
    );
    widget.onRuleChanged(newRule);
    Navigator.pop(context);
  }

  void _resetSpecificDaySelections() {
    _selectedDaysOfWeek = null;
    _selectedDaysOfMonth = null;
    _selectedDaysOfYear = null;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BaseBottomSheet(
      title: l10n.recurringRulesPicker_title,
      onConfirm: _confirmAndClose,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // --- 重复模式选择器 ---
            RulesModePicker(
              currentInterval: _selectedInterval,
              currentFrequency: _selectedFrequency,
              onIntervalSelected: (interval) {
                setState(() {
                  _selectedInterval = interval;
                });
              },
              onFrequencySelected: (frequency) {
                setState(() {
                  _resetSpecificDaySelections(); // 重置时也会将 _selectedDaysOfYear 设为 null
                  _selectedFrequency = frequency;
                });
              },
            ),

            const Divider(height: 32),

            // --- 具体日期选择器 ---
            if (_selectedFrequency == RecurringFrequency.weekly)
              RulesDowPicker(
                currentDaysOfWeek: _selectedDaysOfWeek,
                onDaysSelected: (days) {
                  setState(() {
                    _selectedDaysOfWeek = days;
                  });
                },
              ),
            if (_selectedFrequency == RecurringFrequency.monthly)
              RulesDomPicker(
                currentDaysOfMonth: _selectedDaysOfMonth,
                onDaysSelected: (days) {
                  setState(() {
                    _selectedDaysOfMonth = days;
                  });
                },
              ),
            if (_selectedFrequency == RecurringFrequency.yearly)
              RulesDoyPicker(
                currentDaysOfYear: _selectedDaysOfYear, // 传递 MonthDay 列表
                onDaysSelected: (days) {
                  setState(() {
                    _selectedDaysOfYear = days; // 接收 MonthDay 列表
                  });
                },
              ),

            if (_selectedFrequency != RecurringFrequency.daily)
              const Divider(height: 32),

            // --- 开始日期选择器---
            RulesStartPicker(
              currentStartDate: _selectedStartDate,
              onDateSelected: (date) {
                setState(() {
                  _selectedStartDate = date;
                  if (_selectedEndDate != null && !date.isBefore(_selectedEndDate!)) {
                    _selectedEndDate = null;
                  }
                });
              },
            ),

            const Divider(height: 32),

            // --- 结束条件选择器 ---
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: RulesEndPicker(
                    currentEndDate: _selectedEndDate,
                    startDate: _selectedStartDate,
                    onDateSelected: (date) {
                      setState(() {
                        _selectedEndDate = date;
                        if (date != null) {
                          _selectedRepeatCount = null;
                        }
                      });
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: RulesCountPicker(
                    currentRepeatCount: _selectedRepeatCount,
                    onCountSelected: (count) {
                      setState(() {
                        _selectedRepeatCount = count;
                        if (count != null) {
                          _selectedEndDate = null;
                        }
                      });
                    },
                  ),
                ),
              ],
            ),

            const Divider(height: 32),
          ],
        ),
      ),
    );
  }
}
