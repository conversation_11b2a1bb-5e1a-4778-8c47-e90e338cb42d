import { onCall, HttpsError } from 'firebase-functions/v2/https';
import * as logger from 'firebase-functions/logger';
import { db, FIREBASE, DATABASE } from '../tools/config.js';
/**
 * 删除一个任务及其关联的计时器。
 */
export const deleteTask = onCall({ region: FIREBASE.REGION }, async (request) => {
    if (!request.auth) {
        throw new HttpsError('unauthenticated', 'Authentication is required for this operation');
    }
    const uid = request.auth.uid;
    const { taskId } = request.data;
    if (!taskId || typeof taskId !== 'string') {
        throw new HttpsError('invalid-argument', 'A valid taskId must be provided');
    }
    const taskDocRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid).collection(DATABASE.TASKS_COLLECTION).doc(taskId);
    const timersCollectionRef = db.collection(DATABASE.USERS_COLLECTION).doc(uid).collection(DATABASE.TIMERS_COLLECTION);
    try {
        const batch = db.batch();
        // 1. 添加删除任务文档的操作
        batch.delete(taskDocRef);
        // 2. 查询并删除关联的计时器
        const timerQuerySnapshot = await timersCollectionRef.where('taskId', '==', taskId).get();
        for (const timerDoc of timerQuerySnapshot.docs) {
            batch.delete(timerDoc.ref);
        }
        // 3. 提交批量操作
        await batch.commit();
        return { success: true };
    }
    catch (error) {
        logger.error(`为用户 ${uid} 删除任务 ${taskId} 失败:`, error);
        throw new HttpsError('internal', 'An unknown error occurred while deleting the task');
    }
});
//# sourceMappingURL=task.js.map