// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

typedef DomCallback = void Function(List<int>? days);

// 月份日期选择器 (Days of Month) - 点击占位符弹出选择
class RulesDomPicker extends StatelessWidget {
  final List<int>? currentDaysOfMonth; // 当前选中的日期 (1-31)
  final DomCallback onDaysSelected;

  const RulesDomPicker({
    super.key,
    this.currentDaysOfMonth,
    required this.onDaysSelected,
  });

  // 格式化显示的选中日期
  String _formatSelectedDays(AppLocalizations l10n) {
    if (currentDaysOfMonth == null || currentDaysOfMonth!.isEmpty) {
      return l10n.rulesDomPicker_placeholder; // 占位符
    }
    final sortedDays = List<int>.from(currentDaysOfMonth!)..sort();
    return sortedDays.join(', ');
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: EdgeInsets.only(bottom: 12.0),
          child: Text(
            l10n.common_specificDate,
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: colorScheme.onSurfaceVariant),
          ),
        ),
        // 可点击的选择区域
        InkWell(
          onTap: () => _showRulesDomSheet(context),
          child: Container(
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    _formatSelectedDays(l10n),
                    style: TextStyle(
                      fontSize: 16,
                      color: (currentDaysOfMonth == null || currentDaysOfMonth!.isEmpty)
                          ? colorScheme.onSurfaceVariant
                          : colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center, // 文本居中
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Icon(LucideIcons.chevronsUpDown, size: 18, color: colorScheme.onSurfaceVariant),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 显示月份日期选择底部弹窗
  void _showRulesDomSheet(BuildContext context) {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;
    List<int> tempSelectedDays = List<int>.from(currentDaysOfMonth ?? []);

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            void toggleDayInModal(int day) {
              setModalState(() {
                if (tempSelectedDays.contains(day)) {
                  tempSelectedDays.remove(day);
                } else {
                  if (tempSelectedDays.length < 5) {
                    tempSelectedDays.add(day);
                    tempSelectedDays.sort();
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(l10n.common_maxSelection(5)), duration: Duration(seconds: 1)),
                    );
                  }
                }
              });
            }

            return BaseBottomSheet(
              title: l10n.rulesDomPicker_title,
              onConfirm: () {
                // 确认选择，调用回调并关闭弹窗
                onDaysSelected(tempSelectedDays.isEmpty ? null : tempSelectedDays);
                Navigator.pop(context);
              },
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 7,
                  childAspectRatio: 1.0, // 调整宽高比为 1:1
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: 31,
                itemBuilder: (context, index) {
                  final day = index + 1;
                  final isSelected = tempSelectedDays.contains(day);
                  return ChoiceChip(
                    label: Center(child: Text(day.toString())), // 文本居中
                    selected: isSelected,
                    onSelected: (selected) {
                      // 增加震动反馈
                      HapticFeedback.lightImpact();
                      toggleDayInModal(day);
                    },
                    selectedColor: colorScheme.primary.withValues(alpha: 0.2),
                    showCheckmark: false,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    labelPadding: EdgeInsets.zero,
                    padding: EdgeInsets.zero,
                    side: BorderSide(
                      color: isSelected ? Colors.transparent : colorScheme.outline,
                      width: 1.0,
                    ),
                    // 确保文本颜色正确
                    labelStyle: TextStyle(
                      color: isSelected ? colorScheme.primary : colorScheme.onSurface,
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
