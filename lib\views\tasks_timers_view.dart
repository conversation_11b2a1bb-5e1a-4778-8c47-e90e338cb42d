// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// models
import '../models/task_model.dart';
// states
import '../states/task_state.dart';
import '../states/timer_state.dart';
// tools
import '../tools/config.dart';
import '../tools/extensions.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/task_card.dart';
import '../widgets/timer/mini_timer_card.dart';
// others
import '../generated/l10n/app_localizations.dart';

class TasksTimersView extends StatelessWidget {
  const TasksTimersView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer2<TaskState, TimerNotifier>(
      builder: (context, taskState, timerNotifier, _) {
        final tasks = taskState.getAllTasks(); // 获取所有任务

        // 检查所有任务中，是否有任何一个任务有关联的计时器
        final hasAnyTimers =
            tasks.any((task) => timerNotifier.state.getTimerCountForTask(task.id) > 0);

        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(l10n.tasksTimersView_title),
          ),
          body: CustomScrollView(
            slivers: [
              if (!hasAnyTimers)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(child: Text(l10n.tasksTimersView_emptyMessage)),
                )
              else
                SliverList(
                  delegate: SliverChildBuilderDelegate(
                    (context, index) {
                      return _buildTaskTimersList(context, tasks[index], timerNotifier);
                    },
                    childCount: tasks.length,
                  ),
                ),

              // 在最后添加一个底部空间
              // 列表为空时不添加底部空间，否则可以滚动，不符合预期
              if (hasAnyTimers)
                SliverToBoxAdapter(
                  child: SizedBox(height: Config.app.bottomSpace),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTaskTimersList(BuildContext context, TaskModel task, TimerNotifier timerNotifier) {
    final timers = timerNotifier.state.getTimersForTask(task.id); // 获取该任务的计时器列表

    if (timers.isEmpty) {
      return const SizedBox.shrink(); // 如果没有计时器，返回空容器
    }

    // --- 逻辑修改 ---
    DateTime? displayDate = task.deadlineTime;

    return SingleChildScrollView( // 使用 SingleChildScrollView 允许滚动
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildDateHeader(context, displayDate),

          // 显示任务卡片
          TaskCard(task: task),

          const SizedBox(height: 2.0),

          // 显示计时列表
          ListView.builder(
            shrinkWrap: true, // 在 Column 中需要设置 shrinkWrap
            physics: const NeverScrollableScrollPhysics(), // 禁用内部滚动
            itemCount: timers.length,
            itemBuilder: (context, index) {
              // 在这里重新添加 Align 组件
              return Align(
                alignment: Alignment.centerLeft,
                child: MiniTimerCard(timer: timers[index]),
              );
            },
          ),
        ],
      ),
    );
  }

  // 构建日期分隔标题
  Widget _buildDateHeader(BuildContext context, DateTime? date) {
    String headerText;
    final l10n = AppLocalizations.of(context)!;
    
    if (date == null) {
      headerText = l10n.common_noDueDate;
    } else {
      // 计算相对日期
      headerText = date.getRelativeDateText(l10n);
    }
    
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 8, 20, 4),
      alignment: Alignment.bottomLeft,
      child: Text(
        headerText,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
