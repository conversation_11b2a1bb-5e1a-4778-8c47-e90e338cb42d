// import 'dart:developer' as developer;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// states
import '../../states/timer_state.dart';
import '../../states/task_state.dart';
// tools
import '../../tools/extensions.dart';
// others
import '../../generated/l10n/app_localizations.dart';

/// 计时控制条组件
/// 
/// 悬浮在导航视图底部，与FAB齐平，显示计时信息并提供停止按钮
/// 自行获取计时数据，无需外部传参
class TimerFloatingBar extends StatefulWidget {
  const TimerFloatingBar({
    super.key,
  });

  @override
  State<TimerFloatingBar> createState() => _TimerFloatingBarState();
}

class _TimerFloatingBarState extends State<TimerFloatingBar> {
  // 保存计时时间
  Duration _elapsedTime = Duration.zero;
  // 用于更新计时器显示
  Timer? _timer;
  
  @override
  void initState() {
    super.initState();
    // 启动一个定时器每秒更新一次显示时间
    _timer = Timer.periodic(const Duration(seconds: 1), _updateTime);
  }
  
  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
  
  // 更新计时时间
  void _updateTime(Timer timer) {
    final timerState = Provider.of<TimerNotifier>(context, listen: false).state;
    
    if (timerState.runningTimer != null) {
      final startTime = timerState.runningTimer!.startTime;
      final now = DateTime.now();
      setState(() {
        _elapsedTime = now.difference(startTime);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    return Consumer<TimerNotifier>(
      builder: (context, timerNotifier, child) {
        final runningTimer = timerNotifier.state.runningTimer;
        
        // 如果没有活动的计时器，返回空容器
        if (runningTimer == null) {
          return const SizedBox.shrink();
        }
        
        // 尝试获取关联的任务名称
        String taskName = l10n.common_loading;
        
        // 使用Consumer获取任务状态
        return Consumer<TaskState>(
          builder: (context, taskState, _) {
            // 尝试根据runningTimer.taskId找到对应的任务
            final task = taskState.getTaskById(runningTimer.taskId);
            if (task != null) {
              taskName = task.name;
            } else {
              taskName = l10n.common_unknownTask;
            }
            
            final theme = Theme.of(context);
            final bgColor = theme.colorScheme.primary;
            final timeText = _elapsedTime.formatLong();
            
            return Material(
              // 阴影和 FloatingActionButton 一样
              elevation: 6,
              borderRadius: BorderRadius.circular(30),
              color: Colors.transparent,
              child: Container(
                height: 56,
                constraints: BoxConstraints(
                  // 减去FAB和边距的宽度
                  maxWidth: MediaQuery.of(context).size.width - 56 - 16 * 3,
                  // 和FAB一样高
                  maxHeight: 56,
                ),
                decoration: BoxDecoration(
                  color: bgColor,
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 左侧时间和任务名称
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(left: 28.0, top: 2.0, bottom: 2.0, right: 8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 时间显示
                            Text(
                              timeText,
                              style: TextStyle(
                                color: theme.colorScheme.onPrimary,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),

                            // 任务名称 - 如果太长会截断
                            Text(
                              taskName,
                              style: TextStyle(
                                color: theme.colorScheme.onPrimary,
                                fontSize: 16,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    // 右侧停止按钮
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          // 增加震动反馈
                          HapticFeedback.lightImpact();
                          timerNotifier.stopTimer();
                        },
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(28),
                          bottomRight: Radius.circular(28),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(right: 24.0),
                          child: Icon(
                            LucideIcons.circlePause,
                            color: theme.colorScheme.onPrimary,
                            size: 40,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
