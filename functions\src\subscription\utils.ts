import { Timestamp, FieldValue } from 'firebase-admin/firestore';
import * as logger from 'firebase-functions/logger';
import { db, DATABASE } from '../tools/config.js';
import { UserSubscriptionData, RevenueCatWebhookPayload, SubscriptionPlan, SubscriptionStatus, RevenueCatEvent } from '../tools/types.js';


/**
 * 根据 RevenueCat 的产品 ID (Product ID) 中的关键字动态确定订阅计划类型
 * @param {string} productId - RevenueCat 产品 ID
 * @returns {SubscriptionPlan | null} 应用内的订阅计划类型或 null
 */
function getPlanFromProductId(productId: string): SubscriptionPlan | null {
  if (productId.includes('monthly')) return SubscriptionPlan.MONTHLY;
  if (productId.includes('yearly')) return SubscriptionPlan.YEARLY;
  if (productId.includes('lifetime')) return SubscriptionPlan.LIFETIME;
  // 为 RevenueCat 的测试事件提供一个默认值
  if (productId.includes('test')) return SubscriptionPlan.MONTHLY;
  
  logger.warn('无法根据关键字识别 RevenueCat 产品 ID', { productId });
  return null;
}


/**
 * 将毫秒时间戳安全地转换为 Firestore Timestamp 对象
 * @param {number | null | undefined} msTimestamp - 毫秒时间戳
 * @returns {Timestamp | null} Firestore Timestamp 对象或 null
 */
function msToTimestamp(msTimestamp?: number | null): Timestamp | null {
  if (msTimestamp === undefined || msTimestamp === null) return null;
  return Timestamp.fromMillis(msTimestamp);
}


/**
 * 将单个 RevenueCat 事件映射为要更新到 Firestore 的用户订阅数据
 * @param {RevenueCatEvent} event - RevenueCat 事件对象
 * @returns {Partial<UserSubscriptionData> | null} 一个部分更新对象，或在无需更新时返回 null
 */
function mapRevenueCatEventToSubscriptionData(
  event: RevenueCatEvent
): Partial<UserSubscriptionData> | null {
  const data: Partial<UserSubscriptionData> = {};

  data.plan = getPlanFromProductId(event.product_id);
  data.startTime = msToTimestamp(event.purchased_at_ms);
  data.endTime = msToTimestamp(event.expiration_at_ms);
  data.isRenewal = event.will_renew ?? false;
    
  data.storeTransactionId = event.original_transaction_id;
  data.latestTransactionId = event.transaction_id;

  // 您提到目前只有 Google Play，但我们保留这个逻辑以备未来扩展
  switch (event.store) {
    case 'APP_STORE': data.paymentSource = 'app_store'; break;
    case 'PLAY_STORE': data.paymentSource = 'play_store'; break;
    case 'STRIPE': data.paymentSource = 'stripe'; break;
    default: data.paymentSource = 'unknown'; break;
  }
    
  const hasPremiumEntitlement = event.entitlement_ids?.includes('premium') ?? false;

  if (!hasPremiumEntitlement) {
    data.status = SubscriptionStatus.EXPIRED;
  } else {
    switch (event.type) {
      case 'INITIAL_PURCHASE':
      case 'RENEWAL':
      case 'UNCANCELLATION':
      case 'PRODUCT_CHANGE':
      case 'NON_RENEWING_PURCHASE':
        data.status = event.period_type === 'trial' ? SubscriptionStatus.TRIAL : SubscriptionStatus.ACTIVE;
        break;
      case 'BILLING_ISSUE':
        data.status = SubscriptionStatus.GRACE_PERIOD;
        break;
      case 'CANCELLATION':
      case 'EXPIRATION':
      case 'SUBSCRIPTION_PAUSED':
        data.status = SubscriptionStatus.EXPIRED;
        break;
      default:
        // 对于 TEST, TRANSFER, SUBSCRIBER_ALIAS 等测试或管理性事件，我们不更新状态，直接忽略
        return null;
    }
  }
    
  logger.debug('已将 RevenueCat 事件映射为订阅数据更新', { userId: event.app_user_id, data });
  return data;
}


/**
 * 处理 RevenueCat Webhook 事件的核心逻辑，在 Firestore 事务中更新用户文档
 * @param {RevenueCatWebhookPayload} payload - 从 Webhook 接收的完整数据
 * @returns {Promise<void>}
 */
export async function processRevenueCatEvent(payload: RevenueCatWebhookPayload): Promise<void> {
  const userId = payload.event.app_user_id;
  if (!userId) {
    logger.error('RevenueCat 事件中缺少 app_user_id', { payload });
    return;
  }

  const userDocRef = db.collection(DATABASE.USERS_COLLECTION).doc(userId);

  try {
    await db.runTransaction(async (transaction) => {
      const newSubscriptionPartial = mapRevenueCatEventToSubscriptionData(payload.event);
      
      if (!newSubscriptionPartial) {
        logger.info('事件无需更新订阅状态，跳过数据库写入', { userId, eventType: payload.event.type });
        return;
      }
      
      const userDoc = await transaction.get(userDocRef);
      // 安全地获取当前订阅数据，即使文档或字段不存在也能正常工作
      const currentSubscription = (userDoc.exists ? userDoc.data()?.subscription : {}) || {};
      
      // 合并旧数据和新数据，用新数据覆盖旧数据
      const finalSubscriptionData: UserSubscriptionData = {
        ...currentSubscription,
        ...newSubscriptionPartial,
      };
      
      const updatePayload = {
        subscription: finalSubscriptionData,
        updateTime: FieldValue.serverTimestamp(),
      };

      if (userDoc.exists) {
        transaction.update(userDocRef, updatePayload);
      } else {
        // 用户文档不存在，说明可能是个新用户或匿名用户。
        // 为了数据一致性，我们创建一个包含所有必需字段的新用户文档。
        logger.warn('用户文档不存在，将创建一个包含订阅信息的新文档', { userId });

        const newUserPayload = {
          // 提供符合安全规则的默认值
          createTime: FieldValue.serverTimestamp(),
          updateTime: FieldValue.serverTimestamp(),
          lastSignInTime: FieldValue.serverTimestamp(),
          authProvider: 'unknown', // 标记来源为 webhook 创建
          name: 'Webhook Generated User',
          email: null, // 假设新用户 email 未知
          photoURL: null,
          subscription: finalSubscriptionData,
        };

        transaction.set(userDocRef, newUserPayload);
      }
    });
    logger.info('成功处理 RevenueCat 事件并更新了用户订阅状态', { userId });
  } catch (error) {
    logger.error('处理 RevenueCat 事件或更新 Firestore 时出错', { userId, error, payload });
    throw error;
  }
}
