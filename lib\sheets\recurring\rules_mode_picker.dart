// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/recurring_model.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

// 定义回调类型
typedef IntervalCallback = void Function(int interval);
typedef FrequencyCallback = void Function(RecurringFrequency frequency);

// 重复模式选择器 (封装间隔和频率)
class RulesModePicker extends StatelessWidget {
  final int currentInterval;
  final RecurringFrequency currentFrequency;
  final IntervalCallback onIntervalSelected;
  final FrequencyCallback onFrequencySelected;

  const RulesModePicker({
    super.key,
    required this.currentInterval,
    required this.currentFrequency,
    required this.onIntervalSelected,
    required this.onFrequencySelected,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: EdgeInsets.only(bottom: 12.0),
          child: Text(
            l10n.rulesModePicker_title,
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: colorScheme.onSurfaceVariant),
          ),
        ),
        // 选择器行
        Row(
          children: [
            Text(l10n.recurringModel_every, style: TextStyle(fontSize: 16)),
            const SizedBox(width: 8),
            // --- 间隔选择器 (InkWell 触发弹窗) ---
            Expanded(
              child: InkWell(
                onTap: () => _showRulesModeSheet(context),
                child: Container(
                  padding: const EdgeInsets.all(12.0),
                  decoration: BoxDecoration(
                    color: colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Row(
                    children: [
                      Expanded( // 让文本区域填充可用空间
                        child: Center( // 将文本居中
                          child: Text(
                            currentInterval.toString(),
                            style: TextStyle(fontSize: 16, color: colorScheme.onSurface),
                          ),
                        ),
                      ),
                      Icon(LucideIcons.chevronsUpDown, size: 18, color: colorScheme.onSurfaceVariant), // 图标保持在最右侧
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            // --- 频率选择器 (InkWell 触发弹窗) ---
            Expanded(
              child: InkWell(
                onTap: () => _showFrequencyPicker(context),
                child: Container(
                  padding: const EdgeInsets.all(12.0),
                  decoration: BoxDecoration(
                    color: colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                  child: Row(
                    children: [
                      Expanded( // 让文本区域填充可用空间
                        child: Center( // 将文本居中
                          child: Text(
                            currentFrequency.getLocalizedName(l10n),
                            style: TextStyle(fontSize: 16, color: colorScheme.onSurface),
                          ),
                        ),
                      ),
                      Icon(LucideIcons.chevronsUpDown, size: 18, color: colorScheme.onSurfaceVariant), // 图标保持在最右侧
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // 显示间隔选择 bottom sheet
  void _showRulesModeSheet(BuildContext context) {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final List<int> intervalOptions = List.generate(30, (index) => index + 1);

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return BaseBottomSheet(
          fullWidth: true,
          child: SizedBox(
            height: 300, // 固定高度
            child: ListView.builder(
              itemCount: intervalOptions.length,
              itemBuilder: (context, index) {
                final interval = intervalOptions[index];
                return ListTile(
                  title: Center(
                    child: Text(
                      interval.toString(),
                      style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                    ),
                  ),
                  dense: true,
                  selected: currentInterval == interval, // 直接比较 currentInterval
                  selectedTileColor: Theme.of(context).colorScheme.primaryContainer,
                  onTap: () {
                    // 增加震动反馈
                    HapticFeedback.lightImpact();
                    // 直接回调并关闭弹窗
                    onIntervalSelected(interval);
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  // 显示频率选择 bottom sheet
  void _showFrequencyPicker(BuildContext context) {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final List<RecurringFrequency> frequencyOptions = RecurringFrequency.values;
    final l10n = AppLocalizations.of(context)!;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return BaseBottomSheet(
          fullWidth: true,
          child: ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: frequencyOptions.length,
            itemBuilder: (context, index) {
              final frequency = frequencyOptions[index];
              return ListTile(
                title: Center(
                  child: Text(
                    frequency.getLocalizedName(l10n),
                    style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                  ),
                ),
                dense: true,
                selected: currentFrequency == frequency, // 直接比较 currentFrequency
                selectedTileColor: Theme.of(context).colorScheme.primaryContainer,
                onTap: () {
                  // 增加震动反馈
                  HapticFeedback.lightImpact();
                  // 直接回调并关闭弹窗
                  onFrequencySelected(frequency);
                  Navigator.pop(context);
                },
              );
            },
          ),
        );
      },
    );
  }
}
