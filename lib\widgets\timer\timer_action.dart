// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';

class TimerAction extends StatelessWidget {
  final ({bool disabled, String text}) status;
  final String? taskName;
  final VoidCallback onTap;
  final VoidCallback onEmptyTitle;

  const TimerAction({
    super.key,
    required this.status,
    this.taskName,
    required this.onTap,
    required this.onEmptyTitle,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          // 增加震动反馈
          HapticFeedback.lightImpact();
          if (!status.disabled) {
            _handleTimerAction(context);
          }
        },
        borderRadius: BorderRadius.circular(30),
        child: Ink(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(30),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          child: Row(
            children: [
              // 控制条中的提示文本
              Expanded(
                child: Text(
                  status.text,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              
              // 中间空白区域
              const SizedBox(width: 8),

              // 右侧操作图标
              Icon(
                !status.disabled ? LucideIcons.circlePlay : LucideIcons.ban,
                size: 28,
                color: Theme.of(context).colorScheme.primary,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleTimerAction(BuildContext context) {
    // 检查任务名称是否为空
    if (taskName!.isEmpty) {
      // 如果名称为空，调用回调函数
      onEmptyTitle();
      return;
    }
    
    // 标题不为空，调用计时器操作回调
    onTap();
  }
}
