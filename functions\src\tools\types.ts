import { Timestamp } from 'firebase-admin/firestore';

////////////////////////////////////////////////
/// Task
////////////////////////////////////////////////

export interface TaskModel {
  userId: string;
  createTime: Timestamp;
  updateTime: Timestamp;
  name: string;
  timezone: string;
  dueDate: number | null;
  dueTime: Timestamp | null;
  projectId: string | null;
  recurringId: string | null;
  isImportant: boolean | null;
  isUrgent: boolean | null;
  completeTime: Timestamp | null;
  dropTime: Timestamp | null;
}

////////////////////////////////////////////////
/// Recurring
////////////////////////////////////////////////

export interface RecurringModel {
  id: string; 
  userId: string;
  timezone: string;
  template: RecurringTemplate;
  rule: RecurringRule;
}

export interface RecurringRule {
  frequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
  interval: number;
  daysOfWeek: number[] | null;
  daysOfMonth: number[] | null;
  daysOfYear: { month: number; day: number }[] | null;
  startTime: Timestamp;
  endTime: Timestamp | null;
  repeatCount: number | null;
}
  
export interface RecurringTemplate {
  name: string;
  projectId: string | null;
  dueClock: Timestamp | null;
  isImportant: boolean | null;
  isUrgent: boolean | null;
}

////////////////////////////////////////////////
/// Subscription
////////////////////////////////////////////////

export enum SubscriptionPlan {
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
  LIFETIME = 'lifetime',
}

export enum SubscriptionStatus {
  TRIAL = 'trial',
  ACTIVE = 'active',
  EXPIRED = 'expired',
  GRACE_PERIOD = 'gracePeriod', // 宽限期
}

// 最终确认的数据模型
export interface UserSubscriptionData {
  plan: SubscriptionPlan | null;
  status: SubscriptionStatus | null;
  isRenewal: boolean | null;
  startTime: Timestamp | null;
  endTime: Timestamp | null;
  paymentSource: 'app_store' | 'play_store' | 'stripe' | 'unknown' | null;
  // 保留的平台特定交易ID
  storeTransactionId: string | null; // RevenueCat 的 original_transaction_id
  latestTransactionId: string | null; // RevenueCat 的 transaction_id
}

// RevenueCat Webhook Event 对象
export interface RevenueCatEvent {
  id: string; 
  type: 'INITIAL_PURCHASE' | 'RENEWAL' | 'CANCELLATION' | 'UNCANCELLATION' | 'SUBSCRIPTION_PAUSED' | 'EXPIRATION' | 'BILLING_ISSUE' | 'PRODUCT_CHANGE' | 'TRANSFER' | 'SUBSCRIBER_ALIAS' | 'TEST' | 'NON_RENEWING_PURCHASE';
  app_user_id: string; 
  original_app_user_id: string;
  product_id: string;
  entitlement_ids: string[] | null; 
  period_type: 'trial' | 'intro' | 'normal';
  purchased_at_ms: number;
  expiration_at_ms: number | null;
  store: 'APP_STORE' | 'PLAY_STORE' | 'STRIPE' | 'PROMOTIONAL' | 'UNKNOWN_STORE';
  will_renew?: boolean;
  is_trial_conversion?: boolean;
  transaction_id: string;
  original_transaction_id: string;
  subscriber_attributes?: {
    [key: string]: {
      value: string | null;
      updated_at_ms: number;
    }
  }
}

// RevenueCat Webhook 载荷
export interface RevenueCatWebhookPayload {
  api_version: string;
  event: RevenueCatEvent;
}

////////////////////////////////////////////////
/// CRUD
////////////////////////////////////////////////

export interface AuthProvider {
  // 这是 Firebase Auth UserRecord.providerData[].providerId 中使用的官方 ID
  // 除了 anonymous 和 unknown
  id: 'google.com' | 'apple.com' | 'password' | 'anonymous' | 'unknown';
  // 这是我们存储在 Firestore `users` 文档中的 authProvider 字段的简写形式
  name: 'google' | 'apple' | 'email' | 'anonymous' | 'unknown';
  // 是否为 OAuth 认证方式
  isOauth?: boolean;
  // 是否为匿名账户
  isAnonymous?: boolean;
  // 是否为兜底的认证方式
  isUnknown?: boolean;
}
