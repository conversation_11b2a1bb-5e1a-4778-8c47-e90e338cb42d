// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// models
import '../../models/task_model.dart';
// tools
import '../../tools/config.dart';
import '../../tools/bottom_slide_route.dart';
// pages
import '../../sheets/task/task_edit_page.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class DayTasksColumn extends StatelessWidget {
  final DateTime date;
  final DateTime selectedDate;
  final List<TaskModel> tasks;
  final ScrollController scrollController;
  final double timelineWidth;
  final double untimedHeight;
  final double hourHeight;
  final double dueTimeTaskHeight;
  final double minEventHeight;
  final Color taskColor;
  final DateTime currentTime;
  final bool showCurrentTimeLine;

  const DayTasksColumn({
    super.key,
    required this.date,
    required this.selectedDate,
    required this.tasks,
    required this.scrollController,
    required this.timelineWidth,
    required this.untimedHeight,
    required this.hourHeight,
    required this.dueTimeTaskHeight,
    required this.minEventHeight,
    required this.taskColor,
    required this.currentTime,
    this.showCurrentTimeLine = false,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    
    // --- 逻辑简化 ---
    final dueTimeTasks = tasks.where((t) => t.dueTime != null).toList();
    final untimedTasks = tasks.where((t) => t.dueDate != null && t.dueTime == null).toList();

    final allTimeOverlaps = _calculateAllTimeOverlaps(dueTimeTasks);

    return SingleChildScrollView(
      controller: scrollController,
      padding: EdgeInsets.only(bottom: Config.app.bottomSpace), // 底部留白，防止右下角的添加按钮遮挡内容
      child: SizedBox(
        height: 24 * hourHeight + untimedHeight,
        child: Stack(
          key: ValueKey('day-stack-${date.toString()}'),
          children: [
            if (untimedTasks.isNotEmpty)
              Positioned(
                top: 0,
                left: 4,
                right: 4,
                height: untimedHeight,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 4, bottom: 4, left: 2),
                        child: Text(
                          l10n.dayTasksColumnWidget_untimed,
                          style: TextStyle(fontSize: 12, color: Theme.of(context).colorScheme.onSurfaceVariant),
                        ),
                      ),
                      ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: untimedTasks.length,
                        itemBuilder: (context, index) {
                          final task = untimedTasks[index];
                          return GestureDetector(
                            onTap: () => Navigator.of(context).push(
                              BottomSlideRoute(page: TaskEditPage(task: task))
                            ),
                            child: Container(
                              margin: const EdgeInsets.only(bottom: 4.0),
                              padding: const EdgeInsets.all(4.0),
                              decoration: BoxDecoration(
                                color: taskColor.withValues(alpha: 0.7),
                                borderRadius: BorderRadius.circular(4.0),
                              ),
                              child: Text(
                                task.name,
                                style: TextStyle(fontSize: 12, color: Theme.of(context).colorScheme.onPrimary),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ...List.generate(25, (index) {
              return Positioned(
                top: untimedHeight + (index * hourHeight),
                left: 0,
                right: 0,
                child: Container(
                  height: 1,
                  color: Theme.of(context).colorScheme.outlineVariant,
                ),
              );
            }),
            ...dueTimeTasks.map((task) {
              final dueTime = task.dueTime!;
              final dueHour = dueTime.hour + (dueTime.minute / 60.0);
              final top = dueHour * hourHeight;
              final layoutInfo = allTimeOverlaps.firstWhere(
                (info) => info.task.id == task.id,
                orElse: () => TimeOverlapInfo(task: task, position: 0, total: 1),
              );
              final widthRatio = 1.0 / layoutInfo.total;
              final leftOffset = layoutInfo.position * widthRatio;
              final columnWidth = (MediaQuery.of(context).size.width - timelineWidth) / 3;

              return Positioned(
                // 保留 1px 的边距，使任务不会贴边
                top: untimedHeight + top + 1,
                left: columnWidth * leftOffset + 1,
                width: columnWidth * widthRatio - 2,
                height: dueTimeTaskHeight,
                child: GestureDetector(
                  onTap: () => Navigator.of(context).push(
                    BottomSlideRoute(page: TaskEditPage(task: task))
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      color: taskColor,
                      borderRadius: BorderRadius.circular(2.0),
                    ),
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4.0),
                        child: Text(
                          task.name,
                          style: TextStyle(color: Theme.of(context).colorScheme.onPrimary, fontSize: 9),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }),
            // 确保当前时间线正确显示 - 放在前景位置
            if (showCurrentTimeLine) 
              Positioned(
                top: untimedHeight + ((currentTime.hour + (currentTime.minute / 60)) * hourHeight),
                left: 0,
                right: 0,
                child: Row(
                  children: [
                    // 左侧小圆点
                    Container(
                      width: 8,
                      height: 8,
                      margin: const EdgeInsets.only(left: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.error,
                        shape: BoxShape.circle,
                      ),
                    ),
                    // 红线
                    Expanded(
                      child: Container(
                        height: 1.5,
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  List<TimeOverlapInfo> _calculateAllTimeOverlaps(
    List<TaskModel> dueTimeTasks
  ) {
    if (dueTimeTasks.isEmpty) return [];

    dueTimeTasks.sort((a, b) {
      return a.dueTime!.compareTo(b.dueTime!);
    });

    final List<TimeOverlapInfo> result = [];

    for (int i = 0; i < dueTimeTasks.length; i++) {
      final currentTask = dueTimeTasks[i];
      final overlaps = <TaskModel>[];
      overlaps.add(currentTask);

      for (int j = 0; j < dueTimeTasks.length; j++) {
        if (i == j) continue;
        final otherTask = dueTimeTasks[j];
        if (_checkDueTimesOverlap(currentTask, otherTask)) {
          overlaps.add(otherTask);
        }
      }

      int position = 0;
      final usedPositions = <int>{};

      for (final info in result) {
        if (overlaps.any((task) => task.id == info.task.id)) {
          usedPositions.add(info.position);
        }
      }

      while (usedPositions.contains(position)) {
        position++;
      }

      result.add(TimeOverlapInfo(
        task: currentTask,
        position: position,
        total: overlaps.length,
      ));
    }
    return result;
  }

  bool _checkDueTimesOverlap(TaskModel a, TaskModel b) {
    final aTime = a.dueTime!;
    final bTime = b.dueTime!;

    return aTime.year == bTime.year &&
           aTime.month == bTime.month &&
           aTime.day == bTime.day &&
           aTime.hour == bTime.hour &&
           aTime.minute == bTime.minute;
  }
}

class TimeOverlapInfo {
  final TaskModel task;
  final int position;
  final int total;

  TimeOverlapInfo({
    required this.task,
    required this.position,
    required this.total,
  });
}
