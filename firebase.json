{"firestore": {"rules": "./firestore.rules", "indexes": "./firestore.indexes.json"}, "hosting": {"public": "./hosting", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}, "emulators": {"auth": {"port": 9009, "host": "0.0.0.0"}, "firestore": {"port": 8008, "host": "0.0.0.0"}, "functions": {"port": 7007, "host": "0.0.0.0"}, "hosting": {"port": 6006, "host": "0.0.0.0"}, "ui": {"enabled": true, "port": 5005, "host": "127.0.0.1"}, "singleProjectMode": true}, "flutter": {"platforms": {"android": {"default": {"projectId": "taskive-4eec0", "appId": "1:387043819968:android:132e256ea756f89bbbc825", "fileOutput": "../mobile/android/app/google-services.json"}}, "dart": {"../mobile/lib/firebase_options.dart": {"projectId": "taskive-4eec0", "configurations": {"android": "1:387043819968:android:132e256ea756f89bbbc825", "ios": "1:387043819968:ios:34183980c3c3b902bbc825", "web": "1:387043819968:web:6673f0ec11e3a159bbc825"}}}}}, "functions": [{"source": "./functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" install", "npm --prefix \"$RESOURCE_DIR\" run build"]}]}