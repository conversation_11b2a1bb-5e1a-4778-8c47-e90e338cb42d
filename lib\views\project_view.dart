// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// states
import '../states/task_state.dart';
import '../states/project_state.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/date_grouped_tasks.dart';
// sheets
import '../sheets/project/project_edit_sheet.dart';
// others
import '../generated/l10n/app_localizations.dart';

class ProjectView extends StatelessWidget {
  final String projectId;

  const ProjectView({
    super.key,
    required this.projectId,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    // 监听 ProjectState 的变化
    final projectState = context.watch<ProjectState>();
    final project = projectState.getProjectById(projectId);

    // 如果项目不存在 (可能已被删除)，则显示一个占位符或返回
    if (project == null) {
      return Scaffold(
        body: Center(
          child: Text(l10n.projectView_projectNotFound),
        ),
      );
    }
    
    return Consumer<TaskState>(
      builder: (context, taskState, _) {
        // 获取项目任务并按日期从旧到新排序
        final projectTasks = taskState.getProjectTasks(project.id);
          
        final completedTasks = projectTasks.where((task) => task.isCompleted).length;
        final totalTasks = projectTasks.length;
        
        // 计算完成率
        final completionRate = totalTasks > 0 ? completedTasks / totalTasks : 0.0;
        
        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(project.name),
            actions: [
              // 编辑项目按钮
              IconButton(
                icon: const Icon(LucideIcons.penLine),
                onPressed: () {
                  // 增加震动反馈
                  HapticFeedback.lightImpact();
                  ProjectEditSheet.show(context, project: project);
                }
              ),
            ],
          ),
          body: CustomScrollView(
            slivers: [
              // 项目进度信息卡片 - 减小上下边距
              SliverToBoxAdapter(
                child: Card(
                  margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  child: Padding(
                    padding: const EdgeInsets.only(left: 8, right: 8, top: 10, bottom: 14),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              l10n.projectView_taskCompletedRate(completedTasks, totalTasks),
                              style: const TextStyle(fontSize: 14),
                            ),
                            const Spacer(),
                            if (project.isArchived)
                              Icon(
                                LucideIcons.archive,
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                                size: 16
                              ),
                          ],
                        ),

                        const SizedBox(height: 10),

                        // 进度条
                        LinearProgressIndicator(
                          value: completionRate,
                          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                          color: project.color,
                          minHeight: 6.0,
                          borderRadius: BorderRadius.circular(2.0),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              
              // 任务列表
              if (projectTasks.isEmpty)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(child: Text(l10n.projectView_emptyMessage)),
                )
              else
                DateGroupedTasks(tasks: projectTasks, descending: true),
            ],
          ),
        );
      },
    );
  }
}
