// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// states
import '../states/task_state.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/date_grouped_tasks.dart';
// others
import '../generated/l10n/app_localizations.dart';

class CompletedView extends StatelessWidget {
  const CompletedView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer<TaskState>(
      builder: (context, taskState, _) {
        // 获取所有已完成任务 (已结束已完成)
        final completedTasks = taskState.getCompletedTasks();
        
        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(l10n.completedView_title),
          ),
          body: CustomScrollView(
            slivers: [
              // 任务列表
              if (completedTasks.isEmpty)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(child: Text(l10n.completedView_emptyMessage)),
                )
              else
                DateGroupedTasks(tasks: completedTasks),
            ],
          ),
        );
      },
    );
  }
}
