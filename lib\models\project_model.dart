// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
// tools
import '../tools/explicit_value.dart';

/// ⚠️ 这段注释不要删除：
/// ⚠️ 如果 Models 字段有改动
/// ⚠️ 请提醒我同步更新 Cloud Functions 和 Firebase Rules

class ProjectModel extends Equatable {
  final String id;
  final String userId;
  final DateTime createTime;
  final DateTime updateTime;
  final String name;
  final Color color;
  final bool isArchived;
  
  const ProjectModel({
    required this.id,
    required this.userId,
    required this.createTime,
    required this.updateTime,
    required this.name,
    required this.color,
    this.isArchived = false,
  });

  ProjectModel copyWith({
    ExplicitValue<String>? id,
    ExplicitValue<String>? userId,
    ExplicitValue<DateTime>? createTime,
    ExplicitValue<DateTime>? updateTime,
    ExplicitValue<String>? name,
    ExplicitValue<Color>? color,
    ExplicitValue<bool>? isArchived,
  }) {
    return ProjectModel(
      id: id == null ? this.id : id.value,
      userId: userId == null ? this.userId : userId.value,
      createTime: createTime == null ? this.createTime : createTime.value,
      updateTime: updateTime == null ? this.updateTime : updateTime.value,
      name: name == null ? this.name : name.value,
      color: color == null ? this.color : color.value,
      isArchived: isArchived == null ? this.isArchived : isArchived.value,
    );
  }
  
  factory ProjectModel.fromDatabase(Map<String, dynamic> json) {
    return ProjectModel(
      id: json['id'] as String? ?? '',
      userId: json['userId'] as String? ?? '',
      createTime: (json['createTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updateTime: (json['updateTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      name: json['name'] as String? ?? 'Untitled Project',
      color: Color(json['color'] as int? ?? Colors.grey.toARGB32()),
      isArchived: json['isArchived'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toDatabase() {
    return {
      'name': name,
      'color': color.toARGB32(),
      'isArchived': isArchived,
    };
  }

  // Equatable 需要比较的属性
  @override
  List<Object?> get props => [
    id,
    userId,
    createTime,
    updateTime,
    name,
    color,
    isArchived,
  ];
}
