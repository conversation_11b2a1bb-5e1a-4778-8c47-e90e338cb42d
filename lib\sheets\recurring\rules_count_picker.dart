// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

typedef CountCallback = void Function(int? count); // 允许传入 null 以清除次数

// 周期规则重复次数选择器
class RulesCountPicker extends StatelessWidget {
  final int? currentRepeatCount; // 当前选中的重复次数
  final CountCallback onCountSelected;

  const RulesCountPicker({
    super.key,
    this.currentRepeatCount,
    required this.onCountSelected,
  });

  // 格式化显示的次数
  String _formatDisplayCount(AppLocalizations l10n) {
    if (currentRepeatCount == null) {
      return l10n.rulesCountPicker_unlimited; // 占位符
    }
    return '$currentRepeatCount ${l10n.rulesCountPicker_timesSuffix}';
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    // 使用 Column 包含标题和选择区域
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start, // 标题左对齐
      children: [
        // 标题
        Padding(
          padding: EdgeInsets.only(bottom: 12.0), // 标题和选择器之间的间距
          child: Text(
            l10n.rulesCountPicker_title,
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: colorScheme.onSurfaceVariant), // 标题样式
          ),
        ),
        // 可点击的选择区域
        InkWell(
          onTap: () => _showRulesCountSheet(context),
          child: Container(
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 使用 Expanded 让 Text 填充可用空间
                Expanded(
                  child: Text(
                    _formatDisplayCount(l10n),
                    style: TextStyle(
                      fontSize: 16,
                      color: colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center, // 文本居中
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // 图标保持在右侧
                Icon(LucideIcons.chevronsUpDown, size: 18, color: colorScheme.onSurfaceVariant),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 显示次数选择底部弹窗
  void _showRulesCountSheet(BuildContext context) {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return BaseBottomSheet(
          fullWidth: true,
          child: SizedBox(
            height: MediaQuery.of(context).size.height * 0.4,
            child: ListView.builder(
              itemCount: 100, // 提供 1 到 100 次选项 + 无限次
              itemBuilder: (context, index) {
                if (index == 0) {
                  // "无限次" 选项
                  return ListTile(
                    title: Center(
                      child: Text(
                        l10n.rulesCountPicker_unlimited,
                        style: TextStyle(color: colorScheme.onSurface),
                      ),
                    ),
                    dense: true,
                    selected: currentRepeatCount == null,
                    selectedTileColor: colorScheme.primaryContainer,
                    onTap: () {
                      // 增加震动反馈
                      HapticFeedback.lightImpact();
                      // 直接回调并关闭
                      onCountSelected(null);
                      Navigator.pop(context);
                    },
                  );
                }
                final count = index; // 次数从 1 开始
                return ListTile(
                  title: Center(
                    child: Text(
                      '$count',
                      style: TextStyle(color: colorScheme.onSurface),
                    ),
                  ),
                  dense: true,
                  selected: currentRepeatCount == count,
                  selectedTileColor: colorScheme.primaryContainer,
                  onTap: () {
                    // 增加震动反馈
                    HapticFeedback.lightImpact();
                    // 直接回调并关闭
                    onCountSelected(count);
                    Navigator.pop(context);
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }
}
