// import 'dart:developer' as developer;

/// ====== 包装类 ======

// 主要用于 Model copyWith 方法显式传值
// 如果我为某个字段传了值（即便是 null），就用我传的值。
// 如果我没有为某个字段传值，才用对象本身的对应属性值 (this.fieldName)。

/// ====== 定义 ======

// DataModel copyWith({
//   ExplicitValue<String>? title,
//   ExplicitValue<String?>? description,
// }) {
//   return DataModel(
//     name: name == null ? this.name : name.value,
//     description: description == null ? this.description : description.value,
//   );
// }

/// ====== 使用 ======

// final updatedData = data.copyWith(
//   title: ExplicitValue(title),
//   description: ExplicitValue(description),
// );

class ExplicitValue<T> {
  final T value;
  const ExplicitValue(this.value);
}
