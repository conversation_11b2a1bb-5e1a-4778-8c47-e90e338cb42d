// 导入周期任务相关函数
import * as recurringTrigger from './recurring/trigger.js';
import * as recurringSchedule from './recurring/schedule.js';
// 导入付费订阅相关函数
import * as subscriptionWebhook from './subscription/webhook.js';
// 导入用户管理相关函数
import * as user from "./crud/user.js";
// 导入任务管理相关函数
import * as task from "./crud/task.js";
// 导入计时管理相关函数
import * as timer from "./crud/timer.js";
// 导入项目管理相关函数
import * as project from "./crud/project.js";
// 导入周期任务管理相关函数
import * as recurring from "./crud/recurring.js";
////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////
// 导出周期任务触发函数
export const onRecurringCreate = recurringTrigger.onRecurringCreate;
export const onRecurringUpdate = recurringTrigger.onRecurringUpdate;
export const onRecurringSchedule = recurringSchedule.onRecurringSchedule;
// 导出付费订阅触发函数
export const onSubscriptionWebhook = subscriptionWebhook.onSubscriptionWebhook;
// 导出用户管理函数
export const onUserCreate = user.onUserCreate;
// 暂时兼容
export const recordSignIn = user.upgradeAccount;
// export const recordSignIn = user.recordSignIn;
export const upgradeAccount = user.upgradeAccount;
export const updateUsername = user.updateUsername;
export const deleteUser = user.deleteUser;
// 导出任务管理函数
export const deleteTask = task.deleteTask;
// 导出计时管理函数
export const deleteTimer = timer.deleteTaskTimer;
// 导出项目管理函数
export const deleteProjectOnly = project.deleteProjectOnly;
export const deleteProjectWithTasks = project.deleteProjectWithTasks;
// 导出周期任务管理函数
export const deleteRecurring = recurring.deleteRecurring;
//# sourceMappingURL=index.js.map