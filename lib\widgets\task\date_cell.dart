// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// models
import '../../models/task_model.dart';

class DateCell extends StatelessWidget {
  final DateTime date;
  final bool isToday;
  final bool isOutside; // 是否是当前显示月份之外的日期
  final List<TaskModel> tasks;

  const DateCell({
    super.key,
    required this.date,
    required this.isToday,
    required this.isOutside,
    required this.tasks,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    final displayTasksCount = 4; // 显示的任务数量

    // --- 确定日期文本的颜色和字体粗细 ---
    Color dateTextColor;
    FontWeight dateFontWeight = FontWeight.normal; // 默认普通字体

    if (isOutside) {
      // 如果是当前月份之外的日期，使用较暗的灰色
      dateTextColor = colorScheme.onSurfaceVariant.withValues(alpha: 0.5);
    } else if (isToday) {
      // 如果是今天，使用主题的主色
      dateTextColor = colorScheme.primary;
      dateFontWeight = FontWeight.bold; // 今天使用粗体
    } else {
      // 如果是当前月份内的普通日期，使用标准的表面文字颜色
      dateTextColor = colorScheme.onSurface;
    }

    // 使用 Material 包裹 InkWell 以便显示水波纹效果
    return Container(
      width: double.infinity, // 宽度填满父容器
      padding: const EdgeInsets.symmetric(vertical: 4.0), // 单元格的整体垂直内边距
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start, // 内容在垂直方向上顶部对齐
        children: [
          // 显示日期数字
          Text(
            date.day.toString(), // 显示几号
            style: TextStyle(
              fontSize: 14, // 日期字体大小
              fontWeight: dateFontWeight, // 日期字体粗细（今天会加粗）
              color: dateTextColor, // 日期字体颜色
            ),
          ),

          const SizedBox(height: 2), // 日期数字和任务列表之间的间距

          // 显示任务列表 - 统一样式，不区分完成状态
          if (tasks.isNotEmpty)
            Expanded(
              child: ListView.builder(
                itemCount: tasks.length > displayTasksCount ? displayTasksCount : tasks.length,
                itemBuilder: (context, index) {
                  final task = tasks[index];
                  return Container(
                    margin: const EdgeInsets.only(left: 1.0, right: 1.0, bottom: 3.0),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(2.0),
                    ),
                    child: Text(
                      task.name,
                      style: TextStyle(
                        fontSize: 12,
                        color: colorScheme.primary,
                      ),
                      maxLines: 1,
                    ),
                  );
                },
              ),
            ),
          
          // 任务数量指示器
          if (tasks.length > displayTasksCount)
            Text(
              '+${tasks.length - displayTasksCount}',
              style: TextStyle(
                fontSize: 12,
                color: colorScheme.onSurfaceVariant,
              ),
            ),
        ],
      ),
    );
  }
}
