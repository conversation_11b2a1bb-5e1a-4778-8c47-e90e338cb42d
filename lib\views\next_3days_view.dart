// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// states
import '../states/task_state.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/today_glance.dart';
import '../widgets/task/date_grouped_tasks.dart';
// others
import '../generated/l10n/app_localizations.dart';

class Next3DaysView extends StatelessWidget {
  const Next3DaysView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer<TaskState>(
      builder: (context, taskState, _) {
        // 获取3天任务
        final next3daysTasks = taskState.getNextDaysTasks(3);
        
        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(l10n.next3DaysView_title),
          ),
          body: CustomScrollView(
            slivers: [
              // 今日一览作为头部
              SliverToBoxAdapter(child: TodayGlance()),
              
              // 任务列表
              if (next3daysTasks.isEmpty)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(child: Text(l10n.next3DaysView_emptyMessage)),
                )
              else
                DateGroupedTasks(tasks: next3daysTasks),
            ],
          ),
        );
      },
    );
  }
}
