{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Yechi\\Code\\timeask-flutter\\android\\app\\.cxx\\RelWithDebInfo\\6y1j2235\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Yechi\\Code\\timeask-flutter\\android\\app\\.cxx\\RelWithDebInfo\\6y1j2235\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}