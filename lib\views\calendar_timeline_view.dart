// import 'dart:developer' as developer;
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:linked_scroll_controller/linked_scroll_controller.dart';
// models
import '../models/task_model.dart';
// states
import '../states/task_state.dart';
import '../states/setting_state.dart';
// tools
import '../tools/config.dart';
import '../tools/extensions.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/day_tasks_column.dart';
import '../widgets/task/hour_timeline.dart';
// others
import '../generated/l10n/app_localizations.dart';

class CalendarTimelineView extends StatefulWidget {
  const CalendarTimelineView({super.key});

  @override
  State<CalendarTimelineView> createState() => _CalendarTimelineViewState();
}

class _CalendarTimelineViewState extends State<CalendarTimelineView> {
  DateTime _selectedDate = DateTime.now();
  final double _timelineWidth = 40.0; // 时间轴的宽度
  final double _untimedHeight = 80.0; // 未规划任务区域的高度
  final double _hourHeight = 60.0; // 每小时的高度，统一管理
  final double _dueTimeTaskHeight = 10.0; // 截止时间任务的固定高度
  final double _minEventHeight = 20.0; // 事件的最小显示高度
  
  // 使用LinkedScrollControllerGroup替代手动同步逻辑
  late LinkedScrollControllerGroup _scrollControllerGroup;
  late ScrollController _timelineController;
  late List<ScrollController> _dayScrollControllers;
  
  final DateTime _currentTime = DateTime.now().toLocal(); // 当前时间，用于绘制时间线

  @override
  void initState() {
    super.initState();
    
    // 初始化链接滚动控制器组
    _scrollControllerGroup = LinkedScrollControllerGroup();
    _timelineController = _scrollControllerGroup.addAndGet();
    // 为3列日期创建链接的控制器
    _dayScrollControllers = List.generate(3, (_) => _scrollControllerGroup.addAndGet());
    
    // 在初始化后滚动到当前时间位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToCurrentTime();
      setState(() {});
    });
  }

  @override
  void dispose() {
    _timelineController.dispose();
    for (var controller in _dayScrollControllers) {
      controller.dispose();
    }
    super.dispose();
  }
  
  // 滚动到当前时间位置 - 修改为居中显示
  void _scrollToCurrentTime() {
    final now = DateTime.now();
    final hour = now.hour;
    final minute = now.minute;
    
    // 计算当前时间对应的位置
    final currentTimePosition = (hour + (minute / 60)) * _hourHeight;
    
    // 将当前时间位置居中显示（减去日历部分大约高度）
    final offsetY = max(0.0, currentTimePosition - 180.0);
    
    // 使用LinkedScrollControllerGroup，所有控制器会自动同步
    _scrollControllerGroup.jumpTo(offsetY);
  }

  @override
  Widget build(BuildContext context) {
    // 使用Consumer2同时监听SettingState和TaskState
    return Consumer2<SettingState, TaskState>(
      builder: (context, settingState, taskState, _) {
        // 获取用户设置的一周的开始日
        final weekStartsOnSunday = settingState.weekStartDay == 7;
        final startingDayOfWeek = weekStartsOnSunday ? 
            StartingDayOfWeek.sunday : StartingDayOfWeek.monday;
        final l10n = AppLocalizations.of(context)!;
            
        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(l10n.calendarTimelineView_title),
            actions: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedDate = DateTime.now();
                  });
                },
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.primary,
                ),
                child: Text(l10n.common_today),
              ),
            ],
          ),
          body: Column(
            children: [
              // 日历部分 - 使用TableCalendar替换原来的_buildDateHeader
              TableCalendar<TaskModel>(
                // 不显示日历头部（就是 titleText、formatButton、leftChevron、rightChevron 那一行）
                headerVisible: false,
                firstDay: Config.app.calendarFirstDay,
                lastDay: Config.app.calendarLastDay,
                focusedDay: _selectedDate,
                selectedDayPredicate: (day) => _selectedDate.isSameDay(day),
                calendarFormat: CalendarFormat.week, // 固定为周视图
                eventLoader: (day) => taskState.get1DayTasks(day),
                startingDayOfWeek: startingDayOfWeek,
                onDaySelected: (selectedDay, focusedDay) {
                  setState(() {
                    _selectedDate = selectedDay;
                  });
                },
                calendarStyle: CalendarStyle(
                  tablePadding: const EdgeInsets.symmetric(vertical: 10),
                  // margin 越大，数字圆圈越小
                  cellMargin: const EdgeInsets.all(8),
                  markersMaxCount: 1,
                  markerSize: 4,
                  markerMargin: const EdgeInsets.only(top: 7),
                  markerDecoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    shape: BoxShape.circle,
                  ),
                  todayDecoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  selectedDecoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    shape: BoxShape.circle,
                  ),
                  todayTextStyle: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                    fontSize: 13,
                  ),
                  selectedTextStyle: TextStyle(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                    fontSize: 13,
                  ),
                ),
              ),
              
              // 主体内容区域
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 左侧时间轴
                    HourTimeline(
                      scrollController: _timelineController,
                      timelineWidth: _timelineWidth,
                      untimedHeight: _untimedHeight,
                      hourHeight: _hourHeight,
                    ),
                    
                    // 右侧任务区域
                    Expanded(
                      child: _buildTaskArea(taskState),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 构建主任务显示区域（3天）
  Widget _buildTaskArea(TaskState taskState) {
    final yesterday = _selectedDate.subtract(const Duration(days: 1));
    final today = _selectedDate;
    final tomorrow = _selectedDate.add(const Duration(days: 1));

    // 添加key确保选择日期变更时重建
    return Row(
      key: ValueKey('task-area-${_selectedDate.toString()}'),
      children: [
        Expanded(
          child: DayTasksColumn(
            date: yesterday,
            selectedDate: _selectedDate,
            tasks: taskState.get1DayTasks(yesterday),
            scrollController: _dayScrollControllers[0],
            timelineWidth: _timelineWidth,
            untimedHeight: _untimedHeight,
            hourHeight: _hourHeight,
            dueTimeTaskHeight: _dueTimeTaskHeight,
            minEventHeight: _minEventHeight,
            taskColor: Theme.of(context).colorScheme.primary,
            currentTime: _currentTime,
            showCurrentTimeLine: false, // 昨天不显示当前时间线
          ),
        ),
        Expanded(
          child: DayTasksColumn(
            date: today,
            selectedDate: _selectedDate,
            tasks: taskState.get1DayTasks(today),
            scrollController: _dayScrollControllers[1],
            timelineWidth: _timelineWidth,
            untimedHeight: _untimedHeight,
            hourHeight: _hourHeight,
            dueTimeTaskHeight: _dueTimeTaskHeight,
            minEventHeight: _minEventHeight,
            taskColor: Theme.of(context).colorScheme.primary,
            currentTime: _currentTime,
            showCurrentTimeLine: true, // 关键变更：中间列始终显示当前时间线，不再只在今天显示
          ),
        ),
        Expanded(
          child: DayTasksColumn(
            date: tomorrow,
            selectedDate: _selectedDate,
            tasks: taskState.get1DayTasks(tomorrow),
            scrollController: _dayScrollControllers[2],
            timelineWidth: _timelineWidth,
            untimedHeight: _untimedHeight,
            hourHeight: _hourHeight,
            dueTimeTaskHeight: _dueTimeTaskHeight,
            minEventHeight: _minEventHeight,
            taskColor: Theme.of(context).colorScheme.primary,
            currentTime: _currentTime,
            showCurrentTimeLine: false, // 明天不显示当前时间线
          ),
        ),
      ],
    );
  }
}
