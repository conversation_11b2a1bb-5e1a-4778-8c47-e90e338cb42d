// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// models
import '../../models/project_model.dart';

class ProjectBadge extends StatelessWidget {
  final ProjectModel project;

  const ProjectBadge({
    super.key,
    required this.project,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 10,
          height: 10,
          decoration: BoxDecoration(
            color: project.color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 2),
        Flexible(
          child: Text(
            project.name,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontSize: 12,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }
}
