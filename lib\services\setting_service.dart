import 'dart:developer' as developer;
import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
// models
import '../models/setting_model.dart';
// tools
import '../tools/config.dart';

class SettingService {
  final FirebaseFirestore _db;

  /// ====== 私有状态 ======

  // Firestore 集合引用常量
  final String _usersCollection = Config.service.usersCollection;
  final String _settingsCollection = Config.service.settingsCollection;
  // Firestore 文档 ID 常量 (设置数据存储在单个文档中，这就是文档 id)
  final String _settingsDocId = Config.service.settingsDocId;

  // --- Firestore Key 常量 ---
  final String _navVerKey = Config.service.navVerKey;
  final String _statVerKey = Config.service.statVerKey;
  final String _weekStartKey = Config.service.weekStartKey;
  final String _showCompletedTasksKey = Config.service.showCompletedTasksKey;
  final String _showOverdueTasksKey = Config.service.showOverdueTasksKey;
  final String _showDroppedTasksKey = Config.service.showDroppedTasksKey;

  /// ====== 构造函数 ======
  
  SettingService(this._db);

  /// ====== 流监听 ======

  /// 获取用户设置的实时流
  Stream<SettingModel> getSettingStream(String userId) {
    if (userId.isEmpty) {
      return Stream.value(SettingModel.defaults);
    }
    final docRef = _getSettingDocRef(userId);

    // 监听文档快照
    return docRef.snapshots().map((snapshot) {
      try {
        // 使用 SettingModel.fromDatabase 处理文档存在/不存在及数据转换
        return SettingModel.fromDatabase(snapshot.data());
      } catch (e) {
        developer.log('⛔ 解析设置文档 ${snapshot.id} 失败', error: e);
        // 如果映射出错，返回默认设置
        return SettingModel.defaults;
      }
    });
  }

  /// ====== 网络请求 ======

  /// 保存单个设置项到 Firestore
  Future<void> _saveSetting(String userId, String key, dynamic value) async {
    final docRef = _getSettingDocRef(userId);

    // 使用 set 和 merge:true 来创建或更新文档中的特定字段
    await docRef.set({key: value}, SetOptions(merge: true));
  }

  // --- 具体设置的保存方法 ---

  Future<void> saveNavVer(String userId, List<String> nav) => _saveSetting(userId, _navVerKey, nav);

  Future<void> saveStatVer(String userId, List<String> stat) => _saveSetting(userId, _statVerKey, stat);

  Future<void> saveWeekStartDay(String userId, int day) => _saveSetting(userId, _weekStartKey, day);

  Future<void> saveShowCompletedTasks(String userId, bool show) => _saveSetting(userId, _showCompletedTasksKey, show);

  Future<void> saveShowOverdueTasks(String userId, bool show) => _saveSetting(userId, _showOverdueTasksKey, show);

  Future<void> saveShowDroppedTasks(String userId, bool show) => _saveSetting(userId, _showDroppedTasksKey, show);

  /// ====== 本地存储 ======

  // --- 主题偏好 ---

  Future<String?> getLocalStorageThemeCode() async {
    final prefs = await SharedPreferences.getInstance();

    return prefs.getString(Config.service.appThemeCodeKey);
  }

  Future<void> setLocalStorageThemeCode(String themeCode) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(Config.service.appThemeCodeKey, themeCode);
  }

  // --- 语言偏好 ---

  Future<String?> getLocalStorageLanguageCode() async {
    final prefs = await SharedPreferences.getInstance();

    return prefs.getString(Config.service.appLanguageCodeKey);
  }

  Future<void> setLocalStorageLanguageCode(String languageCode) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(Config.service.appLanguageCodeKey, languageCode);
  }

  /// ====== 工具方法 ======
  
  // 内部方法：获取该用户的设置文档引用
  DocumentReference<Map<String, dynamic>> _getSettingDocRef(String userId) {
    return _db.collection(_usersCollection).doc(userId).collection(_settingsCollection).doc(_settingsDocId);
  }
}
