// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';
// states
import '../../states/user_state.dart';
// pages
import '../common/sign_in_page.dart';
import './account_page.dart';
import './nav_setting_page.dart';
import './stat_setting_page.dart';
import './theme_setting_page.dart';
import './language_setting_page.dart';
import './date_time_setting_page.dart';
import './task_display_setting_page.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final userState = context.watch<UserState>();
    final subscription = userState.user?.subscription;
    final isPremium = subscription != null && subscription.isPremium;
    final isAnonymous = userState.isAnonymous;
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.settingsPage_title),
      ),
      body: Builder(
        builder: (context) {
          Widget userListTile;

          if (userState.status == UserAuthStatus.authenticating || userState.status == UserAuthStatus.refreshingSession) {
            userListTile = ListTile(
              leading: const SizedBox(
                width: 40,
                height: 40,
                child: Center(child: CircularProgressIndicator()),
              ),
              title: Text(l10n.common_loading),
            );
          } else if (isAnonymous || userState.status == UserAuthStatus.error) {
            // 用户未登录 (匿名或出错)
            userListTile = ListTile(
              leading: const Icon(LucideIcons.userRound),
              title: Text(l10n.settingsPage_notSignedIn, style: TextStyle(fontSize: 15)),
              subtitle: Text(l10n.settingsPage_notSignedInDesc, style: TextStyle(fontSize: 12)),
              trailing: const Icon(LucideIcons.chevronRight),
              onTap: () {
                // 增加震动反馈
                HapticFeedback.lightImpact();
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const SignInPage()),
                );
              },
            );
          } else {
            // 用户已登录
            userListTile = ListTile(
              leading: const Icon(LucideIcons.circleUserRound),
              title: Text(l10n.settingsPage_accountInfo, style: TextStyle(fontSize: 15)),
              subtitle: Text(l10n.settingsPage_accountInfoDesc, style: TextStyle(fontSize: 12)),
              trailing: const Icon(LucideIcons.chevronRight),
              onTap: () {
                // 增加震动反馈
                HapticFeedback.lightImpact();
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const AccountPage()),
                );
              },
            );
          }

          return ListTileTheme(
            horizontalTitleGap: 16,
            child: Column(
              children: [
                // 用户信息
                userListTile,

                if (!isPremium) ...[
                  // 分割线
                  const Divider(),

                  // Premium 订阅入口
                  ListTile(
                    leading: const Icon(LucideIcons.badgeCheck),
                    title: Text(l10n.settingsPage_upgradeToPremium, style: TextStyle(fontSize: 15)),
                    subtitle: Text(l10n.settingsPage_upgradeToPremiumDesc, style: TextStyle(fontSize: 12)),
                    trailing: const Icon(LucideIcons.chevronRight),
                    onTap: () async {
                      // 增加震动反馈
                      HapticFeedback.lightImpact();
                      try {
                        await RevenueCatUI.presentPaywall();
                      } catch (e) {
                        // 可选：处理异常，例如用户取消了付费墙
                      }
                    },
                  ),
                ],

                const Divider(),

                // 导航项自定义
                ListTile(
                  leading: const Icon(LucideIcons.navigation),
                  title: Text(l10n.settingsPage_nav, style: TextStyle(fontSize: 15)),
                  subtitle: Text(l10n.settingsPage_navDesc, style: TextStyle(fontSize: 12)),
                  trailing: const Icon(LucideIcons.chevronRight),
                  onTap: () {
                    // 增加震动反馈
                    HapticFeedback.lightImpact();
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const NavSettingPage()),
                    );
                  },
                ),

                // 统计图设置
                ListTile(
                  leading: const Icon(LucideIcons.chartLine),
                  title: Text(l10n.settingsPage_stat, style: TextStyle(fontSize: 15)),
                  subtitle: Text(l10n.settingsPage_statDesc, style: TextStyle(fontSize: 12)),
                  trailing: const Icon(LucideIcons.chevronRight),
                  onTap: () {
                    // 增加震动反馈
                    HapticFeedback.lightImpact();
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const StatSettingPage()),
                    );
                  },
                ),

                // 主题设置
                ListTile(
                  leading: const Icon(LucideIcons.palette),
                  title: Text(l10n.settingsPage_theme, style: TextStyle(fontSize: 15)),
                  subtitle: Text(l10n.settingsPage_themeDesc, style: TextStyle(fontSize: 12)),
                  trailing: const Icon(LucideIcons.chevronRight),
                  onTap: () {
                    // 增加震动反馈
                    HapticFeedback.lightImpact();
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const ThemeSettingPage()),
                    );
                  },
                ),

                // 语言设置
                ListTile(
                  leading: const Icon(LucideIcons.languages),
                  title: Text(l10n.settingsPage_language, style: TextStyle(fontSize: 15)),
                  subtitle: Text(l10n.settingsPage_languageDesc, style: TextStyle(fontSize: 12)),
                  trailing: const Icon(LucideIcons.chevronRight),
                  onTap: () {
                    // 增加震动反馈
                    HapticFeedback.lightImpact();
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const LanguageSettingPage()),
                    );
                  },
                ),

                // 日期和时间设置
                ListTile(
                  leading: const Icon(LucideIcons.calendarCog),
                  title: Text(l10n.settingsPage_dateTime, style: TextStyle(fontSize: 15)),
                  subtitle: Text(l10n.settingsPage_dateTimeDesc, style: TextStyle(fontSize: 12)),
                  trailing: const Icon(LucideIcons.chevronRight),
                  onTap: () {
                    // 增加震动反馈
                    HapticFeedback.lightImpact();
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const DateTimeSettingPage()),
                    );
                  },
                ),

                // 添加任务显示设置
                ListTile(
                  leading: const Icon(LucideIcons.eye),
                  title: Text(l10n.settingsPage_taskDisplay, style: TextStyle(fontSize: 15)),
                  subtitle: Text(l10n.settingsPage_taskDisplayDesc, style: TextStyle(fontSize: 12)),
                  trailing: const Icon(LucideIcons.chevronRight),
                  onTap: () {
                    // 增加震动反馈
                    HapticFeedback.lightImpact();
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const TaskDisplaySettingPage()),
                    );
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
