// import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
// models
import '../models/stat_model.dart';
import '../models/recurring_model.dart';
// states
import './recurring_state.dart';
import './setting_state.dart';
// services
import '../services/setting_service.dart';
// tools
import '../tools/app_exception.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class StatState extends ChangeNotifier {
  final RecurringState _recurringState;
  final SettingState _settingState;
  final SettingService _settingService;

  /// ====== 私有状态 ======

  List<String> _selectedStatIds = [];
  AppLocalizations? _l10n;

  /// ====== 构造函数 ======

  StatState(this._recurringState, this._settingState, this._settingService) {
    _settingState.addListener(_listenToSettingChanges);
    _recurringState.addListener(_listenToRecurringChanges);
  }

  /// ====== 公共状态 ======

  List<String> get currentStatIds => List.unmodifiable(_selectedStatIds);

  List<StatModel> get selectedStats {
    if (_l10n == null) return [];

    return _selectedStatIds
      .map((concatenatedId) {
        final stat = StatModel.fromConcatenatedId(concatenatedId, _recurringState);
        if (stat != null) {
          RecurringModel? recurring;
          if (stat.category == StatCategory.habit) {
            try {
              recurring = _recurringState.recurrings.firstWhere((r) => r.id == stat.baseId);
            } catch (e) {/* 找不到则忽略 */}
          }
          stat.rebuild(_l10n!, recurring: recurring);
        }
        return stat;
      })
      .where((stat) => stat != null) // 过滤掉可能由于ID无效或recurring已删除而导致的null
      .cast<StatModel>()
      .toList();
  }

  List<StatModel> get selectableStats {
    if (_l10n == null) return [];

    final List<StatModel> availableStats = [];

    // 添加标准可选图表
    for (final standardStat in StatModel.values) {
      if (!_selectedStatIds.contains(standardStat.id)) {
        standardStat.rebuild(_l10n!);
        availableStats.add(standardStat);
      }
    }

    // 添加习惯相关的可选图表
    for (final recurring in _recurringState.recurrings) {
      try {
        final habitTaskStat = StatModel.fromRecurring(recurring, StatDataType.task);
        if (!_selectedStatIds.contains(habitTaskStat.id)) {
          habitTaskStat.rebuild(_l10n!, recurring: recurring);
          availableStats.add(habitTaskStat);
        }
        final habitTimerStat = StatModel.fromRecurring(recurring, StatDataType.timer);
        if (!_selectedStatIds.contains(habitTimerStat.id)) {
          habitTimerStat.rebuild(_l10n!, recurring: recurring);
          availableStats.add(habitTimerStat);
        }
      } catch (e) {
        // 错误
      }
    }
    
    return availableStats;
  }

  /// ====== 流监听 ======

  void _listenToSettingChanges() {
    final newStatIds = _settingState.settings.statVer;
    final currentStatIds = newStatIds ?? [];
    load(currentStatIds);
  }

  void _listenToRecurringChanges() {
    final newStatIds = _settingState.settings.statVer;
    final currentStatIds = newStatIds ?? [];
    load(currentStatIds);
  }

  /// ====== 网络请求 ======

  void load(List<String> statIds) {
    final validSelectedIds = _filterAndValidateOrder(statIds);
    if (!listEquals(_selectedStatIds, validSelectedIds)) {
      _selectedStatIds = validSelectedIds;
      notifyListeners();
    }
  }

  Future<void> _saveStatVer() async {
    final userId = _settingState.currentUserId;
    final statIds = List<String>.from(_selectedStatIds);

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.updateSetting,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    await _settingService.saveStatVer(userId, statIds);

    notifyListeners();
  }

  /// ====== 工具方法 ======

  void rebuild(AppLocalizations l10n) {
    _l10n = l10n;
    // 这里不需要遍历重建，因为 getter 会在每次访问时都重建
    // 我们只需要更新 l10n 实例并通知监听器即可
    notifyListeners();
  }

  void add(int position, StatModel statToAdd) {
    if (_selectedStatIds.contains(statToAdd.id)) {
      return;
    }

    if (position < 0) position = 0;
    if (position > _selectedStatIds.length) position = _selectedStatIds.length;
    _selectedStatIds.insert(position, statToAdd.id);

    _saveStatVer();
  }

  void remove(String id) {
    final index = _selectedStatIds.indexOf(id);
    if (index != -1) {
      _selectedStatIds.removeAt(index);
      _saveStatVer();
    }
  }

  void reorder(List<String> newOrder) {
    final validNewOrder = _filterAndValidateOrder(newOrder);
    if (!listEquals(_selectedStatIds, validNewOrder)) {
      _selectedStatIds = validNewOrder;
      _saveStatVer();
    }
  }

  List<String> _filterAndValidateOrder(List<String> concatenatedIds) {
    final validIds = <String>[];
    final standardStatBaseIds = StatModel.values.map((s) => s.baseId).toSet();
    final habitBaseIds = _recurringState.recurrings.map((r) => r.id).toSet();

    for (final concatId in concatenatedIds) {
      final parts = concatId.split(':');
      if (parts.length != 2) continue; // 无效格式

      final baseId = parts[0];
      final dataTypeName = parts[1];
      StatDataType? dataType;
      try {
        dataType = StatDataType.values.firstWhere((e) => e.name == dataTypeName);
      } catch (e) {
        continue; // 无效dataType
      }

      // 检查是否是有效的标准图
      bool isStandardValid = false;
      if (standardStatBaseIds.contains(baseId)) {
        StatModel? standardModel;
        try {
          standardModel = StatModel.values.firstWhere((s) => s.baseId == baseId && s.dataType == dataType);
        } catch (e) {
          // 没找到
        }

        if (standardModel != null && standardModel.id == concatId) {
          isStandardValid = true;
        }
      }
      
      // 检查是否是有效的习惯图
      bool isHabitValid = false;
      if (habitBaseIds.contains(baseId) && 
          (dataType == StatDataType.task || dataType == StatDataType.timer)) {
         // 这里我们信任 baseId (recurringId) 存在于当前的 recurringState.recurrings 即可
         // 并且 dataType 是习惯图支持的类型
         isHabitValid = true;
      }

      if (isStandardValid || isHabitValid) {
        validIds.add(concatId);
      }
    }
    return validIds;
  }

  @override
  void dispose() {
    _settingState.removeListener(_listenToSettingChanges);
    _recurringState.removeListener(_listenToRecurringChanges);
    super.dispose();
  }
}
