// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// states
import '../states/task_state.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/task/date_grouped_tasks.dart';
// others
import '../generated/l10n/app_localizations.dart';

/// 已丢弃任务视图
class DroppedView extends StatelessWidget {
  const DroppedView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer<TaskState>(
      builder: (context, taskState, _) {
        // 获取所有已丢弃任务 (已结束已丢弃)
        final droppedTasks = taskState.getDroppedTasks();

        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(l10n.droppedView_title),
          ),
          body: CustomScrollView(
            slivers: [
              if (droppedTasks.isEmpty)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(child: Text(l10n.droppedView_emptyMessage)),
                )
              else
                DateGroupedTasks(tasks: droppedTasks),
            ],
          ),
        );
      }
    );
  }
}
