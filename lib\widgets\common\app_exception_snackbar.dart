// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// tools
import '../../tools/app_exception.dart';
import '../../tools/app_exception_mapper.dart';
// others
import '../../generated/l10n/app_localizations.dart';

void showAppExceptionSnackBar(BuildContext context, AppException exception) {
  final l10n = AppLocalizations.of(context)!;
  final String howText = appExceptionHowLocaleMapper(context, exception.how);
  final String whyText = appExceptionWhyLocaleMapper(context, exception.why);

  final message = l10n.appException_template(howText, whyText);
  
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Theme.of(context).colorScheme.error,
    ),
  );
}
