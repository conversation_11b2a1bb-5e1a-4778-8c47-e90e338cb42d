// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// states
import '../../states/user_state.dart';
// tools
import '../../tools/validators.dart';
import '../../tools/app_exception.dart';
import '../../tools/app_exception_mapper.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
import '../../widgets/common/app_exception_snackbar.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class ChangeUsernameSheet extends StatefulWidget {
  const ChangeUsernameSheet({super.key});

  @override
  State<ChangeUsernameSheet> createState() => _ChangeUsernameSheetState();
}

class _ChangeUsernameSheetState extends State<ChangeUsernameSheet> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _usernameController;
  String? _initialUsername;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    // 获取当前用户名作为初始值
    _initialUsername = Provider.of<UserState>(context, listen: false).user!.name;
    _usernameController = TextEditingController(text: _initialUsername);
  }

  @override
  void dispose() {
    _usernameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return BaseBottomSheet(
      title: l10n.changeUsernameSheet_title,
      onConfirm: _isLoading ? () {} : () => _saveUsername(context),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min, // 高度自适应内容
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: <Widget>[
            TextFormField(
              controller: _usernameController,
              readOnly: _isLoading,
              decoration: InputDecoration(
                labelText: l10n.changeUsernameSheet_label,
                prefixIcon: _isLoading
                  ? const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: SizedBox(
                        height: 18,
                        width: 18,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : const Icon(LucideIcons.userRound, size: 20),
                border: const OutlineInputBorder(),
              ),
              validator: (value) => Validators.validateUsername(value, l10n),
              autovalidateMode: AutovalidateMode.onUserInteraction,
              onFieldSubmitted: _isLoading ? null : (_) => _saveUsername(context),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveUsername(BuildContext context) async {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    if (!(_formKey.currentState?.validate() ?? false)) {
      return;
    }

    final newUsername = _usernameController.text.trim();
    // 检查用户名是否真的改变了
    if (newUsername == _initialUsername) {
      Navigator.pop(context); // 没有变化，直接关闭
      return;
    }

    if (!mounted) return;
    setState(() => _isLoading = true);

    final userState = Provider.of<UserState>(context, listen: false);
    final navigator = Navigator.of(context);

    try {
      await userState.updateUsername(newUsername);
      if (mounted) navigator.pop();
    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.updateUsername,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
