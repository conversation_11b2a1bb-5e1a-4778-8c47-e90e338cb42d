// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
// models
import '../../models/task_model.dart';
// states
import '../../states/task_state.dart';
// tools
import '../../tools/extensions.dart';
// widgets
import '../../widgets/task/date_grouped_tasks.dart';
// others
import '../../generated/l10n/app_localizations.dart';

// 月日历视图点击进去的任务列表页面
class DayTasksPage extends StatelessWidget {
  final DateTime selectedDate;
  final List<TaskModel> initialTasks;

  const DayTasksPage({
    super.key,
    required this.selectedDate,
    required this.initialTasks,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return Consumer<TaskState>(
      builder: (context, taskState, _) {
        final tasks = taskState.getAllTasksbySettings().where((task) {
          // 检查截止时间是否在当天
          return selectedDate.isSameDay(task.deadlineTime);
        }).toList();
        
        return Scaffold(
          appBar: AppBar(
            title: Text(DateFormat.yMMMMEEEEd(l10n.localeName).format(selectedDate)),
          ),
          body: CustomScrollView(
            slivers: [
              if (tasks.isEmpty)
                SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          l10n.calendarListView_emptyMessage,
                          style: TextStyle(color: colorScheme.onSurfaceVariant),
                        ),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                )
              else
                DateGroupedTasks(tasks: tasks),
            ],
          ),
        );
      },
    );
  }
}
