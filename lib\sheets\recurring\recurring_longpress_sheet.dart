// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/recurring_model.dart';
// states
import '../../states/recurring_state.dart';
// tools
import '../../tools/app_exception.dart';
import '../../tools/app_exception_mapper.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
import '../../widgets/common/base_alert_dialog.dart';
import '../../widgets/common/app_exception_snackbar.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class RecurringLongPressSheet extends StatefulWidget {
  final RecurringModel recurring;

  const RecurringLongPressSheet._({
    required this.recurring,
  });

  // 公共的静态 show 方法
  static void show(BuildContext context, RecurringModel recurring) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext bc) {
        return RecurringLongPressSheet._(recurring: recurring);
      },
    );
  }

  @override
  State<RecurringLongPressSheet> createState() => _RecurringLongPressSheetState();
}

class _RecurringLongPressSheetState extends State<RecurringLongPressSheet> {
  bool _isDeleting = false;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return BaseBottomSheet(
      fullWidth: true,
      child: Wrap(
        children: <Widget>[
          // 删除周期任务选项
          ListTile(
            leading: _isDeleting
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: colorScheme.error,
                  ),
                )
              : Icon(LucideIcons.trash2, color: colorScheme.error),
            title: Text(
              l10n.recurringLongPressSheet_delete,
              style: TextStyle(color: colorScheme.error),
            ),
            subtitle: Text(l10n.recurringLongPressSheet_deleteDesc),
            visualDensity: const VisualDensity(vertical: -4),
            onTap: _isDeleting ? null : () => _confirmDeleteRecurring(context),
          ),
        ],
      ),
    );
  }

  void _confirmDeleteRecurring(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    HapticFeedback.lightImpact();

    showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return BaseAlertDialog(
          title: l10n.recurringLongPressSheet_confirmDeleteTitle,
          content: l10n.recurringLongPressSheet_confirmDeleteContent(widget.recurring.template.name),
          actions: <Widget>[
            TextButton(
              child: Text(l10n.common_cancel),
              onPressed: () => Navigator.of(dialogContext).pop(false),
            ),
            TextButton(
              style: TextButton.styleFrom(foregroundColor: colorScheme.error),
              child: Text(l10n.common_confirmDelete),
              onPressed: () => Navigator.of(dialogContext).pop(true),
            ),
          ],
        );
      },
    ).then((confirmed) {
      if (confirmed == true) {
        _handleDeleteRecurring();
      }
    });
  }

  Future<void> _handleDeleteRecurring() async {
    if (!mounted) return;
    setState(() => _isDeleting = true);

    final recurringState = Provider.of<RecurringState>(context, listen: false);
    final navigator = Navigator.of(context);

    try {
      await recurringState.deleteRecurring(widget.recurring.id);
      if (mounted) navigator.pop();
    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.deleteRecurring,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    } finally {
      if (mounted) {
        setState(() => _isDeleting = false);
      }
    }
  }
}
