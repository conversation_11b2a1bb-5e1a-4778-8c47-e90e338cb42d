// import 'dart:developer' as developer;
// tools
import 'config.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class Validators {
  Validators._(); // 使其不能被实例化

  /// 验证用户名
  static String? validateUsername(String? value, AppLocalizations l10n) {
    final tv = value?.trim() ?? '';
    if (tv.isEmpty) {
      return l10n.changeUsernameSheet_validatorEmpty;
    }
    if (tv.length > Config.app.usernameMaxLength) {
      return l10n.changeUsernameSheet_validatorLength(Config.app.usernameMaxLength);
    }
    return null;
  }

  /// 验证任务名称
  static String? validateTaskName(String? value, AppLocalizations l10n) {
    final tv = value?.trim() ?? '';
    if (tv.isEmpty) {
      return l10n.taskEditPage_validatorEmpty;
    }
    if (tv.length > Config.app.taskNameMaxLength) {
      return l10n.taskEditPage_validatorLength(Config.app.taskNameMaxLength);
    }
    return null;
  }

  /// 验证项目名称
  static String? validateProjectName(String? value, AppLocalizations l10n) {
    final tv = value?.trim() ?? '';
    if (tv.isEmpty) {
      return l10n.projectEditSheet_validatorEmpty;
    }
    if (tv.length > Config.app.projectNameMaxLength) {
      return l10n.projectEditSheet_validatorLength(Config.app.projectNameMaxLength);
    }
    return null;
  }
}
