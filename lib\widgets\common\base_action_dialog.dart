// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// others
import '../../generated/l10n/app_localizations.dart';

/// 一个有状态的对话框，用于处理需要异步确认的操作。
/// 它在等待 Future 完成时，会在内部显示一个加载指示器。
class BaseActionDialog extends StatefulWidget {
  final String title;
  final String content;
  final String cancelText;
  final String confirmText;
  final Future<void> Function() onConfirm;

  const BaseActionDialog._({
    super.key,
    required this.title,
    required this.content,
    required this.cancelText,
    required this.confirmText,
    required this.onConfirm,
  });

  /// 静态方法，显示对话框的唯一途径
  static Future<dynamic> show(BuildContext context, {
    Key? key,
    required String title,
    required String content,
    required String cancelText,
    required String confirmText,
    required Future<void> Function() onConfirm,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false, // 不允许通过点击外部来关闭
      builder: (BuildContext context) {
        return BaseActionDialog._(
          key: key,
          title: title,
          content: content,
          cancelText: cancelText,
          confirmText: confirmText,
          onConfirm: onConfirm,
        );
      },
    );
  }

  @override
  State<BaseActionDialog> createState() => _BaseActionDialogState();
}

class _BaseActionDialogState extends State<BaseActionDialog> {
  bool _isLoading = false;

  void _handleConfirm() async {
    setState(() => _isLoading = true);

    try {
      await widget.onConfirm();
      if (mounted) Navigator.of(context).pop(true); // 成功
    } catch (e) {
      if (mounted) Navigator.of(context).pop(e); // 失败，并传递异常
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return PopScope(
      canPop: !_isLoading,
      child: AlertDialog(
        insetPadding: const EdgeInsets.all(12.0),
        titleTextStyle: Theme.of(context).textTheme.titleMedium,
        titlePadding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 16.0, bottom: 0.0),
        contentPadding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 16.0, bottom: 8.0),
        actionsPadding: const EdgeInsets.only(left: 4.0, right: 4.0, top: 0.0, bottom: 2.0),
        buttonPadding: EdgeInsets.zero,
        title: _isLoading ? null : Text(widget.title),
        content: _isLoading ? _loadingIndicator(context) : Text(widget.content),
        actions: _isLoading
          ? []
          : <Widget>[
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                },
                child: Text(widget.cancelText),
              ),
              TextButton(
                onPressed: _handleConfirm,
                child: Text(
                  widget.confirmText,
                  style: TextStyle(color: colorScheme.error),
                ),
              ),
            ],
      ),
    );
  }

  Widget _loadingIndicator(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Center(
      heightFactor: 4.0,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(
            width: 16.0,
            height: 16.0,
            child: CircularProgressIndicator(strokeWidth: 3.0),
          ),
          const SizedBox(width: 16),
          Text(
            l10n.common_processing,
            style: const TextStyle(fontSize: 16.0),
          ),
        ],
      ),
    );
  }
}
