// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/project_model.dart';
// states
import '../../states/project_state.dart';
// tools
import '../../tools/explicit_value.dart';
import '../../tools/app_exception.dart';
import '../../tools/app_exception_mapper.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
import '../../widgets/common/base_alert_dialog.dart';
import '../../widgets/common/app_exception_snackbar.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class ProjectLongPressSheet extends StatefulWidget {
  final ProjectModel project;

  const ProjectLongPressSheet._({
    required this.project,
  });

  // 公共的静态 show 方法
  static void show(BuildContext context, ProjectModel project) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(8.0),
        ),
      ),
      builder: (BuildContext bc) {
        return ProjectLongPressSheet._(project: project);
      },
    );
  }

  @override
  State<ProjectLongPressSheet> createState() => _ProjectLongPressSheetState();
}

class _ProjectLongPressSheetState extends State<ProjectLongPressSheet> {
  bool _isDeletingProjectOnly = false;
  bool _isDeletingWithTasks = false;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;
    final isArchived = widget.project.isArchived;
    final archiveIcon = isArchived ? LucideIcons.archiveX : LucideIcons.archive;
    final archiveText = isArchived ? l10n.projectLongPressSheet_unarchive : l10n.projectLongPressSheet_archive;
    final archiveSubtitle = isArchived ? l10n.projectLongPressSheet_unarchiveDesc : l10n.projectLongPressSheet_archiveDesc;
    final isLoading = _isDeletingProjectOnly || _isDeletingWithTasks;

    return BaseBottomSheet(
      fullWidth: true,
      child: Wrap(
        children: <Widget>[
          ListTile(
            leading: Icon(archiveIcon),
            title: Text(archiveText),
            subtitle: Text(archiveSubtitle),
            visualDensity: VisualDensity(vertical: -4),
            onTap: isLoading ? null : () => _handleArchiveProject(context),
          ),
          const Divider(),
          ListTile(
            leading: _isDeletingProjectOnly
              ? const SizedBox(
                  width: 22,
                  height: 22,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Icon(LucideIcons.trash2, color: colorScheme.tertiary),
            title: Text(l10n.projectLongPressSheet_deleteProject),
            subtitle: Text(l10n.projectLongPressSheet_deleteProjectDesc),
            visualDensity: VisualDensity(vertical: -4),
            onTap: isLoading ? null : () => _confirmDeleteProjectOnly(context),
          ),
          const Divider(),
          ListTile(
            leading: _isDeletingWithTasks
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: colorScheme.error,
                  ),
                )
              : Icon(LucideIcons.trash2, color: colorScheme.error),
            title: Text(
              l10n.projectLongPressSheet_deleteProjectAndTasks,
              style: TextStyle(color: colorScheme.error),
            ),
            subtitle: Text(l10n.projectLongPressSheet_deleteProjectAndTasksDesc),
            visualDensity: VisualDensity(vertical: -4),
            onTap: isLoading ? null : () => _confirmDeleteProjectAndTasks(context),
          ),
        ],
      ),
    );
  }

  // 更新归档/取消归档逻辑
  void _handleArchiveProject(BuildContext context) async {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final projectState = Provider.of<ProjectState>(context, listen: false);
    final navigator = Navigator.of(context);

    final updatedProject = widget.project.copyWith(
      isArchived: ExplicitValue(!widget.project.isArchived), // 切换归档状态
    );

    try {
      await projectState.updateProject(updatedProject);
      if (mounted) navigator.pop(); // 成功后关闭 bottom sheet
    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.updateProject,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    }
  }

  // 确认仅删除项目
  void _confirmDeleteProjectOnly(BuildContext context) {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final projectName = widget.project.name;
    final l10n = AppLocalizations.of(context)!;

    // 不再先关闭 BottomSheet
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return BaseAlertDialog(
          title: l10n.projectLongPressSheet_confirmDeleteTitle,
          content: l10n.projectLongPressSheet_confirmDeleteContent(projectName),
          actions: <Widget>[
            TextButton(
              child: Text(l10n.common_cancel),
              onPressed: () {
                Navigator.of(dialogContext).pop(); // 仅关闭对话框
              },
            ),
            TextButton(
              style: TextButton.styleFrom(foregroundColor: Theme.of(context).colorScheme.tertiary),
              child: Text(l10n.common_confirmDelete),
              onPressed: () {
                // 增加震动反馈
                HapticFeedback.lightImpact();
                Navigator.of(dialogContext).pop(); // 关闭对话框
                _handleDeleteProjectOnly(); // 然后执行删除
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleDeleteProjectOnly() async {
    // 异步操作前，先检查 widget 是否挂载
    if (!mounted) return;

    setState(() => _isDeletingProjectOnly = true);
    
    final projectState = Provider.of<ProjectState>(context, listen: false);
    final navigator = Navigator.of(context);

    try {
      await projectState.deleteProjectOnly(widget.project.id);
      // 成功后关闭 BottomSheet
      if (mounted) navigator.pop();
    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.deleteProject,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    } finally {
      if (mounted) {
        setState(() => _isDeletingProjectOnly = false);
      }
    }
  }

  // 确认删除项目及任务
  void _confirmDeleteProjectAndTasks(BuildContext context) {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final projectName = widget.project.name;
    final l10n = AppLocalizations.of(context)!;

    // 不再先关闭 BottomSheet
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return BaseAlertDialog(
          title: l10n.projectLongPressSheet_confirmDeleteWithTasksTitle,
          content: l10n.projectLongPressSheet_confirmDeleteWithTasksContent(projectName),
          actions: <Widget>[
            TextButton(
              child: Text(l10n.common_cancel),
              onPressed: () {
                Navigator.of(dialogContext).pop(); // 仅关闭对话框
              },
            ),
            TextButton(
              style: TextButton.styleFrom(foregroundColor: Theme.of(context).colorScheme.error),
              child: Text(l10n.common_confirmDelete),
              onPressed: () {
                // 增加震动反馈
                HapticFeedback.lightImpact();
                Navigator.of(dialogContext).pop(); // 关闭对话框
                _handleDeleteProjectAndTasks(); // 然后执行删除
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _handleDeleteProjectAndTasks() async {
    // 异步操作前，先检查 widget 是否挂载
    if (!mounted) return;
    setState(() => _isDeletingWithTasks = true);

    final projectState = Provider.of<ProjectState>(context, listen: false);
    final navigator = Navigator.of(context);

    try {
      await projectState.deleteProjectWithTasks(widget.project.id);
      // 成功后关闭 BottomSheet
      if (mounted) navigator.pop();
    } catch (error, stack) {
      if (mounted) {
        final appException = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.deleteProject,
          stack: stack,
        );
        showAppExceptionSnackBar(context, appException);
      }
    } finally {
      if (mounted) {
        setState(() => _isDeletingWithTasks = false);
      }
    }
  }
}
