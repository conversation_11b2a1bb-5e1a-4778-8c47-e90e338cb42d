// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

typedef StartDateCallback = void Function(DateTime date);

// 周期规则开始日期选择器
class RulesStartPicker extends StatelessWidget {
  final DateTime? currentStartDate; // 当前选中的开始日期
  final StartDateCallback onDateSelected;

  const RulesStartPicker({
    super.key,
    this.currentStartDate,
    required this.onDateSelected,
  });

  // 格式化显示的日期
  String _formatDisplayDate(AppLocalizations l10n) {
    if (currentStartDate == null) {
      return l10n.rulesStartPicker_placeholder; // 占位符
    }
    // 使用本地化的日期格式
    return DateFormat.yMd(l10n.localeName).format(currentStartDate!);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: const EdgeInsets.only(bottom: 12.0),
          child: Text(
            l10n.rulesStartPicker_title,
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: colorScheme.onSurfaceVariant),
          ),
        ),
        // 可点击的选择区域
        InkWell(
          onTap: () => _showRulesStartSheet(context),
          child: Container(
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _formatDisplayDate(l10n),
                    style: TextStyle(
                      fontSize: 16,
                      color: currentStartDate == null ? colorScheme.onSurfaceVariant : colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center, // 文本居中
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Icon(LucideIcons.chevronsUpDown, size: 20, color: colorScheme.onSurfaceVariant),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 显示日期选择器底部弹窗
  void _showRulesStartSheet(BuildContext context) {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final DateTime today = DateTime.now();

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return BaseBottomSheet(
          fullWidth: true,
          child: CalendarDatePicker(
            initialDate: currentStartDate ?? today,
            firstDate: today, // 只能选择今天及以后的日期
            lastDate: DateTime(today.year + 100),
            onDateChanged: (newDate) {
              onDateSelected(newDate);
              Navigator.pop(context);
            },
          ),
        );
      },
    );
  }
}
