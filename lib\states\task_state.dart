// import 'dart:developer' as developer;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
// models
import '../models/task_model.dart';
import '../models/user_model.dart';
// states
import './setting_state.dart';
// services
import '../services/task_service.dart';
// tools
import '../tools/app_exception.dart';
import '../tools/extensions.dart';
import '../tools/app_exception_mapper.dart';

class TaskState extends ChangeNotifier {
  final TaskService _taskService;

  /// ====== 私有状态 ======

  StreamSubscription<List<TaskModel>>? _tasksSubscription;
  List<TaskModel> _tasks = [];
  UserModel? _currentUser;
  SettingState? _settingState;
  AppException? _streamError;

  // --- 搜索用数据 ---
  List<TaskModel>? _searchDataSource; // 存储获取到的全年任务作为搜索数据源
  String? _searchDataError; // 加载搜索数据时的错误

  /// ====== 构造函数 ======

  TaskState(this._taskService);

  /// ====== 公共状态 ======

  // 获取所有任务的只读列表 (默认100条)
  List<TaskModel> get tasks => List.unmodifiable(_tasks);

  AppException? get streamError => _streamError;

  // --- 搜索用 Getters ---
  List<TaskModel>? get searchDataSource => _searchDataSource != null ? List.unmodifiable(_searchDataSource!) : null;
  String? get searchDataError => _searchDataError;

  /// ====== 流监听 ======

  /// 当 UserState 或 SettingState 发生变化时，由 ProxyProvider 调用
  void listenToDependencyChanges(UserModel? user, SettingState settingState) {
    final oldUserId = _currentUser?.id;
    final newUserId = user?.id;

    // 只有当用户ID实际发生变化时才执行操作
    if (oldUserId == newUserId) return;

    // 关键修复：为防止竞态条件，必须在切换用户时，同步地清理旧状态。
    //
    // 场景：当用户从 A 切换到 B 时，TaskState 等下游 State 可能会在 ProjectState
    // 完成数据更新前，就读取到旧的、属于用户 A 的项目列表，但此时 Auth 凭据
    // 已经是用户 B 的了，从而导致权限错误。
    //
    // 解决方案：通过立即清空 projects 列表并通知监听者，我们为所有下游 State
    // 提供了一个确定的、安全的“空”状态，强制它们先进入休整，然后再从新数据开始。
    // 这确保了状态更新的原子性，牺牲一次UI闪烁换取了架构的绝对稳定。
    _tasksSubscription?.cancel();
    _tasksSubscription = null;
    _currentUser = user;
    _settingState = settingState;
    _tasks = [];
    _streamError = null;
    _searchDataSource = null;
    _searchDataError = null;
    notifyListeners();

    // 只有当用户ID不为空时才监听
    if (newUserId != null) {
      _listenToTaskChanges(newUserId);
    }
  }

  // 初始化方法：接收 userId 并开始监听任务流
  void _listenToTaskChanges(String userId) {
    // 如果 userId 无效，则不监听
    if (userId.isEmpty) return;
    
    _tasksSubscription?.cancel(); // 取消之前的任务订阅（如果存在）
    _tasksSubscription = _taskService.getTaskStream(userId).listen(
      (tasksFromStream) {
        _tasks = tasksFromStream; // 直接用流中的最新数据覆盖本地列表
        _streamError = null;
        notifyListeners(); // 通知 UI 更新
      },
      onError: (error, stack) {
        // 处理流错误
        _streamError = appExceptionMapper(
          error: error as Exception,
          how: AppExceptionHow.loadTask,
          stack: stack,
        );
        notifyListeners();
      },
    );
  }

  /// ====== 网络请求 ======

  // 创建任务
  Future<TaskModel?> createTask(TaskModel task) async {
    final userId = _currentUser?.id;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.createTask,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    return await _taskService.createTask(userId, task);
  }

  // 更新任务
  Future<void> updateTask(TaskModel task) async {
    final userId = _currentUser?.id;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.updateTask,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (task.userId != userId) {
      throw AppException(
        how: AppExceptionHow.updateTask,
        why: AppExceptionWhy.permissionDenied,
      );
    }

    if (task.id.isEmpty) return;

    await _taskService.updateTask(userId, task);
  }

  // 删除任务
  Future<void> deleteTask(String taskId) async {
    final userId = _currentUser?.id;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.deleteTask,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    if (taskId.isEmpty) return;

    await _taskService.deleteTask(taskId);
  }

  // 搜索任务
  // 实际上是获取最近 365 天的任务供本地过滤
  Future<void> searchTasksFake() async {
    // 如果数据已加载，则不执行
    if (_searchDataSource != null) {
      return;
    }

    final userId = _currentUser?.id;

    if (userId == null || userId.isEmpty) {
      return;
    }

    try {
      _searchDataSource = await _taskService.searchTasksFake(userId);
    } catch (e) {
      _searchDataError = 'Load search data failed: ${e.toString()}';
      _searchDataSource = null; // 加载失败清空
    } finally {
      notifyListeners(); // 通知 UI 加载完成或失败 (这会触发 filteredTasks 重新计算)
    }
  }

  /// ====== 工具方法 ======
  
  // 添加此方法以响应设置更改（设置页面会用到）
  void refreshTasks() => notifyListeners();

  // --- 任务筛选 ---

  // 过滤任务列表，应用当前设置的筛选条件
  List<TaskModel> _filterTasksbySettings(List<TaskModel> tasks) {
    if (_settingState == null) return tasks; // 如果 setting state 未注入，则不筛选

    return tasks.where((task) {
      // 如果设置不显示已逾期任务，则过滤掉已逾期的任务
      if (!_settingState!.showOverdueTasks && task.isOverdue) {
        return false;
      }

      // 如果设置不显示已完成任务，则过滤掉已完成的任务
      if (!_settingState!.showCompletedTasks && task.isCompleted) {
        return false;
      }

      // 如果设置不显示已丢弃任务，则过滤掉已丢弃的任务
      if (!_settingState!.showDroppedTasks && task.isDropped) {
        return false;
      }

      return true;
    }).toList();
  }

  // 任务默认排序方法
  List<TaskModel> sortTasksByDefault(List<TaskModel> tasks, {bool descending = false}) {
    // 创建副本以避免修改原始列表
    final sortedTasks = List<TaskModel>.from(tasks);
    
    sortedTasks.sort((a, b) {
      final Timestamp? sortA = a.deadlineIndex;
      final Timestamp? sortB = b.deadlineIndex;
      
      // 无日期的排在后面
      if (sortA == null && sortB == null) return 0;
      if (sortA == null) return 1;
      if (sortB == null) return -1;
      
      // 按 deadlineIndex 排序
      return descending ? sortB.compareTo(sortA) : sortA.compareTo(sortB);
    });
    
    return sortedTasks;
  }

  // 根据ID获取任务
  TaskModel? getTaskById(String taskId) {
    try {
      return _tasks.firstWhere((task) => task.id == taskId);
    } catch (e) {
      return null; // 如果找不到则返回 null
    }
  }

  // 获取所有任务 - 不受设置影响
  List<TaskModel> getAllTasks() {
    // 按默认方式排序
    return sortTasksByDefault(_tasks);
  }

  // 获取所有任务 - 受设置影响
  List<TaskModel> getAllTasksbySettings() {
    // 根据设置筛选任务
    final tasks = _filterTasksbySettings(_tasks);

    // 按默认方式排序
    return sortTasksByDefault(tasks);
  }

  // 获取特定日期的任务 - 受设置影响
  List<TaskModel> getNextDaysTasks(int days) {
    // 根据设置筛选任务
    final tasks = _filterTasksbySettings(_tasks);

    // 筛选指定日期范围内的任务
    final now = DateTime.now();
    final todayStart = now.startOfDay;
    final daysStart = now.add(Duration(days: days)).startOfDay;

    final nextDaysTasks = tasks.where((task) {
      final deadline = task.deadlineTime;
      // 检查截止时间
      if (deadline != null) {
        return !deadline.isBefore(todayStart) && deadline.isBefore(daysStart);
      }
      return false;
    }).toList();
    
    // 按默认方式排序
    return sortTasksByDefault(nextDaysTasks);
  }

  // 获取特定日期的任务 - 受设置影响
  List<TaskModel> get1DayTasks(DateTime date) {
    // 根据设置筛选任务
    final tasks = _filterTasksbySettings(_tasks);

    // 筛选指定日期的任务 (全天任务或时刻任务)
    final dayTasks = tasks.where((task) {
      // 检查截止时间是否在当天
      if (task.deadlineTime != null) {
        return date.isSameDay(task.deadlineTime!);
      }
      return false;
    }).toList();
    
    // 按默认方式排序
    return sortTasksByDefault(dayTasks);
  }

  // 获取收件箱任务 - 受设置影响
  List<TaskModel> getInboxTasks() {
    // 根据设置筛选任务
    final tasks = _filterTasksbySettings(_tasks);

    // 筛选没有截止日期/时间且没有项目的任务
    final inboxTasks = tasks.where((task) => 
      task.hasNoDue && task.projectId == null
    ).toList();
    
    // 按默认方式排序
    return sortTasksByDefault(inboxTasks);
  }

  // 获取待办任务 (未结束未逾期) - 不受设置影响
  List<TaskModel> getTodoTasks() {
    // 筛选指定类型的任务
    final todoTasks = _tasks.where((task) => task.isTodo).toList();

    // 按默认方式排序
    return sortTasksByDefault(todoTasks);
  }

  // 获取逾期任务 (未结束已逾期) - 不受设置影响
  List<TaskModel> getOverdueTasks() {
    // 筛选指定类型的任务
    final overDueTasks = _tasks.where((task) => task.isOverdue).toList();

    // 按默认方式排序
    return sortTasksByDefault(overDueTasks);
  }

  // 获取已完成任务 (已结束已完成) - 不受设置影响
  List<TaskModel> getCompletedTasks() {
    // 筛选指定类型的任务
    final completedTasks = _tasks.where((task) => task.isCompleted).toList();

    // 按默认方式排序
    return sortTasksByDefault(completedTasks);
  }
  
  // 获取已丢弃任务 (已结束已丢弃) - 不受设置影响
  List<TaskModel> getDroppedTasks() {
    // 筛选指定类型的任务
    final droppedTasks = _tasks.where((task) => task.isDropped).toList();

    // 按默认方式排序
    return sortTasksByDefault(droppedTasks);
  }

  // 获取重要且紧急任务 - 受设置影响
  List<TaskModel> getImportantUrgentTasks() {
    // 根据设置筛选任务
    final tasks = _filterTasksbySettings(_tasks);

    // 筛选指定类型的任务
    final importantUrgentTasks = tasks.where(
      (task) => task.isImportant && task.isUrgent
    ).toList();

    // 按默认方式排序
    return sortTasksByDefault(importantUrgentTasks);
  }

  // 获取重要不紧急任务 - 受设置影响
  List<TaskModel> getImportantNotUrgentTasks() {
    // 根据设置筛选任务
    final tasks = _filterTasksbySettings(_tasks);

    // 筛选指定类型的任务
    final importantNotUrgentTasks = tasks.where(
      (task) => task.isImportant && !task.isUrgent
    ).toList();

    // 按默认方式排序
    return sortTasksByDefault(importantNotUrgentTasks);
  }

  // 获取紧急不重要任务 - 受设置影响
  List<TaskModel> getNotImportantUrgentTasks() {
    // 根据设置筛选任务
    final tasks = _filterTasksbySettings(_tasks);

    // 筛选指定类型的任务
    final notImportantUrgent = tasks.where(
      (task) => !task.isImportant && task.isUrgent
    ).toList();

    // 按默认方式排序
    return sortTasksByDefault(notImportantUrgent);
  }

  // 获取不重要不紧急任务 - 受设置影响
  List<TaskModel> getNotImportantNotUrgentTasks() {
    // 根据设置筛选任务
    final tasks = _filterTasksbySettings(_tasks);

    // 筛选指定类型的任务
    final notImportantNotUrgent = tasks.where(
      (task) => !task.isImportant && !task.isUrgent
    ).toList();

    // 按默认方式排序
    return sortTasksByDefault(notImportantNotUrgent);
  }

  // 根据 recurringId 获取任务列表
  List<TaskModel> getRecurringTasks(String recurringId) {
    // 根据设置筛选任务
    final tasks = _filterTasksbySettings(_tasks);

    final recurringTasks = tasks.where((task) => task.recurringId == recurringId).toList();

    return sortTasksByDefault(recurringTasks);
  }

  // 根据 projectId 获取任务列表
  List<TaskModel> getProjectTasks(String projectId) {
    // 根据设置筛选任务
    final tasks = _filterTasksbySettings(_tasks);

    final projectTasks = tasks.where((task) => task.projectId == projectId).toList();

    return sortTasksByDefault(projectTasks);
  }

  // --- 任务统计 ---
  
  // 获取今日待办任务数量
  int getTodayTodoCount() {
    final today = DateTime.now();
    
    return _tasks.where((task) {
      if (task.isCompleted || task.isDropped) return false;
      // 检查截止时间是否在当天
      if (task.deadlineTime != null) {
        return today.isSameDay(task.deadlineTime!);
      }
      return false;
    }).length;
  }
  
  // 获取已逾期任务数量
  int getOverdueTasksCount() {
    return _tasks.where((task) => task.isOverdue).length;
  }
  
  // 获取本周完成的任务数量
  int getWeekCompletedCount() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    // 修正：计算本周开始（周一）和结束（周日）
    final startOfWeek = today.subtract(Duration(days: today.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 7));

    return _tasks.where((task) =>
      task.isCompleted &&
      task.completeTime != null &&
      // 确保完成时间在本周范围内（含开始，不含结束）
      !task.completeTime!.isBefore(startOfWeek) &&
      task.completeTime!.isBefore(endOfWeek)
    ).length;
  }
  
  // 获取项目任务完成率
  double getProjectCompletionRate(String projectId) {
    final projectTasks = getProjectTasks(projectId);
    if (projectTasks.isEmpty) return 0.0;
    
    final completedCount = projectTasks.where((task) => task.isCompleted).length;
    return completedCount / projectTasks.length;
  }

  // 清理资源：取消所有流订阅
  @override
  void dispose() {
    _tasksSubscription?.cancel();
    super.dispose();
  }
}
