import 'dart:developer' as developer;
import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
// models
import '../models/task_model.dart';
// tools
import '../tools/config.dart';
import '../tools/explicit_value.dart';

class TaskService {
  final FirebaseFirestore _firestore;
  final FirebaseFunctions _functions;

  /// ====== 私有状态 ======

  // Firestore Collection 常量
  final String _usersCollection = Config.service.usersCollection;
  final String _tasksCollection = Config.service.tasksCollection;

  /// ====== 构造函数 ======
  
  TaskService(this._firestore, this._functions);

  /// ====== 流监听 ======

  /// 获取任务的实时流
  Stream<List<TaskModel>> getTaskStream(String userId) {
    if (userId.isEmpty) return Stream.value([]);

    final collectionRef = _getTaskCollectionRef(userId);

    // 恢复: 查询最新的 100 个任务，按创建时间降序排列
    return collectionRef.orderBy('createTime', descending: true).limit(100).snapshots().map((snapshot) {
      if (snapshot.docs.isEmpty) {
        return [];
      }
      // 将 Firestore 文档映射回 TaskModel 列表
      return snapshot.docs.map((doc) {
        try {
          final data = doc.data();
          data['id'] = doc.id; // 手动添加文档 ID
          return TaskModel.fromDatabase(data);
        } catch (e) {
          developer.log('⛔ 解析任务文档 ${doc.id} 失败', error: e);
          return null; // 解析失败则返回 null
        }
      }).whereType<TaskModel>().toList(); // 过滤掉所有 null
    });
  }

  /// ====== 网络请求 ======

  /// 创建新任务（客户端直接写入以提高速度）
  Future<TaskModel?> createTask(String userId, TaskModel task) async {
    final collectionRef = _getTaskCollectionRef(userId);

    final data = task.toDatabase();

    data['userId'] = userId;
    data['createTime'] = FieldValue.serverTimestamp();
    data['updateTime'] = FieldValue.serverTimestamp();
    
    final docRef = await collectionRef.add(data);
    
    if (docRef.id.isNotEmpty) {
      return task.copyWith(
        id: ExplicitValue(docRef.id),
        userId: ExplicitValue(userId),
        // 虽然我们发送的是 serverTimestamp，但在UI上立即反映一个近似时间
        // 真实时间会在下次从 Firestore 读取时同步
        createTime: ExplicitValue(DateTime.now()),
        updateTime: ExplicitValue(DateTime.now()),
      );
    }

    return null;
  }

  /// 更新任务（客户端直接写入以提高速度）
  Future<void> updateTask(String userId, TaskModel task) async {
    final docRef = _getTaskCollectionRef(userId).doc(task.id);

    final data = task.toDatabase();

    data['updateTime'] = FieldValue.serverTimestamp();

    await docRef.update(data);
  }

  /// 删除任务及其关联的所有计时器（保留在云函数以保证事务性）
  Future<void> deleteTask(String taskId) async {
    final callable = _functions.httpsCallable(Config.service.deleteTaskFn);
      
    await callable.call({'taskId': taskId});
  }

  // --- 本地搜索 ---

  /// 搜索任务（假的）
  /// 实际上是获取最近 365 天的任务供本地过滤
  Future<List<TaskModel>> searchTasksFake(String userId) async {
    // 如果 userId 无效
    if (userId.isEmpty) return [];

    final collectionRef = _getTaskCollectionRef(userId);
    // 计算 365 天前的日期
    final DateTime oneYearAgo = DateTime.now().subtract(const Duration(days: 365));
    final Timestamp oneYearAgoTimestamp = Timestamp.fromDate(oneYearAgo);

    // 查询 createTime 大于等于一年前的任务
    // 需要 createTime 索引
    final querySnapshot = await collectionRef
      .where('createTime', isGreaterThanOrEqualTo: oneYearAgoTimestamp)
      .get();

    if (querySnapshot.docs.isEmpty) return [];

    // 将结果映射回 TaskModel
    return querySnapshot.docs.map((doc) {
      final data = doc.data();
      data['id'] = doc.id;
      return TaskModel.fromDatabase(data);
    }).toList();
  }

  /// ====== 工具方法 ======

  // 内部方法：获取具体用户的任务子集合引用
  CollectionReference<Map<String, dynamic>> _getTaskCollectionRef(String userId) {
    return _firestore.collection(_usersCollection).doc(userId).collection(_tasksCollection);
  }
}
