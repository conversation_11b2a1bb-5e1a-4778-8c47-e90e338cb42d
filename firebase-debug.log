[debug] [2025-07-31T15:29:27.906Z] ----------------------------------------------------------------------
[debug] [2025-07-31T15:29:27.907Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js emulators:start
[debug] [2025-07-31T15:29:27.908Z] CLI Version:   14.11.0
[debug] [2025-07-31T15:29:27.908Z] Platform:      win32
[debug] [2025-07-31T15:29:27.908Z] Node Version:  v22.14.0
[debug] [2025-07-31T15:29:27.908Z] Time:          Thu Jul 31 2025 22:29:27 GMT+0700 (中南半岛时间)
[debug] [2025-07-31T15:29:27.908Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-31T15:29:27.910Z] >>> [apiv2][query] GET https://firebase-public.firebaseio.com/cli.json [none]
[debug] [2025-07-31T15:29:28.082Z] Object "" in "firebase.json" has unknown property: {"additionalProperty":"flutter"}
[debug] [2025-07-31T15:29:28.086Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-31T15:29:28.286Z] Running auto auth
[debug] [2025-07-31T15:29:28.565Z] openjdk version "21.0.6" 2025-01-21 LTS

[debug] [2025-07-31T15:29:28.566Z] OpenJDK Runtime Environment Temurin-21.0.6+7 (build 21.0.6+7-LTS)
OpenJDK 64-Bit Server VM Temurin-21.0.6+7 (build 21.0.6+7-LTS, mixed mode, sharing)

[debug] [2025-07-31T15:29:28.608Z] Parsed Java major version: 21
[info] i  emulators: Starting emulators: auth, functions, firestore, hosting, extensions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: auth, functions, firestore, hosting, extensions"}}
[debug] [2025-07-31T15:29:28.609Z] No OAuth tokens found
[debug] [2025-07-31T15:29:28.609Z] >>> [apiv2][query] GET https://cloudresourcemanager.googleapis.com/v1/projects/taskive-4eec0 [none]
[debug] [2025-07-31T15:29:28.751Z] <<< [apiv2][status] GET https://firebase-public.firebaseio.com/cli.json 200
[debug] [2025-07-31T15:29:28.751Z] <<< [apiv2][body] GET https://firebase-public.firebaseio.com/cli.json {"cloudBuildErrorAfter":1594252800000,"cloudBuildWarnAfter":1590019200000,"defaultNode10After":1594252800000,"minVersion":"3.0.5","node8DeploysDisabledAfter":1613390400000,"node8RuntimeDisabledAfter":1615809600000,"node8WarnAfter":1600128000000}
