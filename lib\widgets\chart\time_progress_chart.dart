// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';

// 环形时间进度统计小部件
class TimeProgressChart extends StatelessWidget {
  const TimeProgressChart({super.key});

  @override
  Widget build(BuildContext context) {
    // --- 数据计算 ---
    final now = DateTime.now();

    // 1. 本日进度 (小时)
    final dayProgress = now.hour;
    const dayTotal = 24;
    final dayPercentage = (dayProgress / dayTotal) * 100;

    // 2. 本周进度 (天)
    final weekProgress = now.weekday;
    const weekTotal = 7;
    final weekPercentage = (weekProgress / weekTotal) * 100;

    // 3. 本月进度 (天)
    final monthProgress = now.day;
    final monthTotal = DateUtils.getDaysInMonth(now.year, now.month);
    final monthPercentage = (monthProgress / monthTotal) * 100;

    // 4. 本年进度 (天)
    final startOfYear = DateTime(now.year, 1, 1);
    final yearProgress = now.difference(startOfYear).inDays + 1;
    final isLeap = (now.year % 4 == 0 && now.year % 100 != 0) || (now.year % 400 == 0);
    final yearTotal = isLeap ? 366 : 365;
    final yearPercentage = (yearProgress / yearTotal) * 100;

    // --- UI 构建 ---
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildProgressRing(
              percentage: dayPercentage,
              progressText: '$dayProgress/$dayTotal',
              context: context,
            ),
            _buildProgressRing(
              percentage: weekPercentage,
              progressText: '$weekProgress/$weekTotal',
              context: context,
            ),
            _buildProgressRing(
              percentage: monthPercentage,
              progressText: '$monthProgress/$monthTotal',
              context: context,
            ),
            _buildProgressRing(
              percentage: yearPercentage,
              progressText: '$yearProgress/$yearTotal',
              context: context,
            ),
          ],
        ),
      ),
    );
  }

  // 构建单个环形进度图的私有辅助方法
  Widget _buildProgressRing({
    required double percentage,
    required String progressText,
    required BuildContext context,
  }) {
    final Color primaryColor = Theme.of(context).colorScheme.primary;
    final Color backgroundColor = Theme.of(context).colorScheme.surfaceContainerHighest;

    return Expanded(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: 70,
            height: 70,
            child: Stack(
              alignment: Alignment.center,
              children: [
                PieChart(
                  PieChartData(
                    sectionsSpace: 0,
                    centerSpaceRadius: 20,
                    startDegreeOffset: -90,
                    pieTouchData: PieTouchData(enabled: false),
                    sections: [
                      PieChartSectionData(
                        color: primaryColor,
                        value: percentage,
                        showTitle: false,
                        radius: 8,
                      ),
                      PieChartSectionData(
                        color: backgroundColor,
                        value: 100 - percentage,
                        showTitle: false,
                        radius: 8,
                      ),
                    ],
                  ),
                ),
                Text(
                  '${percentage.toStringAsFixed(0)}%',
                  style: TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                    color: primaryColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            progressText,
            style: TextStyle(fontSize: 13, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }
}
