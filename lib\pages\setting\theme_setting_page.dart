// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
// models
import '../../models/theme_model.dart';
// states
import '../../states/theme_state.dart';
// l10n
import '../../generated/l10n/app_localizations.dart';

class ThemeSettingPage extends StatelessWidget {
  const ThemeSettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final themeState = context.read<ThemeState>();
    final currentTheme = context.watch<ThemeState>().currentTheme;
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.themeSettingPage_title),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        itemCount: AppTheme.values.length,
        itemBuilder: (context, index) {
          final theme = AppTheme.values[index];
          final themeData = AppThemes.getTheme(theme);
          final bool isSelected = theme == currentTheme;
          final bool isDefault = theme == AppThemes.defaultTheme;

          return Card(
            clipBehavior: Clip.antiAlias,
            margin: const EdgeInsets.only(bottom: 16.0),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.0),
              side: BorderSide(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).dividerColor.withValues(alpha: 0.5),
                width: isSelected ? 2.0 : 1.0,
              ),
            ),
            child: InkWell(
              onTap: () {
                // 增加震动反馈
                HapticFeedback.lightImpact();
                themeState.setTheme(theme);
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // 浅色和深色主题的预览
                  SizedBox(
                    height: 120, // 固定预览高度
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        Expanded(child: _buildThemePreview(themeData.light)),
                        Expanded(child: _buildThemePreview(themeData.dark)),
                      ],
                    ),
                  ),
                  // 主题名称和选中状态
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
                    child: Row(
                      children: [
                        Text(
                          theme.getLabel(l10n),
                          style: Theme.of(context).textTheme.titleSmall,
                        ),
                        if (isDefault)
                          Padding(
                            padding: const EdgeInsets.only(left: 8.0),
                            child: Text(
                              '(${l10n.common_default})',
                              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                color: Theme.of(context).colorScheme.secondary,
                              ),
                            ),
                          ),
                        const Spacer(),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: Theme.of(context).colorScheme.primary,
                            size: 18,
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  // 用于预览主题颜色方案的小部件
  Widget _buildThemePreview(ThemeData themeData) {
    return Container(
      padding: const EdgeInsets.all(12.0),
      color: themeData.scaffoldBackgroundColor,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildColorBar(themeData.colorScheme.primary),
          _buildColorBar(themeData.colorScheme.secondary),
          Row(
            children: [
              CircleAvatar(
                radius: 10,
                backgroundColor: themeData.colorScheme.tertiary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Container(
                  height: 12,
                  decoration: BoxDecoration(
                    color: themeData.colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              )
            ],
          )
        ],
      ),
    );
  }

  // 预览中用于颜色条的辅助小部件
  Widget _buildColorBar(Color color) {
    return Container(
      height: 20,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}
