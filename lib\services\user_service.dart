// import 'dart:developer' as developer;
import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:shared_preferences/shared_preferences.dart';
// models
import '../models/user_model.dart';
// tools
import '../tools/config.dart';

class UserService {
  final FirebaseFirestore _firestore;
  final FirebaseFunctions _functions;

  /// ====== 私有状态 ======

  // Firestore Collection 常量
  final String _usersCollection = Config.service.usersCollection;

  /// ====== 构造函数 ======

  UserService(this._firestore, this._functions);

  /// ====== 流监听 ======

  /// 获取用户信息的实时流 (Firestore)
  Stream<UserModel?> getUserStream(String userId) {
    try {
      final docRef = _firestore.collection(_usersCollection).doc(userId);
      return docRef.snapshots().map((snapshot) {
        if (!snapshot.exists) return null;
        final data = snapshot.data();
        if (data == null) return null;

        try {
          data['id'] = snapshot.id; // 手动添加文档 ID
          return UserModel.fromDatabase(data);
        } catch (e) {
          return null;
        }
      });
    } catch (e) {
      // 返回一个发出错误的流
      return Stream.error(e);
    }
  }

  /// ====== 网络请求 ======

  // 只更新 lastSignInTime 字段
  Future<void> recordSignIn() async {
    final callable = _functions.httpsCallable(Config.service.recordSignInFn);

    await callable.call();
  }

  // 升级账户，同时更新 lastSignInTime 字段
  Future<void> upgradeAccount() async {
    final callable = _functions.httpsCallable(Config.service.upgradeAccountFn);

    await callable.call();
  }

  /// 更新用户名
  Future<void> updateUsername(String newName) async {
    final callable = _functions.httpsCallable(Config.service.updateUsernameFn);
      
    await callable.call(<String, dynamic>{'newName': newName});
  }

  /// 删除用户
  Future<void> deleteUser() async {
    final callable = _functions.httpsCallable(Config.service.deleteUserFn);

    await callable.call();
  }

  /// ====== 本地存储 ======
  
  // --- 用户 ID ---

  /// 保存用户 ID 到本地
  Future<void> setLocalStorageUserId(String userId) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(Config.service.appUserIdKey, userId);
  }

  /// 从本地读取用户 ID
  Future<String?> getLocalStorageUserId() async {
    final prefs = await SharedPreferences.getInstance();

    return prefs.getString(Config.service.appUserIdKey);
  }

  /// 从本地清除用户 ID
  Future<void> clearLocalStorageUserId() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.remove(Config.service.appUserIdKey);
  }

  // --- 用户邮箱 ---

  /// 保存用于登录的 email
  Future<void> setLocalStorageUserEmail(String email) async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(Config.service.appUserEmailKey, email);
  }

  /// 获取用于登录的 email
  Future<String?> getLocalStorageUserEmail() async {
    final prefs = await SharedPreferences.getInstance();

    return prefs.getString(Config.service.appUserEmailKey);
  }

  /// 移除用于登录的 email
  Future<void> clearLocalStorageUserEmail() async {
    final prefs = await SharedPreferences.getInstance();

    await prefs.remove(Config.service.appUserEmailKey);
  }
}
