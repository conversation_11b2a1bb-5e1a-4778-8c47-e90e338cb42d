
# 部署到生产环境

`firebase deploy --only functions -P taskive-4eec0`
`firebase deploy --only firestore:rules -P taskive-4eec0`
`firebase deploy --only firestore:indexes -P taskive-4eec0`
`firebase deploy --only hosting -P taskive-4eec0`

# Google Play 应用打包

指定风味为 prod

`flutter build appbundle --flavor prod`

# 运行 Firebase 模拟器

`firebase emulators:start`

# 运行 web 端

`flutter run -d chrome`

# 生成图标

`dart run flutter_launcher_icons`

# 安卓 USB 调试时

Firebase 模拟器的 ip 需要连接真实的 ip
`lib/main.dart` 文件和 `android/app/src/main/res/xml/network_security_config.xml` 需要同步修改真实的 ip
目前使用共享网络，解决手机和电脑网络隔绝的问题

# 删除手机上的应用残留

`adb shell pm list packages | findstr taskive`

`adb uninstall com.taskivestudio.taskive`

# Asset Links

`https://taskive-4eec0.firebaseapp.com/.well-known/assetlinks.json`

目前已启用了 Google Play 应用签名

将 SHA-1 证书指纹和 SHA-256 证书指纹上传到 Firebase 后台安卓应用配置，Asset Links 就会有数据

# 关于 RevenueCat webhook 密钥

已经交由 Google Cloud Secret Manager 管理

本地环境：云函数在模拟器中运行时，会使用“应用默认凭据 (ADC)”访问密钥
生产环境：云函数部署后，会使用服务账号的身份访问密钥

# 关于 RevenueCat Webhook

测试环境：运行 `ngrok http 5001`，然后把映射的公网地址粘贴到 webhook 配置项
