{"version": 3, "file": "time.js", "sourceRoot": "", "sources": ["../../src/tools/time.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,KAAK,MAAM,MAAM,2BAA2B,CAAC;AACpD,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AACzD,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAGxC,MAAM,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;AAExB;;;;;;;GAOG;AACH,MAAM,CAAC,OAAO,OAAO,SAAS;IAC5B,gBAAe,CAAC;IAEhB,kDAAkD;IAClD,UAAU;IACV,kDAAkD;IAElD;;;;OAIG;IACH,MAAM,CAAC,YAAY,CAAC,QAAgB,EAAE,IAAU;QAC9C,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC9C,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/B,OAAO,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;gBAC7C,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAG,CAAW,CAAC,OAAO;aAC5B,CAAC,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,YAAY,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YACrC,OAAO,YAAY,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,UAAU,CAAC,QAAgB,EAAE,IAAU;QAC5C,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC9C,SAAS,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;YACpC,OAAO,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;gBAC3C,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAG,CAAW,CAAC,OAAO;aAC5B,CAAC,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;YACpC,YAAY,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;YAC1C,OAAO,YAAY,CAAC;QACtB,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,YAAY,CAAC,QAAgB,EAAE,OAAe;QACnD,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpD,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACnD,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;QACxD,MAAM,GAAG,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAElD,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;YACxC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/B,OAAO,aAAa,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;gBACzC,OAAO;gBACP,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAG,CAAW,CAAC,OAAO;aAC5B,CAAC,CAAC;YACH,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;QAC9C,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,aAAa,CAAC,QAAgB;QACnC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,CAAC;IACjD,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,eAAe,CAAC,QAAgB,EAAE,IAAU;QACjD,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACvC,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;IAC9C,CAAC;IAED;;KAEC;IACD,MAAM,CAAC,wBAAwB,CAAC,QAAgB;QAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QACpC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,qBAAqB,CAAC,CAAC;QACvE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,kBAAkB,CAAC,QAAgB,EAAE,IAAU;QACpD,IAAI,CAAC;YACH,MAAM,iBAAiB,GAAG,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACtD,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CACtB,iBAAiB,CAAC,WAAW,EAAE,EAC/B,iBAAiB,CAAC,QAAQ,EAAE,EAC5B,iBAAiB,CAAC,OAAO,EAAE,CAC5B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;gBAC/C,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAG,CAAW,CAAC,OAAO;aAC5B,CAAC,CAAC;YACH,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CACtB,IAAI,CAAC,cAAc,EAAE,EACrB,IAAI,CAAC,WAAW,EAAE,EAClB,IAAI,CAAC,UAAU,EAAE,CAClB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,gBAAgB,CAAC,QAAgB,EAAE,IAAU;QAClD,IAAI,CAAC;YACH,MAAM,iBAAiB,GAAG,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACtD,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CACtB,iBAAiB,CAAC,WAAW,EAAE,EAC/B,iBAAiB,CAAC,QAAQ,EAAE,EAC5B,iBAAiB,CAAC,OAAO,EAAE,EAC3B,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAChB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;gBAC7C,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAG,CAAW,CAAC,OAAO;aAC5B,CAAC,CAAC;YACH,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CACtB,IAAI,CAAC,cAAc,EAAE,EACrB,IAAI,CAAC,WAAW,EAAE,EAClB,IAAI,CAAC,UAAU,EAAE,EACjB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAChB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,gBAAgB,CAAC,QAAgB,EAAE,SAAe,EAAE,OAAa;QACtE,IAAI,CAAC;YACH,MAAM,kBAAkB,GAAG,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC5D,MAAM,oBAAoB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAC5C,kBAAkB,CAAC,WAAW,EAAE,EAChC,kBAAkB,CAAC,QAAQ,EAAE,EAC7B,kBAAkB,CAAC,OAAO,EAAE,CAC7B,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACxD,MAAM,kBAAkB,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAC1C,gBAAgB,CAAC,WAAW,EAAE,EAC9B,gBAAgB,CAAC,QAAQ,EAAE,EAC3B,gBAAgB,CAAC,OAAO,EAAE,EAC1B,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAChB,CAAC,CAAC;YAEH,OAAO,EAAE,KAAK,EAAE,oBAAoB,EAAE,GAAG,EAAE,kBAAkB,EAAE,CAAC;QAClE,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;gBAC7C,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAG,CAAW,CAAC,OAAO;aAC5B,CAAC,CAAC;YACH,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,oBAAoB,CAAC,QAAgB,EAAE,IAAU;QACtD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC9C,OAAO;gBACL,KAAK,EAAE,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC;gBAC/B,GAAG,EAAE,SAAS,CAAC,OAAO,EAAE;aACzB,CAAC;QACJ,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE;gBAClD,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAG,CAAW,CAAC,OAAO;aAC5B,CAAC,CAAC;YACH,OAAO;gBACL,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC;gBAC7B,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE;aACvB,CAAC;QACJ,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,QAAgB,EAAE,WAAiB,EAAE,YAAkB;QAC7E,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YACxD,MAAM,KAAK,GAAG,WAAW,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAElD,YAAY,CAAC,QAAQ,CACnB,KAAK,CAAC,QAAQ,EAAE,EAChB,KAAK,CAAC,UAAU,EAAE,EAClB,KAAK,CAAC,UAAU,EAAE,EAClB,KAAK,CAAC,eAAe,EAAE,CACxB,CAAC;YAEF,OAAO,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBAC5C,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAG,CAAW,CAAC,OAAO;aAC5B,CAAC,CAAC;YACH,eAAe;YACf,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC;YAC3C,YAAY,CAAC,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE,EAAE,YAAY,CAAC,UAAU,EAAE,EAAE,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC;YACrG,OAAO,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,iBAAiB,CAAC,QAAgB,EAAE,IAAU;QACnD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC9C,MAAM,IAAI,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YACrE,MAAM,GAAG,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAC5D,OAAO,QAAQ,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;gBAC5C,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAG,CAAW,CAAC,OAAO;aAC5B,CAAC,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YACnE,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAC1D,OAAO,QAAQ,CAAC,GAAG,IAAI,GAAG,KAAK,GAAG,GAAG,EAAE,EAAE,EAAE,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED;;;;;OAKG;IACH,MAAM,CAAC,wBAAwB,CAAC,QAAgB,EAAE,SAAe,EAAE,WAAmB;QACpF,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YACnD,gDAAgD;YAChD,MAAM,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,GAAG,WAAW,GAAG,CAAC,CAAC;YAC5D,mBAAmB;YACnB,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE;gBACrD,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAG,CAAW,CAAC,OAAO;aAC5B,CAAC,CAAC;YACH,MAAM,SAAS,GAAG,SAAS,CAAC,cAAc,EAAE,GAAG,WAAW,GAAG,CAAC,CAAC;YAC/D,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,sBAAsB,CAAC,QAAgB,EAAE,IAAU;QACxD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAC9C,MAAM,IAAI,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;YACrC,MAAM,KAAK,GAAG,CAAC,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YACrE,MAAM,GAAG,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAC5D,OAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,CAAC,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE;gBAC9C,QAAQ,EAAE,QAAQ;gBAClB,KAAK,EAAG,CAAW,CAAC,OAAO;aAC5B,CAAC,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;YACnC,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YACnE,MAAM,GAAG,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAC1D,OAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC;QACnC,CAAC;IACH,CAAC;IAED,kDAAkD;IAClD,UAAU;IACV,kDAAkD;IAElD;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,QAA2B;QAC7C,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,MAAM,CAAC,oBAAoB,CAAC,aAA8B;QACxD,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjD,OAAO,IAAI,CAAC;QACd,CAAC;QACD,0CAA0C;QAC1C,uCAAuC;QACvC,iCAAiC;QACjC,OAAO,aAAa,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;IAC3C,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe;QACpB,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,MAAM,EAAE,KAAK,CAAC,MAAM;YACpB,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,MAAM,EAAE,KAAK,CAAC,MAAM;SACrB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,4BAA4B,CACjC,SAAiB,EACjB,UAA4B,EAC5B,WAA6B;QAE7B,IAAI,mBAAmB,GAAG,CAAC,CAAC;QAE5B,IAAI,SAAS,KAAK,QAAQ,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAClE,mBAAmB,GAAG,UAAU,CAAC,MAAM,CAAC;QAC1C,CAAC;aAAM,IAAI,SAAS,KAAK,SAAS,IAAI,WAAW,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC5E,mBAAmB,GAAG,WAAW,CAAC,MAAM,CAAC;QAC3C,CAAC;QAED,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,qBAAqB,CAAC,OAAe;QAC1C,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;QACpD,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;IAC5F,CAAC;CACF"}