// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// states
import '../states/recurring_state.dart';
import '../states/user_state.dart';
// services
import '../services/permission_service.dart';
// tools
import '../tools/bottom_slide_route.dart';
import '../tools/config.dart';
// widgets
import '../widgets/common/app_header_bar.dart';
import '../widgets/recurring/recurring_card.dart';
// sheets
import '../sheets/common/subscribe_guide_sheet.dart';
// pages
import '../sheets/recurring/recurring_edit_page.dart';
// others
import '../generated/l10n/app_localizations.dart';

class RecurringsView extends StatelessWidget {
  const RecurringsView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final recurringState = context.watch<RecurringState>();
    context.watch<UserState>();

    return Scaffold(
      appBar: AppHeaderBar(
        title: Text(l10n.recurringsView_title),
        actions: [
          IconButton(
            icon: const Icon(LucideIcons.circlePlus),
            onPressed: () {
              // 增加震动反馈
              HapticFeedback.lightImpact();
              if (recurringState.canCreateRecurring()) {
                // 如果可以创建，则显示编辑页
                Navigator.of(context).push(BottomSlideRoute(page: const RecurringEditPage()));
              } else {
                // 如果不可以创建，则显示订阅引导页
                SubscribeGuideSheet.show(
                  context,
                  feature: Feature.recurring,
                );
              }
            },
          ),
        ],
      ),
      body: Consumer<RecurringState>(
        builder: (context, recurringState, child) {
          // 获取周期任务列表
          final recurrings = recurringState.recurrings;

          if (recurrings.isEmpty) {
            return Center(child: Text(l10n.recurringsView_emptyMessage));
          }

          // 直接构建列表视图，暂不分组
          return Column(
            children: [
              Expanded(
                child: ListView.builder(
                  itemCount: recurrings.length,
                  itemBuilder: (context, index) {
                    final recurring = recurrings[index];
                    // 使用 RecurringCard 显示每个周期任务
                    return RecurringCard(recurring: recurring);
                  },
                ),
              ),
              
              SizedBox(height: Config.app.bottomSpace),
            ],
          );
        },
      ),
    );
  }
}
