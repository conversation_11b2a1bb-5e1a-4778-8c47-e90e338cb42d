// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// pages
import '../../pages/common/search_page.dart';
import '../../pages/setting/settings_page.dart';

// 通用 AppBar 组件，实现了 PreferredSizeWidget 以便直接在 Scaffold.appBar 中使用
class AppHeaderBar extends StatelessWidget implements PreferredSizeWidget {
  final Text title; // 标题
  final List<Widget>? actions; // 自定义操作按钮列表

  // 构造函数
  const AppHeaderBar({
    super.key,
    required this.title,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: title,
      actions: [
        // 展开自定义操作按钮 (如果提供了)
        if (actions != null) ...actions!,

        IconButton(
          icon: const Icon(LucideIcons.search),
          onPressed: () {
            // 增加震动反馈
            HapticFeedback.lightImpact();
            Navigator.of(context).push(
              MaterialPageRoute(builder: (context) => const SearchPage()),
            );
          },
        ),

        IconButton(
          icon: const Icon(LucideIcons.bolt),
          onPressed: () {
            // 增加震动反馈
            HapticFeedback.lightImpact();
            Navigator.of(context).push(
              MaterialPageRoute(builder: (context) => const SettingsPage()),
            );
          },
        ),
      ],
    );
  }

  // 实现 preferredSize，返回 AppBar 的标准高度
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
