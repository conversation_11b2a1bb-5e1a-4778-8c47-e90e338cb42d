// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

typedef EndDateCallback = void Function(DateTime? date); // 允许传入 null 以清除结束日期

// 周期规则结束日期选择器
class RulesEndPicker extends StatelessWidget {
  final DateTime? currentEndDate; // 当前选中的结束日期
  final DateTime startDate;       // 规则的开始日期，用于限制 firstDate
  final EndDateCallback onDateSelected;

  const RulesEndPicker({
    super.key,
    this.currentEndDate,
    required this.startDate,
    required this.onDateSelected,
  });

  // 格式化显示的日期
  String _formatDisplayDate(AppLocalizations l10n) {
    if (currentEndDate == null) {
      return l10n.rulesEndPicker_neverEnds; // 占位符
    }
    return DateFormat.yMd(l10n.localeName).format(currentEndDate!);
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    // 使用 Column 包含标题和选择区域
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start, // 标题左对齐
      children: [
        // 标题
        Padding(
          padding: const EdgeInsets.only(bottom: 12.0), // 标题和选择器之间的间距
          child: Text(
            l10n.rulesEndPicker_title,
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: colorScheme.onSurfaceVariant), // 标题样式
          ),
        ),
        // 可点击的选择区域
        InkWell(
          onTap: () => _showRulesEndSheet(context),
          child: Container(
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // 使用 Expanded 让 Text 填充可用空间
                Expanded(
                  child: Text(
                    _formatDisplayDate(l10n),
                    style: TextStyle(
                      fontSize: 16,
                      color: colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center, // 文本居中
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // 图标保持在右侧
                Icon(LucideIcons.chevronsUpDown, size: 18, color: colorScheme.onSurfaceVariant),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 显示日期选择器底部弹窗
  void _showRulesEndSheet(BuildContext context) {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final l10n = AppLocalizations.of(context)!;
    // firstDate 必须是 startDate 之后的一天
    final DateTime firstPickerDate = startDate.add(const Duration(days: 1));
    // initialDate 优先使用当前值，但不能早于 firstPickerDate
    DateTime initialPickerDate = (currentEndDate != null && currentEndDate!.isAfter(startDate))
        ? currentEndDate!
        : firstPickerDate;

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return BaseBottomSheet(
          fullWidth: true,
          child: Column(
            mainAxisSize: MainAxisSize.min, // 自适应高度
            children: [
              // 日期选择器
              CalendarDatePicker(
                initialDate: initialPickerDate,
                firstDate: firstPickerDate, // 结束日期必须在开始日期之后
                lastDate: DateTime(startDate.year + 100), // 结束日期不能超过 100 年
                onDateChanged: (newDate) {
                  onDateSelected(newDate);
                  Navigator.pop(context);
                },
              ),
              // 添加清除按钮
              TextButton(
                onPressed: () {
                  onDateSelected(null); // 回调 null
                  Navigator.pop(context); // 关闭弹窗
                },
                child: Text(l10n.rulesEndPicker_neverEnds),
              ),
            ],
          ),
        );
      },
    );
  }
}
