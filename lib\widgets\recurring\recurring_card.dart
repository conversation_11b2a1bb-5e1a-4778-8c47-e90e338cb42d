// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/recurring_model.dart';
// states
import '../../states/project_state.dart';
// tools
import '../../tools/bottom_slide_route.dart';
import '../../tools/config.dart';
import '../../tools/extensions.dart';
// widgets
import '../project/project_badge.dart';
import '../common/base_label.dart';
// sheets
import '../../sheets/recurring/recurring_longpress_sheet.dart';
// pages
import '../../sheets/recurring/recurring_edit_page.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class RecurringCard extends StatelessWidget {
  final RecurringModel recurring;

  const RecurringCard({
    super.key,
    required this.recurring,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final textTheme = Theme.of(context).textTheme;
    final projectState = Provider.of<ProjectState>(context);

    final template = recurring.template;
    final rule = recurring.rule;
    final project = projectState.getProjectById(template.projectId ?? '');

    final String? timeText = template.dueClock?.formatClock();

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      child: InkWell(
        onTap: () {
          // 增加震动反馈
          HapticFeedback.lightImpact();

          Navigator.of(context).push(
            BottomSlideRoute(page: RecurringEditPage(recurring: recurring))
          );
        },
        onLongPress: () {
          // 增加震动反馈
          HapticFeedback.lightImpact();
          // 显示长按操作菜单
          RecurringLongPressSheet.show(context, recurring);
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // --- 任务名称 ---
              Text(
                recurring.template.name,
                style: textTheme.titleMedium,
                maxLines: Config.app.taskNameMaxLines,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),

              // --- 规则描述 ---
              Row(
                children: [
                  Icon(LucideIcons.repeat, size: 14, color: Theme.of(context).colorScheme.onSurfaceVariant),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      rule.getLocalizedDescription(l10n),
                      style: textTheme.bodySmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),

              // --- 时间范围 ---
              Row(
                children: [
                  Icon(LucideIcons.clock, size: 14, color: Theme.of(context).colorScheme.onSurfaceVariant),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      rule.getLocalizedTimeRange(l10n),
                      style: textTheme.bodySmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              // --- 元数据 ---
              if (timeText != null || project != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    if (timeText != null)
                      BaseLabel(text: timeText),
                    const Spacer(),
                    if (project != null)
                      ProjectBadge(project: project),
                  ],
                ),
              ]
            ],
          ),
        ),
      ),
    );
  }
}
