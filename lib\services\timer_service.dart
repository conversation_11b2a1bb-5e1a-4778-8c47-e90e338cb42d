import 'dart:developer' as developer;
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
// models
import '../models/timer_model.dart';
// tools
import '../tools/config.dart';

class TimerService {
  final FirebaseFirestore _firestore;
  final FirebaseFunctions _functions;

  /// ====== 私有状态 ======

  // Firestore 集合名称常量
  final String _usersCollection = Config.service.usersCollection;
  final String _timersCollection = Config.service.timersCollection;

  /// ====== 构造函数 ======

  TimerService(this._firestore, this._functions);

  /// ====== 流监听 ======

  /// 获取指定任务的计时器实时流
  Stream<List<TimerModel>> getTimerStream(String userId) {
    if (userId.isEmpty) return Stream.value([]);

    final collectionRef = _getTimerCollectionRef(userId);

    return collectionRef.orderBy('createTime', descending: true).limit(100).snapshots().map((snapshot) {
      if (snapshot.docs.isEmpty) return []; // 如果没有文档，返回空列表

      // 将 Firestore 文档映射为 TimerModel 列表
      return snapshot.docs.map((doc) {
        try {
          final data = doc.data();
          data['id'] = doc.id; // 将文档 ID 添加到数据中
          return TimerModel.fromDatabase(data);
        } catch (e) {
          developer.log('⛔ 解析计时器文档 ${doc.id} 失败', error: e);
          return null;
        }
      }).whereType<TimerModel>().toList();
    });
  }

  /// ====== 网络请求 ======

  /// 创建一个新的计时器（客户端直接写入以提高速度）
  Future<String?> createTimer(String userId, TimerModel timer) async {
    final collectionRef = _getTimerCollectionRef(userId);

    final data = timer.toDatabase();

    data['userId'] = userId;
    data['createTime'] = FieldValue.serverTimestamp();
    data['updateTime'] = FieldValue.serverTimestamp();

    final docRef = await collectionRef.add(data);

    return docRef.id;
  }

  /// 更新计时器（客户端直接写入以提高速度）
  Future<void> updateTimer(String userId, TimerModel timer) async {
    final docRef = _getTimerCollectionRef(userId).doc(timer.id);

    final data = timer.toDatabase();

    data['updateTime'] = FieldValue.serverTimestamp();

    await docRef.update(data);
  }

  /// 删除计时器（保留在云函数）
  Future<void> deleteTimer(String timerId) async {
    final callable = _functions.httpsCallable(Config.service.deleteTimerFn);

    await callable.call({'timerId': timerId});
  }

  /// ====== 工具方法 ======

  // 内部方法：获取该用户的计时器子集合引用
  CollectionReference<Map<String, dynamic>> _getTimerCollectionRef(String userId) {
    return _firestore.collection(_usersCollection).doc(userId).collection(_timersCollection);
  }
}
