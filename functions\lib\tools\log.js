import * as logger from 'firebase-functions/logger';
/**
 * 统一的错误处理和日志记录
 */
export function handleRecurringError(operation, userId, recurringId, error) {
    const e = error;
    logger.error(`${operation}时出错`, {
        userId,
        recurringId,
        error: e.message,
        stack: e.stack
    });
}
/**
 * 统一的参数验证
 */
export function validateRecurringData(recurring, userId, recurringId) {
    if (!recurring || !recurring.rule) {
        logger.error("周期任务数据无效或缺少规则", {
            userId,
            recurringId,
            dataExists: !!recurring
        });
        return false;
    }
    return true;
}
/**
 * 记录性能指标
 */
export function recordMetrics(operation, duration, count) {
    logger.info(`${operation} 性能指标`, {
        operation,
        duration_ms: duration,
        processed_count: count,
        avg_per_item: duration / count
    });
}
//# sourceMappingURL=log.js.map