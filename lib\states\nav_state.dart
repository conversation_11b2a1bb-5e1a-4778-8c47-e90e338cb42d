// import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:collection/collection.dart';
// models
import '../models/nav_model.dart';
// states
import './project_state.dart';
import './setting_state.dart';
// services
import '../services/setting_service.dart';
// tools
import '../tools/config.dart';
import '../tools/app_exception.dart';
// others
import '../generated/l10n/app_localizations.dart';

class NavState extends ChangeNotifier {
  final ProjectState _projectState;
  final SettingState _settingState;
  final SettingService _settingService;

  /// ====== 私有状态 ======

  // 所有 NavModel 的缓存，以 ID 为键
  final Map<String, NavModel> _allNavs = {};

  // 当前 l10n 实例，用于重建文本
  AppLocalizations? _l10n;

  // 固定的 "Entry" 导航项
  NavModel? _entryNav;

  // 用户选择并排序的导航项 (不包括 Entry)
  List<NavModel> _userNavs = [];

  // 当前选中的底部导航栏索引
  int _currentIndex = 0;

  /// ====== 构造函数 ======

  NavState(this._projectState, this._settingState, this._settingService) {
    _initialize();
    _settingState.addListener(_listenToSettingChanges);
    _projectState.addListener(_listenToProjectChanges);
  }

  /// ====== 公共状态 ======

  /// 底部导航栏和 PageView 使用的组合列表 (用户选择的 + 固定的 Entry)
  List<NavModel> get bottomNavs {
    // 如果用户没有选择任何导航项，只显示 Entry
    if (_userNavs.isEmpty) {
      return [_entryNav!];
    }
    // 否则，显示用户选择的项，并在末尾添加 Entry
    return [..._userNavs, _entryNav!];
  }

  /// 当前选中的底部导航栏索引
  int get currentIndex => _currentIndex;

  /// 所有可用的导航模型 (用于 EntryView)
  Map<String, NavModel> get allNavs => _allNavs;

  /// 用户可选择的导航项
  List<NavModel> get selectableNavs {
    final enabledIds = _userNavs.map((nav) => nav.id).toSet();
    return _allNavs.values
      .where((nav) => !nav.isFixed && !enabledIds.contains(nav.id))
      .toList();
  }

  /// 用户已选择的导航项
  List<NavModel> get selectedNavs => _userNavs;

  /// 是否可以添加更多导航项
  bool get canAddMoreNavs => _userNavs.length < Config.app.maxUserNavCount;

  /// ====== 流监听 ======

  /// 初始化，创建所有 NavModel 实例
  void _initialize() {
    // 1. 从定义创建标准导航模型
    for (final def in NavDefinition.definitions) {
      final model = NavModel.fromDefinition(def);
      _allNavs[model.id] = model;
    }

    // 2. 找到并保存固定的 Entry 导航项
    _entryNav = _allNavs[NavDefinition.entry.id];

    if (_entryNav == null) {
      // 这是一个致命错误，应用无法在没有 Entry 的情况下运行
      throw AppException(
        how: AppExceptionHow.loadSetting,
        why: AppExceptionWhy.operationFailed,
        message: 'Entry NavModel not found during initialization',
      );
    }
  }

  // 监听设置状态变化
  void _listenToSettingChanges() {
    final newNavIds = _settingState.settings.navVer;
    final currentNavIds = newNavIds ?? NavModel.defaultNavIds;
    load(currentNavIds);
  }

  /// 监听项目状态变化
  void _listenToProjectChanges() {
    // 1. 移除所有旧的、由项目生成的 NavModel
    _allNavs.removeWhere((key, value) => value.isProject);

    // 2. 为所有当前项目创建新的 NavModel
    for (final project in _projectState.projects) {
      final navModel = NavModel.fromProject(project);
      // 如果有 l10n 实例，立即重建文本
      if (_l10n != null) {
        navModel.rebuild(_l10n!, projectName: project.name);
      }
      _allNavs[navModel.id] = navModel;
    }
    
    // 3. 重新加载以过滤掉可能已删除的项目，并通知 UI 更新
    final newNavIds = _settingState.settings.navVer;
    final currentNavIds = newNavIds ?? NavModel.defaultNavIds;
    load(currentNavIds);
  }

  /// ====== 网络请求 ======

  /// 从 setting state 加载用户选择的导航项
  void load(List<String> navIds) {
    final newNavs = navIds
      .map((id) => _allNavs[id])
      .where((nav) => nav != null && !nav.isFixed)
      .cast<NavModel>()
      .toList();

    if (!listEquals(newNavs, _userNavs)) {
      _userNavs = newNavs;
      // 如果当前索引超出了新列表的范围，重置为0
      if (_currentIndex >= bottomNavs.length) {
        _currentIndex = 0;
      }
      notifyListeners();
    }
  }

  /// 将当前导航顺序保存到设置
  Future<void> _saveNavVer() async {
    final navIds = _userNavs.map((nav) => nav.id).toList();
    final userId = _settingState.currentUserId;

    if (userId == null || userId.isEmpty) {
      throw AppException(
        how: AppExceptionHow.updateSetting,
        why: AppExceptionWhy.unauthenticated,
      );
    }

    await _settingService.saveNavVer(userId, navIds);

    notifyListeners();
  }

  /// ====== 工具方法 ======

  /// 重建所有导航项的 UI 文本 (例如，在语言更改后)
  void rebuild(AppLocalizations l10n) {
    // 保存 l10n 实例
    _l10n = l10n;
    // 重建所有缓存的模型
    _allNavs.forEach((id, model) {
      if (model.isProject) {
        final project = _projectState.projects.firstWhereOrNull((p) => p.id == id);
        if (project != null) {
          model.rebuild(l10n, projectName: project.name);
        }
      } else {
        model.rebuild(l10n);
      }
    });

    // 手动触发一次更新，以确保 UI 反映新的文本
    notifyListeners();
  }

  /// 当底部导航栏项被点击时调用
  void onNavTapped(int index) {
    // 如果点击的索引与当前索引不同，则更新当前索引
    if (index != _currentIndex) {
      _currentIndex = index;
      notifyListeners();
    }
  }

  /// 添加一个新的导航项
  void add(int index, NavModel nav) {
    if (canAddMoreNavs) {
      _userNavs.insert(index, nav);
      _saveNavVer();
    }
  }

  /// 移除一个导航项
  void remove(int index) {
    _userNavs.removeAt(index);
    // 如果当前索引超出了新列表的范围，重置为最后一个
    if (_currentIndex >= bottomNavs.length) {
      _currentIndex = bottomNavs.length - 1;
    }
    _saveNavVer();
  }

  /// 重新排序导航项
  void reorder(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final item = _userNavs.removeAt(oldIndex);
    _userNavs.insert(newIndex, item);
    _saveNavVer();
  }

  @override
  void dispose() {
    _settingState.removeListener(_listenToSettingChanges);
    _projectState.removeListener(_listenToProjectChanges);
    super.dispose();
  }
}
