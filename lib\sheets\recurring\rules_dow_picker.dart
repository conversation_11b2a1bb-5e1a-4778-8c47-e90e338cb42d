// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// widgets
import '../../widgets/common/base_bottom_sheet.dart';
// others
import '../../generated/l10n/app_localizations.dart';

typedef DowCallback = void Function(List<int>? days);

// 星期选择器 (Days of Week) - 点击占位符弹出选择
class RulesDowPicker extends StatelessWidget {
  final List<int>? currentDaysOfWeek; // 当前选中的星期 (1-7, Monday-Sunday)
  final DowCallback onDaysSelected;

  const RulesDowPicker({
    super.key,
    this.currentDaysOfWeek,
    required this.onDaysSelected,
  });

  // 格式化显示的选中日期
  String _formatSelectedDays(AppLocalizations l10n, List<String> weekDays) {
    if (currentDaysOfWeek == null || currentDaysOfWeek!.isEmpty) {
      return l10n.rulesDowPicker_placeholder; // 占位符
    }
    // 对 currentDaysOfWeek 排序以确保显示顺序一致
    final sortedDays = List<int>.from(currentDaysOfWeek!)..sort();
    return sortedDays.map((day) => weekDays[day - 1]).join(', ');
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;
    final List<String> weekDays = l10n.rulesDowPicker_weekdays.split(',');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        Padding(
          padding: EdgeInsets.only(bottom: 12.0),
          child: Text(
            l10n.common_specificDate,
            style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold, color: colorScheme.onSurfaceVariant),
          ),
        ),
        // 可点击的选择区域
        InkWell(
          onTap: () => _showRulesDowSheet(context),
          child: Container(
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    _formatSelectedDays(l10n, weekDays), // 显示占位符或选中的日期
                    style: TextStyle(
                      fontSize: 16,
                      color: (currentDaysOfWeek == null || currentDaysOfWeek!.isEmpty)
                          ? colorScheme.onSurfaceVariant // 占位符颜色
                          : colorScheme.onSurface, // 选中值颜色
                    ),
                    textAlign: TextAlign.center, // 文本居中
                    overflow: TextOverflow.ellipsis, // 防止文本溢出
                  ),
                ),
                Icon(LucideIcons.chevronsUpDown, size: 18, color: colorScheme.onSurfaceVariant),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 显示星期选择底部弹窗
  void _showRulesDowSheet(BuildContext context) {
    // 增加震动反馈
    HapticFeedback.lightImpact();

    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;
    final List<String> weekDays = l10n.rulesDowPicker_weekdays.split(',');
    // 使用 StatefulBuilder 来管理弹窗内的临时选中状态
    List<int> tempSelectedDays = List<int>.from(currentDaysOfWeek ?? []);

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            // 处理按钮点击（更新临时状态）
            void toggleDayInModal(int day) {
              setModalState(() {
                if (tempSelectedDays.contains(day)) {
                  tempSelectedDays.remove(day);
                } else {
                  if (tempSelectedDays.length < 5) {
                    tempSelectedDays.add(day);
                    tempSelectedDays.sort();
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text(l10n.common_maxSelection(5)), duration: Duration(seconds: 1)),
                    );
                  }
                }
              });
            }

            return BaseBottomSheet(
              title: l10n.rulesDowPicker_title,
              onConfirm: () {
                // 确认选择，调用回调并关闭弹窗
                onDaysSelected(tempSelectedDays.isEmpty ? null : tempSelectedDays);
                Navigator.pop(context);
              },
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 7,
                  childAspectRatio: 1.0,
                  crossAxisSpacing: 8,
                  mainAxisSpacing: 8,
                ),
                itemCount: weekDays.length,
                itemBuilder: (context, index) {
                  final day = index + 1;
                  final isSelected = tempSelectedDays.contains(day);
                  return ChoiceChip(
                    label: Center(child: Text(weekDays[index])),
                    selected: isSelected,
                    onSelected: (selected) {
                      // 增加震动反馈
                      HapticFeedback.lightImpact();
                      toggleDayInModal(day);
                    },
                    selectedColor: colorScheme.primary.withValues(alpha: 0.2),
                    showCheckmark: false,
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    labelPadding: EdgeInsets.zero,
                    padding: EdgeInsets.zero,
                    side: BorderSide(
                      color: isSelected ? Colors.transparent : colorScheme.outline,
                      width: 1.0,
                    ),
                    labelStyle: TextStyle(
                      color: isSelected ? colorScheme.primary : colorScheme.onSurface,
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}
