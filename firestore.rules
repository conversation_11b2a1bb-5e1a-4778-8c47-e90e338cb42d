rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // ===== 用户 (users) 集合规则 =====
    match /users/{userId} {

      // isOwner 是检查请求认证 uid 是否与路径中的 userId 匹配的函数
      function isOwner() {
        return request.auth != null && request.auth.uid == userId;
      }

      // 用户只能读取自己的用户文档
      allow get: if isOwner();

      // 由云函数处理，客户端不再需要直接的写权限
      allow write: if false;

      // ===== 任务 (tasks) 子集合 =====
      match /tasks/{taskId} {

        allow get, list: if isOwner();
        
        allow create: if isOwner() &&
          request.resource.data.name is string &&
          request.resource.data.name.size() > 0 &&
          request.resource.data.name.size() <= 200 &&
          isStringOrNull(request.resource.data.projectId) &&
          request.resource.data.recurringId == null &&
          request.resource.data.isImportant is bool &&
          request.resource.data.isUrgent is bool &&
          // isTaskTimingValid(request.resource.data) &&
          // 创建时，任务不能是已完成或已丢弃状态
          request.resource.data.completeTime == null &&
          request.resource.data.dropTime == null &&
          // 确保 userId 正确
          request.resource.data.userId == userId &&
          request.resource.data.createTime == request.time &&
          request.resource.data.updateTime == request.time;

        allow update: if isOwner() &&
          request.resource.data.name is string &&
          request.resource.data.name.size() > 0 &&
          request.resource.data.name.size() <= 200 &&
          isStringOrNull(request.resource.data.projectId) &&
          isStringOrNull(request.resource.data.recurringId) &&
          request.resource.data.isImportant is bool &&
          request.resource.data.isUrgent is bool &&
          // isTaskTimingValid(request.resource.data) &&
          // 确保 userId 和 createTime 不可变
          request.resource.data.userId == resource.data.userId &&
          request.resource.data.createTime == resource.data.createTime &&
          request.resource.data.updateTime == request.time;
      }

      // ===== 计时 (timers) 子集合 =====
      match /timers/{timerId} {

        allow get, list: if isOwner();

        allow create: if isOwner() &&
          isStringOrNull(request.resource.data.timezone) &&
          request.resource.data.startTime is timestamp &&
          request.resource.data.endTime is timestamp &&
          request.resource.data.isRunning is bool &&
          // 确保 userId 正确
          request.resource.data.userId == userId &&
          request.resource.data.taskId is string &&
          request.resource.data.createTime == request.time &&
          request.resource.data.updateTime == request.time;

        allow update: if isOwner() &&
          isStringOrNull(request.resource.data.timezone) &&
          request.resource.data.startTime is timestamp &&
          request.resource.data.endTime is timestamp &&
          request.resource.data.isRunning is bool &&
          // 确保 userId taskId createTime 不可变
          request.resource.data.userId == resource.data.userId &&
          request.resource.data.taskId == resource.data.taskId &&
          request.resource.data.createTime == resource.data.createTime &&
          request.resource.data.updateTime == request.time;
      }

      // ===== 项目 (projects) 子集合 =====
      match /projects/{projectId} {

        allow get, list: if isOwner();

        allow create: if isOwner() &&
          request.resource.data.name is string &&
          request.resource.data.name.size() > 0 &&
          request.resource.data.name.size() <= 50 &&
          request.resource.data.color is number &&
          request.resource.data.isArchived is bool &&
          // 确保 userId 正确
          request.resource.data.userId == userId &&
          request.resource.data.createTime == request.time &&
          request.resource.data.updateTime == request.time;
        
        allow update: if isOwner() &&
          request.resource.data.name is string &&
          request.resource.data.name.size() > 0 &&
          request.resource.data.name.size() <= 50 &&
          request.resource.data.color is number &&
          request.resource.data.isArchived is bool &&
          // 确保 userId createTime 不可变
          request.resource.data.userId == resource.data.userId &&
          request.resource.data.createTime == resource.data.createTime &&
          request.resource.data.updateTime == request.time;
      }

      // ===== 周期任务 (recurrings) 子集合 =====
      match /recurrings/{recurringId} {

        allow get, list: if isOwner();

        allow create: if isOwner() &&
          request.resource.data.timezone is string &&
          request.resource.data.template is map &&
          request.resource.data.rule is map &&
          // template 字段
          isStringOrNull(request.resource.data.template.projectId) &&
          !('recurringId' in request.resource.data.template) &&
          isTimestampOrNull(request.resource.data.template.dueClock) &&
          request.resource.data.template.isImportant is bool &&
          request.resource.data.template.isUrgent is bool &&
          // rule 字段
          request.resource.data.rule.frequency is string &&
          request.resource.data.rule.interval is number &&
          isMapOrNull(request.resource.data.rule.daysOfWeek) &&
          isMapOrNull(request.resource.data.rule.daysOfMonth) &&
          isMapOrNull(request.resource.data.rule.daysOfYear) &&
          request.resource.data.rule.startTime is timestamp &&
          isTimestampOrNull(request.resource.data.rule.endTime) &&
          isNumberOrNull(request.resource.data.rule.repeatCount) &&
          // 确保 userId 正确
          request.resource.data.userId == userId &&
          request.resource.data.createTime == request.time &&
          request.resource.data.updateTime == request.time;

        allow update: if isOwner() &&
          request.resource.data.timezone is string &&
          request.resource.data.template is map &&
          request.resource.data.rule is map &&
          // template 字段
          isStringOrNull(request.resource.data.template.projectId) &&
          !('recurringId' in request.resource.data.template) &&
          isTimestampOrNull(request.resource.data.template.dueClock) &&
          request.resource.data.template.isImportant is bool &&
          request.resource.data.template.isUrgent is bool &&
          // rule 字段
          request.resource.data.rule.frequency is string &&
          request.resource.data.rule.interval is number &&
          isMapOrNull(request.resource.data.rule.daysOfWeek) &&
          isMapOrNull(request.resource.data.rule.daysOfMonth) &&
          isMapOrNull(request.resource.data.rule.daysOfYear) &&
          request.resource.data.rule.startTime is timestamp &&
          isTimestampOrNull(request.resource.data.rule.endTime) &&
          isNumberOrNull(request.resource.data.rule.repeatCount) &&
          // 确保 userId 和 createTime 不可变
          request.resource.data.userId == resource.data.userId &&
          request.resource.data.createTime == resource.data.createTime &&
          request.resource.data.updateTime == request.time;
      }

      // ===== 设置 (settings) 子集合 =====
      match /settings/{settingId} {

        // 允许读写 settings 文档，如果 settingId 是特定的硬编码 ID
        allow read: if isOwner();

        allow write: if isOwner() &&
          settingId == 'XaB3kPq9m2N5vL6Y7wR4' &&
          validateSettingsFields(request.resource.data);
      }
    }
  }
}

// =====================================
// ========= 自定义函数 =========
// =====================================

function isStringOrNull(field) {
  return (field is string) || (field == null);
}

function isNumberOrNull(field) {
  return (field is number) || (field == null);
}

function isTimestampOrNull(field) {
  return (field is timestamp) || (field == null);
}

function isMapOrNull(field) {
  return (field is map) || (field == null);
}

// 验证任务的时间相关字段 (新模型 - 过渡期宽松版)
// function isTaskTimingValid(data) {
  // 我们只验证新字段的类型，不对它们的组合逻辑做严格限制
  // 以确保数据迁移期间的兼容性。
  // return isStringOrNull(data.dueDate) &&
    // isTimestampOrNull(data.dueTime) &&
    // isStringOrNull(data.timezone) &&
// }

// 验证设置字段
function validateSettingsFields(data) {
  let validKeys = ['navVer', 'statVer', 'navHor', 'statHor', 'weekStartDay', 'showOverdueTasks', 'showCompletedTasks', 'showDroppedTasks'];
  return data.keys().hasOnly(validKeys) &&
    (!('navVer' in data) || data.navVer is list) &&
    (!('statVer' in data) || data.statVer is list) &&
    (!('navHor' in data) || data.navHor is list) &&
    (!('statHor' in data) || data.statHor is list) &&
    (!('weekStartDay' in data) || data.weekStartDay is number) &&
    (!('showOverdueTasks' in data) || data.showOverdueTasks is bool) &&
    (!('showCompletedTasks' in data) || data.showCompletedTasks is bool) &&
    (!('showDroppedTasks' in data) || data.showDroppedTasks is bool);
}
