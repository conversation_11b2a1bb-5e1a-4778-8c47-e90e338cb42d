// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
// models
import '../../models/nav_model.dart';
// states
import '../../states/nav_state.dart';
// tools
import '../../tools/config.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class NavSettingPage extends StatelessWidget {
  const NavSettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.navSettingPage_title),
      ),
      body: Consumer<NavState>(
        builder: (context, navState, _) {
          final selectedNavs = navState.selectedNavs;
          final selectableNavs = navState.selectableNavs;

          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.navSettingPage_enabledNavs,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                ),

                if (selectedNavs.isEmpty)
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Center(
                      child: Text(
                        l10n.navSettingPage_emptyEnabledNavs,
                        style: TextStyle(
                          color: colorScheme.onSurfaceVariant,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ),
                  )
                else
                  ReorderableListView(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    onReorder: (oldIndex, newIndex) {
                      // 增加震动反馈
                      HapticFeedback.lightImpact();
                      navState.reorder(oldIndex, newIndex);
                    },
                    children: [
                      for (int i = 0; i < selectedNavs.length; i++)
                        _buildSelectedNav(
                          context,
                          i,
                          selectedNavs[i],
                          navState,
                        ),
                    ],
                  ),

                const Divider(height: 32),

                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Text(
                    l10n.navSettingPage_disabledNavs,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                ),

                _buildSelectableCategorySection(
                  context,
                  l10n.common_categoryTask,
                  NavCategory.task,
                  selectableNavs,
                  navState,
                ),
                _buildSelectableCategorySection(
                  context,
                  l10n.common_categoryTimer,
                  NavCategory.timer,
                  selectableNavs,
                  navState,
                ),
                _buildSelectableCategorySection(
                  context,
                  l10n.common_categoryProject,
                  NavCategory.project,
                  selectableNavs,
                  navState,
                ),
                _buildSelectableCategorySection(
                  context,
                  l10n.common_categoryFunction,
                  NavCategory.function,
                  selectableNavs,
                  navState,
                ),
                SizedBox(height: Config.app.bottomSpace),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCategoryHeader(BuildContext context, String title) {
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 4),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: colorScheme.primary.withValues(alpha: 0.8),
        ),
      ),
    );
  }

  Widget _buildSelectedNav(
    BuildContext context,
    int index,
    NavModel nav,
    NavState navState,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    return ListTile(
      key: ValueKey('selected-${nav.id}-$index'),
      leading: IconButton(
        icon: const Icon(LucideIcons.circleMinus),
        color: colorScheme.error,
        visualDensity: VisualDensity.compact,
        onPressed: () {
          // 增加震动反馈
          HapticFeedback.lightImpact();
          navState.remove(index);
        },
      ),
      title: Text(nav.name, style: TextStyle(fontSize: 14)),
      subtitle: Text(nav.description, style: TextStyle(fontSize: 12)),
      trailing: Icon(LucideIcons.equal),
    );
  }

  Widget _buildSelectableNav(
    BuildContext context,
    NavModel nav,
    NavState navState,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final canAdd = navState.canAddMoreNavs;
    Widget? trailing;

    if (nav.isProject) {
      trailing = Container(
        width: 18,
        height: 18,
        decoration: BoxDecoration(
          color: nav.color,
          borderRadius: BorderRadius.circular(2),
        ),
      );
    } else {
      trailing = Icon(nav.icon);
    }

    return ListTile(
      key: ValueKey('selectable-${nav.id}'),
      leading: IconButton(
        icon: const Icon(LucideIcons.circlePlus),
        color: canAdd ? colorScheme.primary : colorScheme.onSurfaceVariant,
        visualDensity: VisualDensity.compact,
        onPressed: () {
          // 增加震动反馈
          HapticFeedback.lightImpact();
          if (canAdd) {
            navState.add(navState.selectedNavs.length, nav);
          }
        },
      ),
      title: Text(nav.name, style: TextStyle(fontSize: 14)),
      subtitle: Text(nav.description, style: TextStyle(fontSize: 12)),
      trailing: trailing,
    );
  }

  Widget _buildSelectableCategorySection(
    BuildContext context,
    String title,
    NavCategory category,
    List<NavModel> selectableNavs,
    NavState navState,
  ) {
    final itemsToShow = selectableNavs
        .where((nav) => nav.category == category && !nav.isFixed)
        .toList();

    if (itemsToShow.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildCategoryHeader(context, title),
        for (final nav in itemsToShow)
          _buildSelectableNav(
            context,
            nav,
            navState,
          ),
      ],
    );
  }
}
