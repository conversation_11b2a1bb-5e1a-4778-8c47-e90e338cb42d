// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// states
import '../states/task_state.dart';
// widgets
import '../widgets/task/date_grouped_tasks.dart';
import '../widgets/common/app_header_bar.dart';
// others
import '../generated/l10n/app_localizations.dart';

class TodoView extends StatelessWidget {
  const TodoView({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer<TaskState>(
      builder: (context, taskState, _) {
        // 获取所有待办任务 (未结束未逾期)
        final todoTasks = taskState.getTodoTasks();
        
        return Scaffold(
          appBar: AppHeaderBar(
            title: Text(l10n.todoView_title),
          ),
          body: CustomScrollView(
            slivers: [
              // 待办任务列表
              if (todoTasks.isEmpty)
                SliverFillRemaining(
                  hasScrollBody: false,
                  child: Center(child: Text(l10n.todoView_emptyMessage)),
                )
              else
                DateGroupedTasks(tasks: todoTasks),
            ],
          ),
        );
      },
    );
  }
}
