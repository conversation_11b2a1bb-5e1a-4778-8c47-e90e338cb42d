{"buildFiles": ["C:\\Users\\<USER>\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Yechi\\Taskive\\mobile\\android\\app\\.cxx\\Debug\\724t2am2\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Yechi\\Taskive\\mobile\\android\\app\\.cxx\\Debug\\724t2am2\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}