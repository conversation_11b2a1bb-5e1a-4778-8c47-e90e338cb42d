// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
// states
import '../../states/setting_state.dart';
import '../../states/task_state.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TaskDisplaySettingPage extends StatelessWidget {
  const TaskDisplaySettingPage({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.taskDisplaySettingPage_title),
      ),
      body: Consumer<SettingState>(
        builder: (context, settingState, _) {
          final showCompletedTasks = settingState.showCompletedTasks;
          final showDroppedTasks = settingState.showDroppedTasks;
          final showOverdueTasks = settingState.showOverdueTasks;
          
          // 获取 TaskState 实例，但不监听它
          final taskState = Provider.of<TaskState>(context, listen: false);

          return ListView(
            children: [
              SwitchListTile(
                title: Text(l10n.taskDisplaySettingPage_showCompleted),
                subtitle: Text(l10n.taskDisplaySettingPage_showCompletedDesc),
                value: showCompletedTasks,
                visualDensity: VisualDensity.compact,
                onChanged: (value) {
                  // 增加震动反馈
                  HapticFeedback.lightImpact();
                  settingState.setShowCompletedTasks(value).then((_) {
                    // 调用 TaskState 中的公共方法来刷新列表
                    taskState.refreshTasks(); 
                  });
                },
              ),
              
              const Divider(),
              
              SwitchListTile(
                title: Text(l10n.taskDisplaySettingPage_showOverdue),
                subtitle: Text(l10n.taskDisplaySettingPage_showOverdueDesc),
                value: showOverdueTasks,
                visualDensity: VisualDensity.compact,
                onChanged: (value) {
                  // 增加震动反馈
                  HapticFeedback.lightImpact();
                  settingState.setShowOverdueTasks(value).then((_) {
                    // 调用 TaskState 中的公共方法来刷新列表
                    taskState.refreshTasks();
                  });
                },
              ),

              const Divider(),

              SwitchListTile(
                title: Text(l10n.taskDisplaySettingPage_showDropped),
                subtitle: Text(l10n.taskDisplaySettingPage_showDroppedDesc),
                value: showDroppedTasks,
                visualDensity: VisualDensity.compact,
                onChanged: (value) {
                  // 增加震动反馈
                  HapticFeedback.lightImpact();
                  settingState.setShowDroppedTasks(value).then((_) {
                    // 调用 TaskState 中的公共方法来刷新列表
                    taskState.refreshTasks();
                  });
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
