// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
// models
import '../../models/task_model.dart';
import '../../models/recurring_model.dart';
// states
import '../../states/recurring_state.dart';
import '../../states/task_state.dart';
// tools
import '../../tools/extensions.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class HabitTaskChart extends StatelessWidget {
  final String recurringId;

  const HabitTaskChart({
    super.key,
    required this.recurringId,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final DateTime currentMonth = DateTime(DateTime.now().year, DateTime.now().month);
    final int daysInMonth = DateUtils.getDaysInMonth(currentMonth.year, currentMonth.month);
    final monthFormatter = DateFormat.yMMMM(l10n.localeName);
    final monthTitle = monthFormatter.format(currentMonth);

    return Consumer2<RecurringState, TaskState>(
      builder: (context, recurringState, taskState, _) {
        RecurringModel? habit;
        try {
          habit = recurringState.recurrings.firstWhere((r) => r.id == recurringId);
        } catch (e) {
          habit = null;
        }

        if (habit == null) {
          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Center(child: Text(l10n.habitTaskChart_error)),
            ),
          );
        }

        final List<TaskModel> habitTasks = taskState.tasks
            .where((task) => task.recurringId == recurringId)
            .toList();

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        l10n.statModel_habitTaskName(habit.template.name),
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontSize: 13,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Text(
                      monthTitle,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: daysInMonth,
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 10,
                    crossAxisSpacing: 6,
                    mainAxisSpacing: 6,
                    childAspectRatio: 1,
                  ),
                  itemBuilder: (context, index) {
                    final day = index + 1;
                    final dateForDay = DateTime(currentMonth.year, currentMonth.month, day);
                    Color color = colorScheme.surfaceContainer;

                    TaskModel? taskForDay;
                    try {
                      taskForDay = habitTasks.firstWhere(
                        (task) {
                          final taskDate = task.deadlineTime;
                          return taskDate != null && taskDate.isSameDay(dateForDay);
                        },
                      );
                    } catch (e) {
                      taskForDay = null;
                    }

                    if (taskForDay != null) {
                       if (taskForDay.isCompleted) {
                         color = Colors.green;
                       } else if (taskForDay.isOverdue) {
                         color = colorScheme.error;
                       } else if (!taskForDay.isDropped) {
                         color = colorScheme.primary;
                       } else {
                         color = colorScheme.onSurfaceVariant.withValues(alpha: 0.3);
                       }
                    }

                    final textColor = color.computeLuminance() > 0.5 
                      ? colorScheme.onSurface.withValues(alpha: 0.8)
                      : Colors.white.withValues(alpha: 0.8);

                    return Container(
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: Center(
                        child: Text(
                          day.toString(),
                          style: TextStyle(
                            fontSize: 10,
                            color: textColor,
                          ),
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 4),
              ],
            ),
          ),
        );
      }
    );
  }
}
