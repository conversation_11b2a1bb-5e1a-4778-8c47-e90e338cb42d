// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// states
import '../../states/task_state.dart';
import '../../states/timer_state.dart';
// tools
import '../../tools/extensions.dart';
// views
import '../../views/overdue_view.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TodayGlance extends StatelessWidget {
  const TodayGlance({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Consumer2<TaskState, TimerNotifier>(
      builder: (context, taskState, timerNotifier, _) {
        // 从 TimerState 获取今日总时长
        final todayTotalDuration = timerNotifier.state.getTodayTotalTimerDuration();
        final formattedDuration = todayTotalDuration.format();

        final todayTodoCount = taskState.getTodayTodoCount();
        final overdueCount = taskState.getOverdueTasksCount();

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
          child: LayoutBuilder(
            builder: (context, constraints) {
              final availableWidth = constraints.maxWidth;
              final cardWidth = availableWidth / 3 - 8;

              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildStatCard(
                    context,
                    title: l10n.todayGlanceWidget_todo,
                    value: todayTodoCount.toString(),
                    width: cardWidth,
                  ),
                  _buildStatCard(
                    context,
                    title: l10n.todayGlanceWidget_timer,
                    value: formattedDuration,
                    width: cardWidth,
                  ),
                  GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(builder: (context) => const OverdueView()),
                      );
                    },
                    child: _buildStatCard(
                      context,
                      title: l10n.todayGlanceWidget_overdue,
                      value: overdueCount.toString(),
                      width: cardWidth,
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required String value,
    required double width,
  }) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      child: Container(
        width: width,
        padding: const EdgeInsets.all(6),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 数据放在上面
            Text(
              value, // 显示 String value
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              maxLines: 1, // 确保单行显示
              overflow: TextOverflow.ellipsis, // 超出部分显示省略号
            ),
            const SizedBox(height: 4),
            // 标题放在下面
            Text(
              title,
              style: TextStyle(
                fontSize: 11,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
