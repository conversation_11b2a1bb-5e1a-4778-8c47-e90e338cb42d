import 'dart:developer' as developer;
import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:cloud_functions/cloud_functions.dart';
// models
import '../models/recurring_model.dart';
// tools
import '../tools/config.dart';

class RecurringService {
  final FirebaseFirestore _firestore;
  final FirebaseFunctions _functions;

  /// ====== 私有状态 ======

  // Firestore Collection 常量
  final String _usersCollection = Config.service.usersCollection;
  final String _recurringsCollection = Config.service.recurringsCollection;

  /// ====== 构造函数 ======
  
  RecurringService(this._firestore, this._functions);

  /// ====== 流监听 ======

  /// 获取周期任务的实时流
  Stream<List<RecurringModel>> getRecurringStream(String userId) {
    if (userId.isEmpty) return Stream.value([]);

    final collectionRef = _getRecurringCollectionRef(userId);

    // 按创建时间降序排列
    return collectionRef.orderBy('createTime', descending: true).snapshots().map((snapshot) {
      if (snapshot.docs.isEmpty) return [];
      // 将 Firestore 文档映射回 RecurringModel 列表
      return snapshot.docs.map((doc) {
        try {
          final data = doc.data();
          // 手动添加文档 ID
          data['id'] = doc.id;
          return RecurringModel.fromDatabase(data);
        } catch (e) {
          developer.log('⛔ 解析周期任务文档 ${doc.id} 失败', error: e);
          return null;
        }
      }).whereType<RecurringModel>().toList();
    });
  }

  /// ====== 网络请求 ======

  /// 创建新周期任务（客户端直接写入以提高速度）
  Future<String?> createRecurring(String userId, RecurringModel recurring) async {
    final collectionRef = _getRecurringCollectionRef(userId);
    final data = recurring.toDatabase();

    // 添加系统字段
    data['userId'] = userId;
    data['createTime'] = FieldValue.serverTimestamp();
    data['updateTime'] = FieldValue.serverTimestamp();

    final docRef = await collectionRef.add(data);
    return docRef.id;
  }

  /// 更新周期任务（客户端直接写入以提高速度）
  Future<void> updateRecurring(String userId, RecurringModel recurring) async {
    final docRef = _getRecurringCollectionRef(userId).doc(recurring.id);
    final data = recurring.toDatabase();

    // 添加系统字段
    data['updateTime'] = FieldValue.serverTimestamp();

    await docRef.update(data);
  }

  /// 删除周期任务及其未来的任务实例（保留在云函数以保证事务性）
  Future<void> deleteRecurring(String recurringId) async {
    final callable = _functions.httpsCallable(Config.service.deleteRecurringFn);
      
    await callable.call({'recurringId': recurringId});
  }

  /// ====== 工具方法 ======
  
  // 获取用户周期任务集合的引用
  CollectionReference<Map<String, dynamic>> _getRecurringCollectionRef(String userId) {
    return _firestore.collection(_usersCollection).doc(userId).collection(_recurringsCollection);
  }
}
