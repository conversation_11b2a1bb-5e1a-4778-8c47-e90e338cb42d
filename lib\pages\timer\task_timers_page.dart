// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
// models
import '../../models/task_model.dart';
import '../../models/timer_model.dart';
// states
import '../../states/task_state.dart';
import '../../states/timer_state.dart';
// widgets
import '../../widgets/task/task_card.dart';
import '../../widgets/timer/mini_timer_card.dart';
// others
import '../../generated/l10n/app_localizations.dart';

class TaskTimersPage extends StatelessWidget {
  final String taskId;

  const TaskTimersPage({
    super.key,
    required this.taskId,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return MultiProvider(
      // 同时监听 TaskState 和 TimerState
      providers: [
        ChangeNotifierProvider.value(value: Provider.of<TaskState>(context)),
        ChangeNotifierProvider.value(value: Provider.of<TimerNotifier>(context)),
      ],
      child: Scaffold(
        appBar: AppBar(
          title: Text(l10n.taskTimersPage_title),
        ),
        body: Consumer2<TaskState, TimerNotifier>( // 使用 Consumer2 获取两个状态
          builder: (context, taskState, timerNotifier, child) {
            final TaskModel? task = taskState.getTaskById(taskId);
            final List<TimerModel> timers = timerNotifier.state.getTimersForTask(taskId);

            if (task == null) {
              // 如果任务找不到，显示提示信息
              return Center(child: Text(l10n.taskTimersPage_taskNotFound));
            }

            return SingleChildScrollView( // 使用 SingleChildScrollView 允许滚动
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // 显示任务卡片
                  TaskCard(task: task),

                  const SizedBox(height: 2.0),

                  // 显示计时列表
                  if (timers.isEmpty)
                    Center(child: Text(l10n.taskTimersPage_noTimers))
                  else
                    ListView.builder(
                      shrinkWrap: true, // 在 Column 中需要设置 shrinkWrap
                      physics: const NeverScrollableScrollPhysics(), // 禁用内部滚动
                      itemCount: timers.length,
                      itemBuilder: (context, index) {
                        // 在这里重新添加 Align 组件
                        return Align(
                          alignment: Alignment.centerLeft,
                          child: MiniTimerCard(timer: timers[index]),
                        );
                      },
                    ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
