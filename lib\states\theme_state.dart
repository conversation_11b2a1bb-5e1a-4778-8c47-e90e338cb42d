// import 'dart:developer' as developer;
import 'package:flutter/material.dart';
// models
import '../models/theme_model.dart';
// services
import '../services/setting_service.dart';

class ThemeState with ChangeNotifier {
  final SettingService _settingService;

  /// ====== 私有状态 ======
  
  // 从模型获取默认主题
  AppTheme _currentTheme = AppThemes.defaultTheme;

  /// ====== 构造函数 ======

  ThemeState(this._settingService) {
    _loadTheme();
  }
  
  /// ====== 公共状态 ======

  AppTheme get currentTheme => _currentTheme;

  /// ====== 网络请求 (本地IO) ======
  
  // 从本地存储加载主题
  Future<void> _loadTheme() async {
    // 如果没有存储过，则使用默认主题的 name
    final themeName = await _settingService.getLocalStorageThemeCode() ?? AppThemes.defaultTheme.name;
    
    // 通过 name 找到对应的枚举值
    _currentTheme = AppTheme.values.firstWhere(
      (theme) => theme.name == themeName,
      orElse: () => AppThemes.defaultTheme,
    );
    
    // 异步加载完成后，通知监听者更新UI
    notifyListeners();
  }

  // 外部调用此方法来切换主题
  void setTheme(AppTheme theme) {
    if (_currentTheme == theme) return; // 如果主题未变，则不执行任何操作

    _currentTheme = theme;
    _settingService.setLocalStorageThemeCode(theme.name);
    
    // 通知所有监听者，UI 需要重建以应用新主题
    notifyListeners();
  }
}
